package com.mlc.ai.service.workflow.task.logic;

import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.logic.AbstractDagNodeLogic;
import com.mlc.ai.service.MlcAiServiceConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 澄清问题生成器逻辑
 * 从执行上下文获取澄清问题列表，并进行初始化
 */
@Slf4j
public class QuestionGeneratorLogic extends AbstractDagNodeLogic {

    /**
     * 澄清问题列表
     */
    private List<ClarificationQuestionNode> clarifyingQuestions;

    /**
     * 构造函数
     */
    public QuestionGeneratorLogic() {
        super(QuestionGeneratorLogic.class.getSimpleName());
    }

    /**
     * 初始化澄清问题生成器
     * 从执行上下文获取澄清问题列表
     */
    @Override
    public Map<String, Object> initialize(IDagDataContext context) {
        // 调用父类初始化
        Map<String, Object> initResult = super.initialize(context);
        
        // 从执行上下文获取澄清问题列表
        this.clarifyingQuestions = context.getExecutionContext().getAttribute(MlcAiServiceConstants.CLARIFYING_QUESTIONS);

        String nodeId = getCurrentNodeId(context);
        if (clarifyingQuestions == null || clarifyingQuestions.isEmpty()) {
            log.info("[{}] 没有澄清问题需要处理", nodeId);
            this.clarifyingQuestions = List.of();
        } else {
            log.info("[{}] 初始化澄清问题生成器，共 {} 个问题", nodeId, clarifyingQuestions.size());

            // 将问题列表添加到初始化结果中
            initResult.put("generated_questions", clarifyingQuestions);
        }
        
        return initResult;
    }

    /**
     * 执行澄清问题生成
     * 因为问题已在初始化阶段准备好，这里只需返回结果
     * 这里的执行逻辑可以根据需要进行扩展或修改
     */
    @Override
    public Map<String, Object> execute(IDagDataContext context) {
        Map<String, Object> result = new HashMap<>();
        
        // 将准备好的问题列表放入结果
        result.put("generated_questions", this.clarifyingQuestions);

        String nodeId = getCurrentNodeId(context);
        log.info("[{}] 澄清问题生成完成，共 {} 个问题", nodeId, clarifyingQuestions.size());
        return result;
    }
} 