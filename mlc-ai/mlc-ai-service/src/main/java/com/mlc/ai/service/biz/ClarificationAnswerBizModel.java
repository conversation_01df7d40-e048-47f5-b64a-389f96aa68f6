package com.mlc.ai.service.biz;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.util.QAPair;
import com.mlc.ai.service.workflow.clarification.ClarificationCycleHandler;
import com.mlc.ai.service.workflow.StructuredRequirementsWorkflow;
import com.mlc.ai.prompt.store.cache.DagGraphCacheManager;
import com.mlc.ai.task.context.ExecutionContext;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

/**
 * 澄清问题回答处理控制器
 * 专门处理基于 ComposeTaskNode 的函数式澄清循环节点
 */
@Slf4j
@RestController
@RequestMapping("/clarification")
public class ClarificationAnswerBizModel {

    /**
     * 处理澄清循环节点的批量回答
     *
     * @param cycleRequest 批量回答请求
     * @return 处理结果响应
     */
    @PostMapping(value = "/loopAnswers", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> processCycleAnswers(@RequestBody @Valid CycleBatchAnswerRequest cycleRequest) {

        String cycleNodeId = cycleRequest.getQaLoopId();
        String executionId = cycleRequest.getExecutionId();
        log.info("[{}] 处理循环节点 {} 的批量回答，包含 {} 个回答",
            executionId, cycleNodeId, cycleRequest.getAnswers().size());

        try {
            DagGraph dagGraph = getDagGraphFromCache(executionId);
            ClarificationCycleHandler cycleHandler = new ClarificationCycleHandler(dagGraph);

            Map<String, String> answers =
                cycleRequest.getAnswers().stream().collect(Collectors.toMap(AnswerRequest::getQuestionId, AnswerRequest::getAnswer));

            ExecutionContext executionContext = ExecutionContext.getInstance();
            executionContext.setExecutionId(executionId);

            // 批量处理回答
            boolean success = cycleHandler.processBatchAnswersToCycle(cycleNodeId, executionContext, answers);
            
            // 检查处理是否成功
            if (!success) {
                log.error("[{}] 循环节点 {} 的回答处理失败", executionId, cycleNodeId);
                return Flux.error(new RuntimeException("回答处理失败，请检查输入数据"));
            }

            // 检查是否所有问题都已回答
            boolean completed = cycleHandler.isCycleCompleted(cycleNodeId);
            if (!completed) {
                List<QAPair> pendingQuestions = cycleHandler.getPendingQuestions(cycleNodeId);
                log.error("[{}] 循环节点 {} 仍有 {} 个未回答问题", executionId, cycleNodeId, pendingQuestions.size());
                return Flux.error(new RuntimeException("所有问题必须全部回答完成，还有 " + pendingQuestions.size() + " 个问题未回答"));
            }
            
            // 存储处理结果
            storeDagGraphToCache(executionId, dagGraph);

            // 获取汇总问答对
            List<QAPair> consolidatedQAPairs = cycleHandler.getConsolidatedQAPairs(cycleNodeId);

            // 构建流式响应 - 只有在所有问题都成功回答的情况下才到达这里
            return Flux.<String>create(sink -> {
                // 发送处理状态
                sink.next("✅ 所有回答处理成功 \n");

                // 发送汇总问答对
                sink.next("📚 共有 " + consolidatedQAPairs.size() + " 个汇总问答对 \n");

                // 记录日志
                log.info("[{}] 循环节点 {} 已完成，返回 {} 个汇总问答对", executionId, cycleNodeId, consolidatedQAPairs.size());
                
                sink.complete();
            }).concatWith(
                // 启动结构化需求分析工作流
                startStructuredRequirementsWorkflow(executionContext, cycleNodeId)
            );
            
        } catch (Exception e) {
            log.error("[{}] 处理循环节点 {} 时发生异常", executionId, cycleNodeId, e);
            return Flux.error(new RuntimeException("处理过程中发生异常: " + e.getMessage(), e));
        }
    }

    /**
     * 启动结构化需求分析工作流
     * 
     * @param qaLoopId 澄清循环节点的ID
     * @return 结构化需求分析工作流的执行结果流
     */
    private Flux<String> startStructuredRequirementsWorkflow(ExecutionContext executionContext, String qaLoopId) {
        log.info("[{}] 启动结构化需求分析工作流，针对澄清循环节点ID: {}", executionContext.getExecutionId(), qaLoopId);
        
        try {
            // 创建结构化需求工作流
            StructuredRequirementsWorkflow structuredWorkflow = new StructuredRequirementsWorkflow();
            
            // 执行结构化需求分析工作流
            return structuredWorkflow.executeStructuredRequirementsAnalysis(executionContext, qaLoopId);
            
        } catch (Exception e) {
            log.error("[{}] 启动结构化需求分析工作流失败", executionContext.getExecutionId(), e);
            return Flux.error(new RuntimeException("启动结构化需求分析工作流失败: " + e.getMessage(), e));
        }
    }

    // ==================== cache方法 ====================

    /**
     * 从缓存获取DagGraph实例
     */
    private DagGraph getDagGraphFromCache(String executionId) {
        DagGraph dagGraph = DagGraphCacheManager.INSTANCE.getDagGraph(executionId);
        if (dagGraph == null) {
            log.error("[{}] 无法获取DagGraph实例", executionId);
            throw new IllegalStateException("无法获取DagGraph实例: " + executionId);
        }
        return dagGraph;
    }

    /**
     * 存储DagGraph到缓存
     */
    private void storeDagGraphToCache(String executionId, DagGraph dagGraph) {
        DagGraphCacheManager.INSTANCE.putDagGraph(executionId, dagGraph);
        log.debug("[{}] DagGraph已更新到缓存", executionId);
    }

    // ==================== 数据对象 ====================

    /**
     * 循环节点批量回答请求
     */
    @Getter
    @Setter
    public static class CycleBatchAnswerRequest {
        @NotBlank(message = "执行ID不能为空")
        private String executionId;

        @NotBlank(message = "问答节点ID不能为空")
        private String qaLoopId;

        @Valid
        private List<AnswerRequest> answers;
    }

    @Getter
    @Setter
    public static class AnswerRequest {
        @NotBlank(message = "问题ID不能为空")
        private String questionId;
        @NotBlank(message = "回答不能为空")
        private String answer;
    }
}

