package com.mlc.ai.service.workflow.task.logic;

import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.logic.AbstractDagNodeLogic;
import com.mlc.ai.prompt.store.dag.util.QAPair;
import com.mlc.ai.service.workflow.clarification.ClarificationLoopState;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 澄清循环节点逻辑
 * 管理澄清问题循环的状态和流程
 */
@Getter
@Slf4j
public class ClarificationCycleLogic extends AbstractDagNodeLogic {

    /**
     * 澄清循环状态
     */
    private ClarificationLoopState cycleState;

    /**
     * 构造函数
     */
    public ClarificationCycleLogic() {
        super(ClarificationCycleLogic.class.getSimpleName());
    }

    /**
     * 初始化澄清循环
     * 从上游节点获取澄清问题，并初始化循环状态
     */
    @Override
    public Map<String, Object> initialize(IDagDataContext context) {
        // 调用父类初始化
        Map<String, Object> initResult = super.initialize(context);
        
        String nodeId = super.getCurrentNodeId(context);
        try {
            // 从上游节点获取澄清问题列表
            Optional<Object> generatedQuestions = context.getInputData("generated_questions");
            if (generatedQuestions.isEmpty() || !(generatedQuestions.get() instanceof List<?>)) {
                log.error("[{}] 初始化澄清循环失败，未找到生成的问题列表或格式不正确", nodeId);
                throw new IllegalArgumentException("生成的问题列表不能为空或格式不正确");
            }

            @SuppressWarnings("unchecked")
            List<ClarificationQuestionNode> clarificationQuestions = (List<ClarificationQuestionNode>) generatedQuestions.get();

            // 初始化澄清循环的内部状态
            this.cycleState = new ClarificationLoopState(clarificationQuestions);
            log.info("[{}] 初始化澄清循环状态，包含 {} 个问题", nodeId, clarificationQuestions.size());

        } catch (Exception e) {
            log.error("[{}] 澄清循环初始化失败", nodeId, e);
            throw new RuntimeException("澄清循环初始化失败: " + e.getMessage(), e);
        }
        
        return initResult;
    }

    /**
     * 执行澄清循环逻辑
     */
    @Override
    public Map<String, Object> execute(IDagDataContext context) {
        Map<String, Object> result = new HashMap<>();

        String nodeId = super.getCurrentNodeId(context);
        try {
            if (cycleState == null) {
                log.error("[{}] 澄清循环状态未初始化", nodeId);
                throw new IllegalStateException("澄清循环状态未初始化");
            }
            
            log.info("[{}] 澄清循环逻辑执行 - 当前状态: {}, 进度: {}%",
                nodeId, cycleState.getStatus(), cycleState.getProgressPercentage());
            
            // 当循环完成时，返回汇总的问答对
            if (cycleState.isCompleted()) {
                List<QAPair> consolidatedQAPairs = cycleState.getConsolidatedQAPairs();
                result.put("consolidated_qa_pairs_key", consolidatedQAPairs);
                log.info("[{}] 澄清循环已完成，返回 {} 个汇总问答对", nodeId, consolidatedQAPairs.size());
            } else {
                // 如果循环未完成，返回待回答问题列表
                List<QAPair> pendingQuestions = cycleState.getPendingQuestions();
                result.put("pending_questions", pendingQuestions);
                log.info("[{}] 澄清循环未完成，返回 {} 个待回答问题", nodeId, pendingQuestions.size());
            }
            
            return result;
        } catch (Exception e) {
            log.error("[{}] 澄清循环逻辑执行失败", nodeId, e);
            throw new RuntimeException("澄清循环逻辑执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 完成澄清循环
     */
    @Override
    public Map<String, Object> finish(IDagDataContext context, Map<String, Object> executeResult) {
        // 在这里可以添加完成循环的逻辑，例如记录统计信息等
        if (cycleState != null && cycleState.isCompleted()) {
            log.info("[{}] 澄清循环完成，统计信息: {}", super.getCurrentNodeId(context), cycleState.getStatistics());
        }
        
        return super.finish(context, executeResult);
    }

    /**
     * 批量处理用户回答
     *
     * @param answers 问题ID到回答的映射
     * @return 是否处理成功
     */
    public boolean processBatchAnswers(Map<String, String> answers, String nodeId) {
        if (cycleState == null) {
            log.error("[{}] 澄清循环状态未初始化，无法批量处理用户回答", nodeId);
            return false;
        }
        
        try {
            for (Map.Entry<String, String> entry : answers.entrySet()) {
                cycleState.processUserAnswer(entry.getKey(), entry.getValue());
            }
            
            log.info("[{}] 批量处理用户回答完成，处理 {} 个回答", nodeId, answers.size());
            return true;
        } catch (Exception e) {
            log.error("[{}] 批量处理用户回答失败", nodeId, e);
            return false;
        }
    }
    
    /**
     * 判断循环是否完成
     */
    public boolean isCompleted() {
        return cycleState != null && cycleState.isCompleted();
    }
    
    /**
     * 强制完成循环
     */
    public void forceComplete(String nodeId) {
        if (cycleState != null) {
            cycleState.forceComplete();
            log.warn("[{}] 强制完成澄清循环", nodeId);
        }
    }
} 