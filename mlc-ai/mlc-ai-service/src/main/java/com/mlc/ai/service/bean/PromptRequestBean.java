package com.mlc.ai.service.bean;

import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * Prompt执行请求体
 */
@Getter
@Setter
@AllArgsConstructor
public class PromptRequestBean implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模型类型
     */
    @NotBlank(message = "模型类型不能为空")
    private String modelType;

    /**
     * 用户输入消息
     */
    @NotBlank(message = "消息不能为空")
    private String message;

    /**
     * 上下文数据
     */
    private Map<String, Object> contextData;
}
