package com.mlc.ai.service.biz;

import com.mlc.ai.service.bean.PromptRequestBean;
import com.mlc.ai.service.proxy.PromptAgentProxy;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@Slf4j
@RestController
@RequestMapping("/PrefixPromptBizModel")
public class PrefixPromptBizModel {

    /**
     * 执行用户提供的包含模型定义引用的请求
     *
     * @param promptRequest 请求体，包含用户输入和模型ID
     * @return 执行结果流
     */
    @PostMapping(value = "/executePrompt", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> executePromptStream(@RequestBody @Valid PromptRequestBean promptRequest) {
        if (promptRequest == null) {
            return Flux.error(new IllegalArgumentException("❌ 输入消息不能为空"));
        }

        log.info("收到提示请求: modelType={}, message={}", promptRequest.getModelType(), promptRequest.getMessage());

        try {
            // 调用处理方法
            return PromptAgentProxy.INSTANCE.processRequest(promptRequest);
        } catch (Exception e) {
            log.error("启动提示请求流失败", e);
            return Flux.error(new RuntimeException("❌ 启动提示请求失败: " + e.getMessage(), e));
        }
    }
}
