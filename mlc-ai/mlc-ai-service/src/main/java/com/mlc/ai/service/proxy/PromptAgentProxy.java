/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按"原样"提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.ai.service.proxy;

import com.mlc.ai.dsl.model.AbstractPromptAgentModel;
import com.mlc.ai.dsl.model.orm.OrmPromptAgentModel;
import com.mlc.ai.service.bean.PromptRequestBean;
import io.nop.api.core.auth.IUserContext;
import io.nop.api.core.ioc.BeanContainer;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 负责根据不同领域选择适合的实现
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PromptAgentProxy {

    public static final PromptAgentProxy INSTANCE = new PromptAgentProxy();

    private static final String DOMAIN_ORM = "orm";
    private static final String DOMAIN_META = "meta";  // 准备将来扩展
    
    // 缓存已创建的业务模型实例
    private final Map<String, AbstractPromptAgentModel> modelInstances = new ConcurrentHashMap<>();
    
    // 领域ID到模型类型的映射
    private static final Map<String, Class<? extends AbstractPromptAgentModel>> SPACE_MODEL_MAPPING = new HashMap<>();
    
    static {
        SPACE_MODEL_MAPPING.put("orm", OrmPromptAgentModel.class);
    }

    /**
     * 处理提示请求
     * 
     * @param promptRequest 提示请求
     * @return 处理结果流
     */
    public Flux<String> processRequest(PromptRequestBean promptRequest) {
        // 默认为ORM领域
        String domain = DOMAIN_ORM;

        log.info("处理请求 - 领域: {}, 会话ID: {}", domain, promptRequest.getModelType());
        
        // 获取或创建适当的业务模型实例
        AbstractPromptAgentModel bizModel = this.getModelInstance(domain);
        if (bizModel == null) {
            log.error("无法找到领域 '{}' 的处理模型", domain);
            return Flux.just("不支持的领域类型 '" + domain + "'");
        }

        // 委托给实际的业务模型处理
        String userId = IUserContext.get().getUserId();
        return bizModel.processRequest(userId, promptRequest.getMessage());
    }
    
    /**
     * 获取或创建指定领域的业务模型实例
     */
    private AbstractPromptAgentModel getModelInstance(String spaceId) {
        return modelInstances.computeIfAbsent(spaceId, this::createModelInstance);
    }
    
    /**
     * 创建指定领域的业务模型实例
     */
    private AbstractPromptAgentModel createModelInstance(String domain) {
        Class<? extends AbstractPromptAgentModel> modelClass = SPACE_MODEL_MAPPING.get(domain);
        if (modelClass == null) {
            log.error("未注册领域 '{}' 的模型类", domain);
            return null;
        }
        
        try {
            log.info("创建领域 '{}' 的业务模型实例: {}", domain, modelClass.getSimpleName());
            return BeanContainer.getBeanByType(modelClass);
        } catch (Exception e) {
            log.error("创建领域 '{}' 的业务模型实例失败", domain, e);
            return null;
        }
    }
}
