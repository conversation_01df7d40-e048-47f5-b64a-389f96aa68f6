# DagGraph 工作流系统

## 概述

DagGraph 工作流系统是一个基于任务编排器的智能化需求处理系统，能够自动分析用户的原始需求，构建完整的依赖结构，并将结果存储到 LocalCache 缓存中。

## 系统架构

### 核心组件

1. **DagNodeManagerTaskExecutor** - DAG节点管理任务执行器
   - 负责将任务处理结果存储到DagGraph中
   - 构建完整的上下文问答结构
   - 建立依赖关系：userInput -> 边界分析 -> 意图澄清&推荐

2. **DagGraphCacheManager** - DagGraph缓存管理器
   - 提供DagGraph实例的缓存管理功能
   - 支持按执行ID进行缓存和清理
   - 基于LocalCache实现高性能缓存

3. **RequirementAnalysisWorkflow** - 需求分析工作流
   - 整合所有任务形成完整的处理流程
   - 管理任务间的依赖关系和数据流转

### 依赖结构

```
用户原始请求 (UserRawRequestNode)
    ↓ DERIVATION
边界分析结果 (OutsideBorderNode)
    ↓ IMPLIES
意图澄清&推荐 (ClarificationQuestionNode)
    ↓ CLARIFIES
用户原始请求 (形成闭环)
```

## 核心功能

### 1. 节点创建

#### 用户原始请求节点 (UserRawRequestNode)
- 作为DAG的根节点
- 存储用户的原始输入
- 所有其他节点的起始点

#### 边界外节点 (OutsideBorderNode)
- 存储边界分析的结果
- 包含边界外概念和剩余语句
- 通过DERIVATION关系连接到用户请求节点

#### 澄清问题节点 (ClarificationQuestionNode)
- 存储生成的澄清问题
- 包含问题文本、对齐焦点、推荐选项等
- 通过IMPLIES关系连接到边界外节点
- 通过CLARIFIES关系连接回用户请求节点

### 2. 依赖关系建立

#### DERIVATION (推导关系)
- 用户请求 → 边界外节点
- 表示从用户输入推导出边界分析结果

#### IMPLIES (蕴含关系)
- 边界外节点 → 澄清问题节点
- 表示边界分析结果蕴含了需要澄清的问题

#### CLARIFIES (澄清关系)
- 澄清问题节点 → 用户请求节点
- 表示澄清问题为用户请求提供澄清信息

### 3. 缓存机制

#### LocalCache 实现
```java
private static final ICache<String, AtomicReference<DagGraph>> DAG_GRAPH_CACHE =
    LocalCache.newCache("dag-graph-cache", CacheConfig.newConfig(
        CoreConfigs.CFG_COMPONENT_RESOURCE_CACHE_TENANT_CACHE_CONTAINER_SIZE.get()), 
        s -> new AtomicReference<>());
```

#### 缓存键策略
- 使用执行ID作为缓存键：`dag_graph_{executionId}`
- 确保每个执行上下文有独立的DagGraph实例
- 支持并发访问和线程安全

#### 缓存操作
- **获取或创建**: `DagGraphCacheManager.getOrCreateDagGraph(executionId)`
- **存储**: `DagGraphCacheManager.putDagGraph(executionId, dagGraph)`
- **清理**: `DagGraphCacheManager.clearCache(executionId)`
- **统计**: `DagGraphCacheManager.getCacheStatistics()`

## 使用方法

### 1. 基本使用

```java
// 创建需求分析工作流
RequirementAnalysisWorkflow workflow = new RequirementAnalysisWorkflow();

// 执行需求分析
String userRequest = "设计用户管理系统，包含用户实体和角色实体";
Flux<String> resultStream = workflow.executeRequirementAnalysis(userRequest);

// 订阅结果流
resultStream.subscribe(
    result -> System.out.println(result),
    error -> log.error("执行失败", error),
    () -> log.info("执行完成")
);
```

### 2. 缓存管理

```java
// 获取缓存统计
String statistics = DagGraphCacheManager.getCacheStatistics();

// 检查缓存是否存在
boolean exists = DagGraphCacheManager.containsCache(executionId);

// 清理特定缓存
DagGraphCacheManager.clearCache(executionId);

// 清理所有缓存
DagGraphCacheManager.clearAllCache();
```

### 3. 运行示例

```bash
# 运行DagGraph工作流示例
java -cp target/classes com.mlc.ai.service.example.DagGraphWorkflowExample
```

## 输出结果

### 节点统计报告

```
✅ DAG节点构建完成
📊 节点统计:
  - 用户请求节点: 1
  - 边界外节点: 1
  - 澄清问题节点: 3
  - 总节点数: 5
  - 总边数: 6

🔗 依赖结构:
  用户输入 -> 边界分析 -> 意图澄清&推荐
  ├─ 用户请求 --DERIVATION--> 边界外节点
  ├─ 边界外节点 --IMPLIES--> 澄清问题 [ORM]
  └─ 澄清问题 [ORM] --CLARIFIES--> 用户请求
  ├─ 边界外节点 --IMPLIES--> 澄清问题 [Entity]
  └─ 澄清问题 [Entity] --CLARIFIES--> 用户请求
  ├─ 边界外节点 --IMPLIES--> 澄清问题 [Relation]
  └─ 澄清问题 [Relation] --CLARIFIES--> 用户请求

💾 缓存信息:
  - 缓存大小: 1
  - 存储位置: LocalCache + ExecutionContext
  - 缓存统计: DagGraph缓存统计 - 总键数: 1, 缓存键: [dag_graph_execution-123]
```

## 技术特性

### 1. 高性能缓存
- 基于LocalCache实现，支持高并发访问
- 使用AtomicReference确保线程安全
- 支持缓存大小限制和自动清理

### 2. 完整的依赖结构
- 建立清晰的节点依赖关系
- 支持多种边类型（DERIVATION、IMPLIES、CLARIFIES）
- 形成完整的上下文问答结构

### 3. 灵活的扩展性
- 支持自定义节点类型
- 支持自定义边类型
- 支持动态添加节点和边

### 4. 完善的错误处理
- 异常情况下的优雅降级
- 详细的日志记录
- 资源自动清理

## 配置选项

### 缓存配置
- 缓存大小：通过 `CoreConfigs.CFG_COMPONENT_RESOURCE_CACHE_TENANT_CACHE_CONTAINER_SIZE` 配置
- 缓存策略：支持LRU、FIFO等策略
- 过期时间：可配置缓存项的过期时间

### 任务配置
- 超时时间：可配置任务执行的超时时间
- 重试次数：可配置任务失败时的重试次数
- 并行度：可配置任务的并行执行数量

## 最佳实践

### 1. 缓存管理
- 及时清理不需要的缓存项
- 监控缓存使用情况
- 避免缓存过大导致内存问题

### 2. 依赖关系设计
- 保持依赖关系的清晰性
- 避免循环依赖
- 合理使用不同的边类型

### 3. 错误处理
- 实现完善的异常处理机制
- 提供详细的错误信息
- 支持故障恢复

## 故障排除

### 常见问题

1. **缓存未命中**
   - 检查执行ID是否正确
   - 确认缓存是否已被清理
   - 验证缓存配置是否正确

2. **依赖关系错误**
   - 检查节点创建顺序
   - 验证边类型是否正确
   - 确认节点ID是否唯一

3. **内存使用过高**
   - 检查缓存大小配置
   - 及时清理不需要的缓存项
   - 监控缓存使用情况

### 调试工具

1. **缓存统计**: `DagGraphCacheManager.getCacheStatistics()`
2. **节点遍历**: `dagGraph.getAllNodes()`
3. **边遍历**: `dagGraph.getAllEdges()`
4. **拓扑排序**: `dagGraph.getTopologicalOrder()`

## 版本历史

- **v1.0.0**: 初始版本，支持基本的节点创建和依赖关系建立
- **v1.1.0**: 增加LocalCache缓存机制
- **v1.2.0**: 完善依赖结构，支持完整的工作流程
- **v1.3.0**: 增加缓存管理器和统计功能 