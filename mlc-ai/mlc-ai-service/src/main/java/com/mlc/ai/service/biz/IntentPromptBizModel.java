package com.mlc.ai.service.biz;


import com.mlc.ai.service.bean.PromptRequestBean;
import com.mlc.ai.service.workflow.RequirementAnalysisWorkflow;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@Slf4j
@RestController
@RequestMapping("/NewPromptBizModel")
public class IntentPromptBizModel {

    @PostMapping(value = "/executePrompt", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> executePromptStream(@RequestBody @Valid PromptRequestBean promptRequest) {
        if (promptRequest == null) {
            return Flux.error(new IllegalArgumentException("❌ 输入消息不能为空"));
        }

        log.info("收到提示请求: modelType={}, message={}", promptRequest.getModelType(), promptRequest.getMessage());

        try {
            // 调用处理方法
            RequirementAnalysisWorkflow workflow = new RequirementAnalysisWorkflow();
            Flux<String> stringFlux = workflow.executeRequirementAnalysis(promptRequest.getMessage());
            return stringFlux;
        } catch (Exception e) {
            log.error("启动提示请求流失败", e);
            return Flux.error(new RuntimeException("❌ 启动提示请求失败: " + e.getMessage(), e));
        }
    }
}
