package com.mlc.ai.service.workflow.task;

import com.google.common.base.Strings;
import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import com.mlc.ai.prompt.store.dag.context.DagDataContextImpl;
import com.mlc.ai.prompt.store.dag.node.ComposeTaskNode;
import com.mlc.ai.prompt.store.dag.node.UserRawRequestNode;
import com.mlc.ai.prompt.store.dag.node.OutsideBorderNode;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.service.MlcAiServiceConstants;
import com.mlc.ai.service.workflow.clarification.ClarificationCycleHandler;
import com.mlc.ai.prompt.store.cache.DagGraphCacheManager;
import com.mlc.ai.service.workflow.task.logic.QuestionGeneratorLogic;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.executor.ITaskExecutor;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * DAG节点管理任务执行器
 * 负责将任务处理结果存储到DagGraph中，构建完整的上下文问答结构
 * <p>
 * 重构后的依赖结构：userInput -> 边界分析 -> 澄清问题生成器（基于IDagNodeLogic） -> 澄清循环节点（基于IDagNodeLogic）
 */
@Slf4j
public class DagNodeManagerTaskExecutor implements ITaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
        String executionId = context.getExecutionId();
        log.info("[{}] 开始执行DAG节点管理任务: {}", executionId, task.getName());
        
        // 创建DAG节点并建立依赖结构
        String cycleNodeId = this.createDagNodesWithDependencies(task, context);
        
        // 构建包含 cycleNodeId 的格式化响应
        String formattedResponse = this.buildFormattedResponseWithCycleNodeId(context, cycleNodeId);
        
        return Flux.just(formattedResponse);
    }

    /**
     * 创建DAG节点并建立完整的依赖结构
     * 阶段1: 用户请求解析 -> 阶段2: 边界分析 -> 阶段3: 澄清问题生成器（基于IDagNodeLogic） -> 阶段4: 澄清循环节点（基于IDagNodeLogic）
     * 
     * @return 澄清循环节点ID，如果没有创建则返回null
     */
    private String createDagNodesWithDependencies(BaseTaskModel task, ExecutionContext executionContext) {
        try {
            // 获取或创建DagGraph实例
            DagGraph dagGraph = getDagGraphFromCache(executionContext);
            
            // ==================== 阶段1: 用户请求解析 ====================
            // 1. 创建用户原始请求节点（根节点）
            UserRawRequestNode userRequestNode = createUserRequestNode(executionContext, dagGraph);

            // ==================== 阶段2: 边界分析 ====================
            OutsideBorderNode outsideBorderNode = createOutsideBorderNode(executionContext, dagGraph);

            // ==================== 阶段3: 澄清问题生成器（基于IDagNodeLogic） ====================
            ComposeTaskNode questionGeneratorNode = this.createQuestionGeneratorNode(executionContext, dagGraph);

            // ==================== 阶段4: 澄清循环节点（基于IDagNodeLogic） ====================
            ComposeTaskNode clarificationCycleNode = this.createClarificationQALoopNode(executionContext, dagGraph, questionGeneratorNode);

            // 5. 建立完整的依赖结构 (Provider -> Consumer 模式)
            this.buildDependencyStructure(dagGraph, executionContext,
                                          userRequestNode, outsideBorderNode, questionGeneratorNode, clarificationCycleNode);

            // 6. 存储DagGraph到缓存
            this.storeDagGraphToCache(executionContext, dagGraph);

            // 返回澄清循环节点ID
            return clarificationCycleNode != null ? clarificationCycleNode.getId() : null;

        } catch (Exception e) {
            log.error("[{}] DAG节点构建失败", executionContext.getExecutionId(), e);
            return null;
        }
    }

    /**
     * 构建包含 cycleNodeId 的格式化响应
     */
    private String buildFormattedResponseWithCycleNodeId(ExecutionContext executionContext, String cycleNodeId) {
        List<ClarificationQuestionNode> clarifyingQuestions = executionContext.getAttribute(MlcAiServiceConstants.CLARIFYING_QUESTIONS);
        
        if (clarifyingQuestions == null || clarifyingQuestions.isEmpty()) {
            log.info("[{}] 没有澄清问题，返回空响应", executionContext.getExecutionId());
            return "没有生成澄清问题";
        }
        
        if (cycleNodeId == null) {
            log.warn("[{}] 澄清循环节点ID为空，使用默认响应", executionContext.getExecutionId());
            return String.format("澄清问题生成完成，共生成 %d 个问题，但未创建循环节点", clarifyingQuestions.size());
        }
        
        // 调用 ClarifyingQuestionsTaskExecutor 的格式化方法
        String formattedResponse = ClarifyingQuestionsTaskExecutor.buildFormattedResponse(
            clarifyingQuestions, 
            executionContext.getExecutionId(),
            cycleNodeId
        );
        
        log.info("[{}] 构建格式化响应完成，包含 cycleNodeId: {}", executionContext.getExecutionId(), cycleNodeId);
        return formattedResponse;
    }

    /**
     * 从缓存获取或创建DagGraph实例
     */
    private DagGraph getDagGraphFromCache(ExecutionContext context) {
        String executionId = context.getExecutionId();
        // 从缓存获取或创建新的DagGraph
        return DagGraphCacheManager.INSTANCE.getOrCreateDagGraph(executionId);
    }

    /**
     * 阶段1: 创建用户原始请求节点
     * 作为DAG的起始节点，需要明确标记为已解析状态
     */
    private UserRawRequestNode createUserRequestNode(ExecutionContext context, DagGraph dagGraph) {
        String userInput = context.getAttribute(MlcAiServiceConstants.USER_RAW_REQUEST_CONTEXT);
        if (Strings.isNullOrEmpty(userInput)) {
           throw new RuntimeException("用户原始请求为空");
        }

        UserRawRequestNode userRequestNode = new UserRawRequestNode(userInput);
        dagGraph.addNode(userRequestNode);
        // 手动设置用户请求节点状态为已解析
        dagGraph.updateNodeStatus(userRequestNode.getId(), NodeStatus.RESOLVED);
        
        // 设置输出数据
        userRequestNode.getOutputs().put("original_request_data_key", userInput);

        log.info("[{}] 创建用户原始请求节点: {} (状态: {})",
            context.getExecutionId(), userRequestNode.getId(), userRequestNode.getStatus());
        
        return userRequestNode;
    }

    /**
     * 阶段2: 创建边界外节点
     * 代表边界分析的结果，标识超出当前领域边界的概念
     */
    private OutsideBorderNode createOutsideBorderNode(ExecutionContext context, DagGraph dagGraph) {
        OutsideBorderNode outsideBorderNode = context.getAttribute(MlcAiServiceConstants.BOUNDARY_ANALYSIS_RESULT);
        if (outsideBorderNode == null || outsideBorderNode.getCross().isEmpty()) {
            log.info("[{}] 没有边界外概念，跳过创建边界外节点", context.getExecutionId());
            return null;
        }

        dagGraph.addNode(outsideBorderNode);
        // 边界分析结果已经由前置任务处理完成，标记为已解析
        dagGraph.updateNodeStatus(outsideBorderNode.getId(), NodeStatus.RESOLVED);
        
        // 设置输出数据
        outsideBorderNode.getOutputs().put("boundary_analysis_result", outsideBorderNode);

        log.info("[{}] 创建边界外节点: {} (状态: {}, 边界外概念数: {})",
            context.getExecutionId(), outsideBorderNode.getId(), 
            outsideBorderNode.getStatus(), outsideBorderNode.getCross().size());
        
        return outsideBorderNode;
    }

    /**
     * 阶段3: 创建澄清问题生成器节点（基于IDagNodeLogic）
     * 使用 IDagNodeLogic 接口实现，提供更好的生命周期管理
     * 核心职责：
     * 1. 接收用户原始请求和边界分析结果
     * 2. 生成一系列澄清问题
     * 3. 作为澄清问题的"菜单"，为后续的澄清循环提供输入
     */
    private ComposeTaskNode createQuestionGeneratorNode(ExecutionContext context, DagGraph dagGraph) {
        List<ClarificationQuestionNode> clarifyingQuestions = context.getAttribute(MlcAiServiceConstants.CLARIFYING_QUESTIONS);

        if (clarifyingQuestions == null || clarifyingQuestions.isEmpty()) {
            log.info("[{}] 没有澄清问题，跳过创建澄清问题生成器节点", context.getExecutionId());
            return null;
        }

        // 创建基于生命周期的节点
        ComposeTaskNode generatorNode = new ComposeTaskNode(
            "ClarificationQuestionGenerator", // 节点名称
            new QuestionGeneratorLogic(),     //创建澄清问题生成器逻辑
            List.of(),                        // 声明输入数据键
            "generated_questions"             // 声明输出数据键
        );

        dagGraph.addNode(generatorNode);
        
        // 创建上下文用于节点初始化
        IDagDataContext dataContext = new DagDataContextImpl(context, dagGraph, generatorNode);

        // 初始化节点 - 这是生命周期的重要环节，确保数据准备好
        generatorNode.initialize(dataContext);

        log.info("[{}] 创建澄清问题生成器节点: {} (状态: {})",
            context.getExecutionId(), generatorNode.getId(), generatorNode.getStatus());
        
        return generatorNode;
    }

    /**
     * 阶段4: 创建澄清循环节点（基于IDagNodeLogic）
     * 使用 ClarificationCycleHandler 创建基于 IDagNodeLogic 的澄清循环节点
     */
    private ComposeTaskNode createClarificationQALoopNode(ExecutionContext context, DagGraph dagGraph,
                                                        ComposeTaskNode questionGeneratorNode) {
        if (questionGeneratorNode == null) {
            log.info("[{}] 澄清问题生成器节点为空，跳过创建澄清循环节点", context.getExecutionId());
            return null;
        }

        List<ClarificationQuestionNode> clarifyingQuestions = context.getAttribute(MlcAiServiceConstants.CLARIFYING_QUESTIONS);
        if (clarifyingQuestions == null || clarifyingQuestions.isEmpty()) {
            log.info("[{}] 没有澄清问题，跳过创建澄清循环节点", context.getExecutionId());
            return null;
        }

        // 使用 ClarificationCycleHandler 创建澄清循环节点
        ClarificationCycleHandler cycleHandler = new ClarificationCycleHandler(dagGraph);
        ComposeTaskNode clarificationCycleNode = cycleHandler.createClarificationQALoopNode(questionGeneratorNode.getId(), clarifyingQuestions);

        if (clarificationCycleNode != null) {
            // 创建上下文用于节点初始化
            IDagDataContext dataContext = new DagDataContextImpl(context, dagGraph, clarificationCycleNode);

            // 初始化节点 - 这是生命周期的重要环节，确保循环状态准备好
            clarificationCycleNode.initialize(dataContext);

            log.info("[{}] 创建澄清循环节点: {} (状态: {})",
                context.getExecutionId(), clarificationCycleNode.getId(), clarificationCycleNode.getStatus());
        }

        return clarificationCycleNode;
    }

    /**
     * 建立完整的依赖结构
     * 依赖路径：userInput --PROVIDES--> 边界分析 --PROVIDES--> 澄清问题生成器 --PROVIDES--> 澄清循环节点
     */
    private void buildDependencyStructure(DagGraph dagGraph, ExecutionContext executionContext, UserRawRequestNode userRequestNode,
              OutsideBorderNode outsideBorderNode, ComposeTaskNode questionGeneratorNode, ComposeTaskNode clarificationCycleNode) {
        
        if (userRequestNode == null) {
            log.warn("用户请求节点为空，无法建立依赖结构");
            return;
        }

        // ==================== 依赖关系1: 用户输入 -> 边界分析 ====================
        if (outsideBorderNode != null) {
            dagGraph.addEdge(userRequestNode, outsideBorderNode, EdgeType.PROVIDES);
            log.info("[{}] 建立依赖关系: 用户请求 --PROVIDES--> 边界外节点", userRequestNode.getId());
        }

        // ==================== 依赖关系2: 边界分析 -> 澄清问题生成器 ====================
        if (questionGeneratorNode != null) {
            if (outsideBorderNode != null) {
                dagGraph.addEdge(outsideBorderNode, questionGeneratorNode, EdgeType.PROVIDES);
                log.info("[{}] 建立依赖关系: 边界外节点 --PROVIDES--> 澄清问题生成器节点", outsideBorderNode.getId());
            } else {
                // 如果没有边界外节点，直接从用户请求推导澄清问题生成器
                dagGraph.addEdge(userRequestNode, questionGeneratorNode, EdgeType.PROVIDES);
                log.info("[{}] 建立依赖关系: 用户请求 --PROVIDES--> 澄清问题生成器节点 (无边界节点)", userRequestNode.getId());
            }
            
            // 执行澄清问题生成器节点 - 此时已经完成初始化，只需执行
            this.executeNodeLogic(questionGeneratorNode, executionContext, dagGraph);
        }

        // ==================== 依赖关系3: 澄清问题生成器 -> 澄清循环节点 ====================
        if (clarificationCycleNode != null && questionGeneratorNode != null) {
            // 注意：这里的边依赖关系已经在 ClarificationCycleHandler.createClarificationQALoopNode 中建立了
            // 但我们需要确保边关系正确
            boolean edgeExists = dagGraph.getOutgoingEdges(questionGeneratorNode).stream()
                .anyMatch(edge -> edge.getType() == EdgeType.PROVIDES &&
                          dagGraph.getGraph().getEdgeTarget(edge).getId().equals(clarificationCycleNode.getId()));
            
            if (edgeExists) {
                log.info("[{}] 确认依赖关系: 澄清问题生成器 --PROVIDES--> 澄清循环节点", questionGeneratorNode.getId());
            } else {
                log.warn("[{}] 澄清问题生成器到澄清循环节点的边关系未正确建立", questionGeneratorNode.getId());
            }
        }

        log.info("[{}] 依赖结构构建完成，总节点数: {}, 总边数: {}", 
            userRequestNode.getId(), dagGraph.getAllNodes().size(), dagGraph.getAllEdges().size());
    }

    /**
     * 执行节点逻辑
     * 确保节点已经初始化，然后执行业务逻辑
     */
    private void executeNodeLogic(ComposeTaskNode node, ExecutionContext executionContext, DagGraph dagGraph) {
        // 创建数据上下文
        IDagDataContext dataContext = new DagDataContextImpl(executionContext, dagGraph, node);
        // 执行节点的业务逻辑（注意：初始化在创建节点时已完成）
        node.execute(dataContext);

        // 标记节点为已解析状态
        dagGraph.updateNodeStatus(node.getId(), NodeStatus.RESOLVED);

        log.info("[{}] 节点逻辑执行完成: {}", node.getId(), node.getDisplayName());
    }

    /**
     * 存储DagGraph到缓存
     */
    private void storeDagGraphToCache(ExecutionContext context, DagGraph dagGraph) {
        String executionId = context.getExecutionId();
        
        // 更新缓存
        DagGraphCacheManager.INSTANCE.putDagGraph(executionId, dagGraph);
        
        log.info("[{}] DagGraph已存储到缓存，当前阶段: userInput -> 边界分析 -> 澄清问题生成器 -> 澄清循环节点", executionId);
    }
}