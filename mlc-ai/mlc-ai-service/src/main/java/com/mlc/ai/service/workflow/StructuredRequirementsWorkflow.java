package com.mlc.ai.service.workflow;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.service.MlcAiServiceConstants;
import com.mlc.ai.service.workflow.task.StructuredRequirementsTaskExecutor;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.manager.WorkflowManager;
import com.mlc.ai.task.model.AITaskModel;
import com.mlc.ai.task.model.AITaskModel.ChatClientConfig;
import com.mlc.ai.task.model.AITaskModel.PromptModelConfig;
import com.mlc.base.common.utils.ContextKey;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 结构化需求工作流
 * 处理澄清问答记录，生成结构化的原子意图和概念
 */
@Slf4j
public class StructuredRequirementsWorkflow {

    private final WorkflowManager workflowManager;

    public StructuredRequirementsWorkflow() {
        this.workflowManager = WorkflowManager.INSTANCE;
    }

    /**
     * 注册任务执行器
     */
    private void registerExecutors(ExecutionContext executionContext) {
        // 注册结构化需求分析执行器
        executionContext.registerExecutor("StructuredRequirementsTaskExecutor",
            new StructuredRequirementsTaskExecutor());
    }

    /**
     * 执行结构化需求分析工作流
     * 
     * @param executionContext 执行上下文（与澄清问答的执行上下文相同）
     * @param qaLoopId 澄清循环节点的ID
     * @return 执行结果流
     */
    public Flux<String> executeStructuredRequirementsAnalysis(ExecutionContext executionContext, String qaLoopId) {
        // 创建工作流
        String dagEngineId = this.createStructuredRequirementsWorkflow();
        this.registerExecutors(executionContext);
        // 放入澄清循环节点ID到上下文
        executionContext.setAttribute(MlcAiServiceConstants.QA_LOOP_ID, qaLoopId);
        // 执行工作流
        return workflowManager.executeWorkflow(dagEngineId, executionContext);
    }

    /**
     * 创建结构化需求分析工作流
     */
    private String createStructuredRequirementsWorkflow() {
        // 创建工作流
        String dagEngineId = workflowManager.createWorkflow(
            "结构化需求分析工作流", 
            "将澄清问答记录转换为结构化的原子意图和概念"
        );

        // 1. 结构化需求分析任务
        String structuredRequirementsTaskId = workflowManager.addTask(dagEngineId, createStructuredRequirementsTask());

        log.info("结构化需求分析工作流创建完成，工作流ID: {}", dagEngineId);
        return dagEngineId;
    }

    /**
     * 创建结构化需求分析任务
     * 温度：0.1，目的是在结构化分析时保持高度确定性，确保输出格式的一致性
     */
    private AITaskModel createStructuredRequirementsTask() {
        return AITaskModel.builder()
            .name("结构化需求分析任务")
            .modelName("gemini")
            .executorKey("StructuredRequirementsTaskExecutor")
            .chatClientConfig(ChatClientConfig.builder()
                                .modelType(AiModelTypeEnum.GEMINI)
                                .temperature(0.1)
                                .build()
            )
            .promptModelConfig(PromptModelConfig.builder()
                               .promptPath("/mlc/ai/prompt/Outline/3_0/Structured_Requirements_CoT_v3.prompt.yaml")
                               .build())
            .build();
    }
}