package com.mlc.ai.service.workflow.clarification;

import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.util.QAPair;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 澄清循环节点 - 自包含的多轮问答交互节点
 * <p>
 * 核心职责：
 * 1. 接收澄清问题列表作为输入
 * 2. 管理多轮问答交互的完整生命周期
 * 3. 实现内部的 Scatter-Gather 逻辑
 * 4. 输出汇总的问答对集合
 * <p>
 * 对主DAG的接口：
 * - 输入：List<ClarificationQuestionNode> 澄清问题列表
 * - 输出：List<QAPair> 汇总的问答对集合
 * <p>
 * 内部状态管理：
 * - PENDING: 等待初始化
 * - CLARIFY_PENDING: 等待用户回答
 * - RESOLVED: 所有问题已回答，数据已汇总
 */
@Getter
@Setter
@Slf4j
public class ClarificationLoopState {
    private List<ClarificationQuestionNode> inputQuestions;
    private Map<String, QAPair> qaPairMap = new ConcurrentHashMap<>();
    private List<QAPair> consolidatedQAPairs = new ArrayList<>();
    private CompletionStrategy completionStrategy = CompletionStrategy.ALL_ANSWERED;
    private CycleStatus status = CycleStatus.PENDING;
    private long createdTimestamp = System.currentTimeMillis();
    private long completedTimestamp = 0;
    private CycleStatistics statistics = new CycleStatistics();

    public ClarificationLoopState(List<ClarificationQuestionNode> inputQuestions) {
        this.inputQuestions = new ArrayList<>(inputQuestions);
        initializeQAPairs();
    }

    /**
     * 初始化问答对集合
     */
    public void initializeQAPairs() {
        if (CollectionUtils.isEmpty(inputQuestions)) {
            return;
        }

        qaPairMap.clear();
        for (ClarificationQuestionNode questionNode : inputQuestions) {
            QAPair qaPair = QAPair.fromClarificationQuestion(questionNode);
            qaPairMap.put(questionNode.getId(), qaPair);
        }

        statistics.totalQuestions = inputQuestions.size();
        this.status = CycleStatus.CLARIFY_PENDING;
    }

    /**
     * 处理用户回答 (Scatter-Gather 的核心逻辑)
     */
    public boolean processUserAnswer(String questionId, String userAnswer) {
        QAPair qaPair = qaPairMap.get(questionId);
        if (qaPair == null) {
            log.warn("未找到问题ID: {}", questionId);
            return false;
        }

        qaPair.setAnswer(userAnswer);
        statistics.answeredQuestions++;

        log.info("处理用户回答: 问题[{}] -> 回答[{}]", questionId, userAnswer);

        if (checkCompletionCondition()) {
            completeGatherPhase();
        }

        return true;
    }

    /**
     * 检查完成条件 (Gather 阶段的关键逻辑)
     *
     * @return 是否满足完成条件
     */
    private boolean checkCompletionCondition() {
        return switch (completionStrategy) {
            case ALL_ANSWERED -> qaPairMap.values().stream().allMatch(QAPair::isAnswered);
            case MAJORITY_ANSWERED -> {
                long answeredCount = qaPairMap.values().stream().mapToLong(qaPair -> qaPair.isAnswered() ? 1 : 0).sum();
                yield answeredCount >= (qaPairMap.size() + 1) / 2;
            }
            case AT_LEAST_ONE -> qaPairMap.values().stream().anyMatch(QAPair::isAnswered);
        };
    }

    /**
     * 完成汇总阶段 (Gather 的最终步骤)
     */
    private void completeGatherPhase() {
        // 执行最终校验
        // validateCompleteness();

        // 汇总所有问答对
        consolidatedQAPairs = new ArrayList<>(qaPairMap.values());

        // 更新统计信息
        statistics.completedTimestamp = System.currentTimeMillis();
        statistics.totalDurationMs = statistics.completedTimestamp - createdTimestamp;

        // 更新节点状态
        this.status = CycleStatus.RESOLVED;
        this.completedTimestamp = System.currentTimeMillis();
    }

    public void forceComplete() {
        log.warn("强制完成澄清循环");
        completeGatherPhase();
    }

    public List<QAPair> getPendingQuestions() {
        return qaPairMap.values().stream()
                        .filter(qaPair -> qaPair.getAnswerStatus() == QAPair.AnswerStatus.PENDING)
                        .collect(Collectors.toList());
    }

    public double getProgressPercentage() {
        if (statistics.totalQuestions == 0) return 0.0;
        return (double) statistics.answeredQuestions / statistics.totalQuestions * 100.0;
    }

    public boolean isCompleted() {
        return status == CycleStatus.RESOLVED && consolidatedQAPairs != null;
    }


    /**
     * 循环状态枚举
     */
    public enum CycleStatus {
        PENDING,           // 等待初始化
        CLARIFY_PENDING,   // 等待用户回答
        RESOLVED           // 已完成
    }

    /**
     * 完成策略枚举
     */
    public enum CompletionStrategy {
        ALL_ANSWERED,      // 所有问题都必须回答
        MAJORITY_ANSWERED, // 超过一半的问题回答即可
        AT_LEAST_ONE      // 至少回答一个问题
    }

    /**
     * 循环统计信息
     */
    @Getter
    @Setter
    public static class CycleStatistics {
        private int totalQuestions = 0;
        private int answeredQuestions = 0;
        private long completedTimestamp = 0;
        private long totalDurationMs = 0;

        public double getAnswerRate() {
            return totalQuestions == 0 ? 0.0 : (double) answeredQuestions / totalQuestions;
        }
    }
}

