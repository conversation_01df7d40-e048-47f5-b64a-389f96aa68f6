package com.mlc.ai.service.workflow.clarification;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.context.DagDataContextImpl;
import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.ComposeTaskNode;
import com.mlc.ai.prompt.store.dag.util.QAPair;
import com.mlc.ai.service.workflow.clarification.ClarificationLoopState.CycleStatistics;
import com.mlc.ai.service.workflow.task.logic.ClarificationCycleLogic;
import com.mlc.ai.task.context.ExecutionContext;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 澄清循环处理器 - 基于 IDagNodeLogic 生命周期实现
 * 专门处理澄清循环的创建、管理和状态更新
 */
@Slf4j
public class ClarificationCycleHandler {
    
    private final DagGraph dagGraph;

    public ClarificationCycleHandler(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
    }

    /**
     * 创建澄清循环函数节点
     * @param previousNodeId 前置节点ID
     * @param clarificationQuestions 澄清问题列表
     * @return 创建的澄清循环函数节点
     */
    public ComposeTaskNode createClarificationQALoopNode(String previousNodeId, List<ClarificationQuestionNode> clarificationQuestions) {
        IDagNode previousNode = dagGraph.getNode(previousNodeId);
        if (previousNode == null) {
            log.error("前置节点未找到: {}", previousNodeId);
            return null;
        }
        
        if (clarificationQuestions == null || clarificationQuestions.isEmpty()) {
            log.error("澄清问题列表为空或null，无法创建澄清循环节点的状态: {}", previousNodeId);
            throw new IllegalArgumentException("Clarification questions list cannot be null or empty for creating ClarificationCycleNode.");
        }

        // 创建基于生命周期的澄清循环节点
        ComposeTaskNode cycleNode = new ComposeTaskNode(
            "ClarificationCycle",
            new ClarificationCycleLogic(),  //创建澄清循环逻辑实现
            List.of("generated_questions"), // 输入：澄清问题列表 (用于DAG依赖)
            "consolidated_qa_pairs_key" // 输出：汇总的问答对
        );

        // 将循环节点添加到图中
        dagGraph.addNode(cycleNode);

        // 建立从前置节点到循环节点的流向
        dagGraph.addEdge(previousNode, cycleNode, EdgeType.PROVIDES);

        log.info("从节点 {} 创建澄清循环函数节点: {} (包含 {} 个问题)",
            previousNodeId, cycleNode.getId(), clarificationQuestions.size());

        return cycleNode;
    }

    /**
     * 批量处理用户回答
     * @param cycleNodeId 循环节点ID
     * @param answers 问题ID到回答的映射
     * @return 是否处理成功
     */
    public boolean processBatchAnswersToCycle(String cycleNodeId, ExecutionContext executionContext, Map<String, String> answers) {
        try {
            IDagNode node = dagGraph.getNode(cycleNodeId);
            if (!(node instanceof ComposeTaskNode cycleNode)) {
                throw new IllegalArgumentException("未找到澄清循环节点或类型不正确: " + cycleNodeId);
            }
            
            // 获取节点逻辑
            if (!(cycleNode.getNodeLogic() instanceof ClarificationCycleLogic cycleLogic)) {
                throw new IllegalArgumentException("澄清循环节点逻辑类型不正确: " + cycleNodeId);
            }
            
            // 处理批量回答
            boolean result = cycleLogic.processBatchAnswers(answers, cycleNodeId);
            
            if (cycleLogic.isCompleted()) {
                // 循环完成，更新DAG中的节点状态
                dagGraph.updateNodeStatus(cycleNodeId, NodeStatus.RESOLVED);
                log.info("澄清循环节点 {} 已完成", cycleNodeId);

                this.executeCycleLogic(cycleNodeId, executionContext);
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量处理回答失败: {}", cycleNodeId, e);
            return false;
        }
    }

    /**
     * 执行澄清循环节点的业务逻辑
     */
    public Map<String, Object> executeCycleLogic(String cycleNodeId, ExecutionContext executionContext) {
        IDagNode node = dagGraph.getNode(cycleNodeId);
        if (!(node instanceof ComposeTaskNode cycleNode)) {
            throw new IllegalArgumentException("未找到澄清循环节点或类型不正确: " + cycleNodeId);
        }

        // 创建 DagDataContext 实例
        IDagDataContext context = new DagDataContextImpl(executionContext, dagGraph, cycleNode);

        // 调用 ComposeTaskNode 的 execute 方法
        Map<String, Object> result = cycleNode.execute(context);

        log.info("[{}] 澄清循环逻辑执行完成", cycleNodeId);
        return result;
    }

    /**
     * 将澄清循环节点连接到下一个处理节点
     *
     * @param cycleNodeId 循环节点ID
     * @param nextNodeId 下一个节点ID
     */
    public void connectCycleToNextNode(String cycleNodeId, String nextNodeId) {
        IDagNode cycleNode = dagGraph.getNode(cycleNodeId);
        IDagNode nextNode = dagGraph.getNode(nextNodeId);
        
        if (cycleNode == null || nextNode == null) {
            log.error("节点未找到 - 循环节点: {}, 下一节点: {}", cycleNodeId, nextNodeId);
            return;
        }
        
        // 建立从循环节点到下一节点的流向
        dagGraph.addEdge(cycleNode, nextNode, EdgeType.PROVIDES);
        log.info("澄清循环节点 {} 已连接到下一节点: {}", cycleNodeId, nextNodeId);
    }
    
    /**
     * 获取循环节点的汇总问答对
     */
    public List<QAPair> getConsolidatedQAPairs(String cycleNodeId) {
        try {
            ClarificationCycleLogic cycleLogic = getCycleLogic(cycleNodeId);
            return cycleLogic.getCycleState().getConsolidatedQAPairs();
        } catch (Exception e) {
            log.error("获取汇总问答对失败: {}", cycleNodeId, e);
            return List.of();
        }
    }
    
    /**
     * 获取循环节点的待回答问题
     */
    public List<QAPair> getPendingQuestions(String cycleNodeId) {
        try {
            ClarificationCycleLogic cycleLogic = getCycleLogic(cycleNodeId);
            return cycleLogic.getCycleState().getPendingQuestions();
        } catch (Exception e) {
            log.error("获取待回答问题失败: {}", cycleNodeId, e);
            return List.of();
        }
    }
    
    /**
     * 获取循环节点的进度信息
     */
    public double getCycleProgress(String cycleNodeId) {
        try {
            ClarificationCycleLogic cycleLogic = getCycleLogic(cycleNodeId);
            return cycleLogic.getCycleState().getProgressPercentage();
        } catch (Exception e) {
            log.error("获取循环进度失败: {}", cycleNodeId, e);
            return 0.0;
        }
    }

    /**
     * 获取循环节点的统计信息
     */
    public CycleStatistics getCycleStatistics(String cycleNodeId) {
        try {
            ClarificationCycleLogic cycleLogic = getCycleLogic(cycleNodeId);
            return cycleLogic.getCycleState().getStatistics();
        } catch (Exception e) {
            log.error("获取循环统计信息失败: {}", cycleNodeId, e);
            return new CycleStatistics(); // 返回空的统计信息
        }
    }

    /**
     * 检查循环节点是否已完成
     */
    public boolean isCycleCompleted(String cycleNodeId) {
        try {
            ClarificationCycleLogic cycleLogic = getCycleLogic(cycleNodeId);
            return cycleLogic.isCompleted();
        } catch (Exception e) {
            log.error("检查循环完成状态失败: {}", cycleNodeId, e);
            return false;
        }
    }
    
    /**
     * 强制完成循环节点（用于异常情况等）
     */
    public boolean forceCompleteCycle(String cycleNodeId) {
        try {
            ClarificationCycleLogic cycleLogic = getCycleLogic(cycleNodeId);
            cycleLogic.forceComplete(cycleNodeId);
            dagGraph.updateNodeStatus(cycleNodeId, NodeStatus.RESOLVED);
            
            log.warn("强制完成澄清循环节点: {}", cycleNodeId);
            return true;
        } catch (Exception e) {
            log.error("强制完成循环失败: {}", cycleNodeId, e);
            return false;
        }
    }
    
    /**
     * 获取循环节点逻辑
     */
    private ClarificationCycleLogic getCycleLogic(String cycleNodeId) {
        IDagNode node = dagGraph.getNode(cycleNodeId);
        if (!(node instanceof ComposeTaskNode cycleNode)) {
            throw new IllegalArgumentException("未找到澄清循环节点或类型不正确: " + cycleNodeId);
        }
        
        if (!(cycleNode.getNodeLogic() instanceof ClarificationCycleLogic cycleLogic)) {
            throw new IllegalArgumentException("澄清循环节点逻辑类型不正确: " + cycleNodeId);
        }
        
        return cycleLogic;
    }
} 