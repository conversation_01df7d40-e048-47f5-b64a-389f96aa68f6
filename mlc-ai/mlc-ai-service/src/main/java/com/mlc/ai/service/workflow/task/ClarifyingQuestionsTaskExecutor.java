package com.mlc.ai.service.workflow.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.service.MlcAiServiceConstants;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.context.AIContextExecutor;
import com.mlc.ai.task.executor.AITaskExecutor;
import com.mlc.ai.task.model.AITaskModel;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;

/**
 * 澄清问题生成任务执行器
 * 负责根据边界分析结果生成澄清问题，帮助用户明确需求
 */
@Slf4j
public class ClarifyingQuestionsTaskExecutor extends AITaskExecutor {

    @Override
    protected void justInLLMExecutor(AIContextExecutor llmExecutor, AITaskModel aiTask, ExecutionContext context) {
        llmExecutor.withStreamProcessor(AIContextExecutor::codeBlockExtractorProcessor)
        .withResultProcessor((response, task, ctx) -> {
            try {
                // 解析LLM返回的JSON结果,提取澄清问题列表
                List<ClarificationQuestionNode> questions = new ObjectMapper().readValue(response, new TypeReference<>() {});

                // 存储结果到ExecutionContext，供后续任务使用
                context.setAttribute(MlcAiServiceConstants.CLARIFYING_QUESTIONS, questions);

                log.info("[{}] 澄清问题生成完成，共生成 {} 个问题", context.getExecutionId(), questions.size());

                // 重构：将格式化响应逻辑移到 DagNodeManagerTaskExecutor 之后
                // 这样可以在响应中包含 cycleNodeId，便于 ClarificationAnswerBizModel 直接使用
                return String.format("澄清问题生成完成，共生成 %d 个问题", questions.size());
            } catch (IOException e) {
                log.error("[{}] 解析LLM响应失败", context.getExecutionId(), e);
                throw new RuntimeException("解析澄清问题结果失败: " + e.getMessage());
            }
        });
    }

    /**
     * 构建规范化的返回格式（移到外部调用）
     * 使用```clarifyingQuestions代码块包装澄清问题对象
     *
     * @param questions 澄清问题列表
     * @param executionId 执行ID
     * @param cycleNodeId 澄清循环节点ID
     * @return 格式化的响应字符串
     */
    public static String buildFormattedResponse(List<ClarificationQuestionNode> questions, String executionId, String cycleNodeId) {
        return "```clarifyingQuestions\n"
        + buildQuestionObjectsJson(questions, executionId, cycleNodeId)
        + "\n```\n\n";
    }

    /**
     * 构建澄清问题对象的JSON格式
     * 每个问题对象包含完整的信息：问题ID、序列号、executionId、cycleNodeId等
     * 
     * @param questions 澄清问题列表
     * @param executionId 执行ID
     * @param cycleNodeId 澄清循环节点ID
     * @return JSON字符串
     */
    private static String buildQuestionObjectsJson(List<ClarificationQuestionNode> questions, String executionId, String cycleNodeId) {
        try {
            List<ClarificationQuestionObject> questionObjects = new ArrayList<>();

            for (int i = 0; i < questions.size(); i++) {
                ClarificationQuestionNode qn = questions.get(i);

                ClarificationQuestionObject questionObj = new ClarificationQuestionObject(
                    qn.getId(), i + 1, qn.getQuestion(), qn.getPotentialRecommendations()
                );

                questionObjects.add(questionObj);
            }

            return new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(Map.of(
                "executionId", executionId,
                "qaLoopId", cycleNodeId,
                "clarifyingQuestions", questionObjects
            ));
        } catch (Exception e) {
            log.error("构建澄清问题对象JSON失败", e);
            return "[]";
        }
    }

    public record ClarificationQuestionObject(String questionId, int sequence, String question, List<String> potentialRecommendations) {}
}

