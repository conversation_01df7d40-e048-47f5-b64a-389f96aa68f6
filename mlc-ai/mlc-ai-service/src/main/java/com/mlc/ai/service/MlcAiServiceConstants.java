package com.mlc.ai.service;


import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.OutsideBorderNode;
import com.mlc.base.common.utils.ContextKey;
import java.util.List;

public interface MlcAiServiceConstants {

    // 工作流执行上下文的唯一标识
    ContextKey<String> QA_LOOP_ID = ContextKey.create("qaLoopId");

    // 获取澄清问题列表
    ContextKey<List<ClarificationQuestionNode>> CLARIFYING_QUESTIONS = ContextKey.create("clarifyingQuestions");

    // 用户原始请求的上下文键
    ContextKey<String> USER_RAW_REQUEST_CONTEXT = ContextKey.create("userRawRequest");

    // 边界分析结果的上下文键
    ContextKey<OutsideBorderNode> BOUNDARY_ANALYSIS_RESULT = ContextKey.create("boundaryAnalysisResult");
}
