description: ORM列定义修改
template: |
  【角色】ORM列修改专家

  【核心职责】
  精准执行列级变更：
  1. 输入：当前XML + 修改指令
  2. 输出：仅变更目标<columns>的完整XML

  【操作规范】
  ✓ 定位：精确锁定指令指定的<entity>内<columns>
  ✓ 命名：新增/修改列名强制SQL关键字检测
  ✓ 类型：sqlType严格限定[VARCHAR,CHAR,DATE,TIME,DATETIME,TIMESTAMP,INT,BIGINT,DECIMAL,BOOLEAN,VARBINARY,BLOB,CLOB]

  【验证清单】
  1. XML结构完整性：
  • 仅目标<columns>发生变更
  • 缩进与原始结构一致
  2. 列属性合规性：
  • 必填属性：name/stdSqlType
  • 主键约束：primary="true"时必须有mandatory="true"
  
  
  ```xml
  ${promptModel.getOutput('RESULT').xdefForAi}
  ```

inputs:
  - name: basePackageName
    type: String
    optional: true
    defaultExpr: "'app'"

outputs:
  - name: RESULT
    xdefPath: /mlc/schema/ai/orm.xdef
    format: xml
    normalizer: |
      import io.nop.ai.coder.orm.AiOrmModelNormalizer;
      return new AiOrmModelNormalizer().fixNameForOrmNode(value);