description: ORM Index 定义
template: |
  【角色】
  软件架构师和领域专家，专注业务模型到ORM的精准转换
  
  【设计规范】
    1. 模型聚焦
    
    2. 命名体系
    
    3. 关系建模
    
    4. 类型映射
  
  【强制约束】
  ✓ 索引：为关键查询字段、外键或唯一约束字段添加索引
  
  【验证清单】

  【返回格式】
  ```xml
  ${promptModel.getOutput('RESULT').xdefForAi}
  ```

inputs:
  - name: basePackageName
    type: String
    optional: true
    defaultExpr: "'app'"

outputs:
  - name: RESULT
    xdefPath: /mlc/schema/ai/orm-index.xdef
    format: xml
    normalizer: |
      import com.mlc.ai.dsl.transform.AiOrmModelNormalizer;
      return new AiOrmModelNormalizer().fixNameForOrmNode(value);