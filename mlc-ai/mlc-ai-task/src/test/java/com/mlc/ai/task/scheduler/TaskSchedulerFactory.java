package com.mlc.ai.task.scheduler;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.scheduler.policy.ITaskSchedulerPolicy;
import com.mlc.ai.task.scheduler.policy.DefaultTaskSchedulerPolicy;
import com.mlc.ai.task.workflow.WorkflowEngine;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务调度器工厂类
 * 提供便捷的方法来创建不同配置的任务调度器
 */
@Slf4j
public class TaskSchedulerFactory {

    /**
     * 创建默认配置的任务调度器
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @return 任务调度器实例
     */
    public static TaskScheduler createDefault(DagInMemoryEngine dagEngine, 
                                            WorkflowEngine workflowEngine, 
                                            ExecutionContext executionContext) {
        return new TaskScheduler(dagEngine, workflowEngine, executionContext);
    }

    /**
     * 创建带自定义调度策略的任务调度器
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @param schedulerPolicy 调度策略
     * @return 任务调度器实例
     */
    public static TaskScheduler createWithPolicy(DagInMemoryEngine dagEngine, 
                                               WorkflowEngine workflowEngine, 
                                               ExecutionContext executionContext,
                                               ITaskSchedulerPolicy schedulerPolicy) {
        return new TaskScheduler(dagEngine, workflowEngine, executionContext, schedulerPolicy);
    }

    /**
     * 创建高并发配置的任务调度器
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @param maxParallelism 最大并行度
     * @return 任务调度器实例
     */
    public static TaskScheduler createHighConcurrency(DagInMemoryEngine dagEngine, 
                                                    WorkflowEngine workflowEngine, 
                                                    ExecutionContext executionContext,
                                                    int maxParallelism) {
        ITaskSchedulerPolicy policy = DefaultTaskSchedulerPolicy.builder()
                .maxParallelism(maxParallelism)
                .continueOnFailure(true)
                .build();
        
        return new TaskScheduler(dagEngine, workflowEngine, executionContext, policy);
    }

    /**
     * 创建容错配置的任务调度器（任务失败时停止调度）
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @return 任务调度器实例
     */
    public static TaskScheduler createFailFast(DagInMemoryEngine dagEngine, 
                                             WorkflowEngine workflowEngine, 
                                             ExecutionContext executionContext) {
        ITaskSchedulerPolicy policy = DefaultTaskSchedulerPolicy.builder()
                .maxParallelism(10)
                .continueOnFailure(false)
                .build();
        
        return new TaskScheduler(dagEngine, workflowEngine, executionContext, policy);
    }

    /**
     * 创建串行执行的任务调度器
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @return 任务调度器实例
     */
    public static TaskScheduler createSerial(DagInMemoryEngine dagEngine, 
                                           WorkflowEngine workflowEngine, 
                                           ExecutionContext executionContext) {
        ITaskSchedulerPolicy policy = DefaultTaskSchedulerPolicy.builder()
                .maxParallelism(1)
                .continueOnFailure(true)
                .build();
        
        return new TaskScheduler(dagEngine, workflowEngine, executionContext, policy);
    }

    /**
     * 创建自定义配置的任务调度器
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @param maxParallelism 最大并行度
     * @param continueOnFailure 失败时是否继续
     * @return 任务调度器实例
     */
    public static TaskScheduler createCustom(DagInMemoryEngine dagEngine, 
                                           WorkflowEngine workflowEngine, 
                                           ExecutionContext executionContext,
                                           int maxParallelism,
                                           boolean continueOnFailure) {
        ITaskSchedulerPolicy policy = DefaultTaskSchedulerPolicy.builder()
                .maxParallelism(maxParallelism)
                .continueOnFailure(continueOnFailure)
                .build();
        
        log.info("创建自定义任务调度器 - 最大并行度: {}, 失败时继续: {}", maxParallelism, continueOnFailure);
        
        return new TaskScheduler(dagEngine, workflowEngine, executionContext, policy);
    }
}
