package com.mlc.ai.task.example;

import com.mlc.ai.core.enums.ConditionEngineType;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.executor.ConditionalTaskExecutor;
import com.mlc.ai.task.executor.ITaskExecutor;
import com.mlc.ai.task.model.ConditionalTaskModel;
import com.mlc.ai.task.model.NormalTaskModel;
import com.mlc.ai.task.model.NormalTaskModel.NormalTaskConfig;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.scheduler.strategy.ConditionBranchStrategy;
import com.mlc.ai.core.util.BranchConfigBuilder;
import com.mlc.ai.task.workflow.WorkflowEngine;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CountDownLatch;

/**
 * 规范化分支系统示例
 * 展示统一的条件表达式格式：executorName.outputKey operator value
 */
@Slf4j
public class StandardizedBranchExample {

    /**
     * 数据验证执行器
     * 输出标准化的验证结果
     */
    public static class DataValidationExecutor implements ITaskExecutor {
        @Override
        public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
            Random random = new Random();
            
            // 模拟验证结果
            boolean isValid = random.nextBoolean();
            int confidence = random.nextInt(100);
            String status = isValid ? "passed" : "failed";
            String level = confidence > 80 ? "high" : confidence > 50 ? "medium" : "low";
            
            // 标准化输出
            task.getTaskContext().getOutput().put("isValid", isValid);
            task.getTaskContext().getOutput().put("confidence", confidence);
            task.getTaskContext().getOutput().put("status", status);
            task.getTaskContext().getOutput().put("level", level);
            
            String message = String.format("数据验证完成: isValid=%s, confidence=%d, status=%s, level=%s", 
                isValid, confidence, status, level);
            log.info("[{}] {}", context.getExecutionId(), message);
            
            return Flux.just(message);
        }
    }

    /**
     * 业务处理执行器
     * 根据不同的处理类型执行相应的业务逻辑
     */
    public static class BusinessProcessExecutor implements ITaskExecutor {
        @Override
        public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
            String taskName = task.getName();
            String processType = task.getConfig().getParams().getOrDefault("processType", "standard");
            
            // 模拟处理结果
            boolean success = !processType.equals("error");
            String result = success ? "completed" : "failed";
            long processingTime = System.currentTimeMillis();
            
            // 标准化输出
            task.getTaskContext().getOutput().put("success", success);
            task.getTaskContext().getOutput().put("result", result);
            task.getTaskContext().getOutput().put("processingTime", processingTime);
            
            String message = String.format("业务处理 [%s-%s]: success=%s, result=%s", 
                taskName, processType, success, result);
            log.info("[{}] {}", context.getExecutionId(), message);
            
            return Flux.just(message);
        }
    }

    public static void main(String[] args) throws InterruptedException {
        // 创建执行上下文
        ExecutionContext executionContext = ExecutionContext.getInstance();
        
        // 创建DAG引擎
        DagInMemoryEngine dagEngine = DagInMemoryEngine.builder()
                   .name("规范化分支系统示例")
                   .description("展示统一的条件表达式格式和分支配置")
                   .build();
        
        // 创建分支策略
        executionContext.addStatusStrategy(new ConditionBranchStrategy());

        // 创建执行器注册表
        executionContext.registerExecutor("DataValidationExecutor", new DataValidationExecutor());
        executionContext.registerExecutor("BusinessProcessExecutor", new BusinessProcessExecutor());
        executionContext.registerExecutor("ConditionalTaskExecutor", new ConditionalTaskExecutor());
        
        // 创建工作流引擎
        WorkflowEngine workflowEngine = new WorkflowEngine(dagEngine, executionContext);

        // 构建规范化分支工作流
        
        // 1. 数据验证任务
        BaseTaskModel validationTask = NormalTaskModel.builder()
                       .name("数据验证")
                       .executorKey("DataValidationExecutor")
                       .build();
        dagEngine.addTask(validationTask);
        
        // 2. 验证结果分支条件任务
        ConditionalTaskModel.BranchCondition validBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("valid")
            .conditionExpression("DataValidation.isValid == true")  // 规范化格式
            .description("验证通过分支")
            .priority(1)
            .build();
            
        ConditionalTaskModel.BranchCondition highConfidenceBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("highConfidence")
            .conditionExpression("DataValidation.confidence > 80")  // 规范化格式
            .description("高置信度分支")
            .priority(2)
            .build();
            
        ConditionalTaskModel.BranchCondition mediumConfidenceBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("mediumConfidence")
            .conditionExpression("DataValidation.confidence > 50 && DataValidation.confidence <= 80")  // 复合条件
            .description("中等置信度分支")
            .priority(3)
            .build();

        BaseTaskModel validationBranchTask = ConditionalTaskModel.builder()
                    .name("验证结果分支")
                    .executorKey("ConditionalTaskExecutor")
                    .branchConditions(List.of(validBranch, highConfidenceBranch, mediumConfidenceBranch))
                    .defaultBranch("lowConfidence")
                    .engineType(ConditionEngineType.JAVASCRIPT)
                    .build();
        dagEngine.addTask(validationBranchTask);
        
        // 3. 高置信度处理任务
        BaseTaskModel highConfidenceProcessTask = NormalTaskModel.builder()
                .name("高置信度处理")
                .executorKey("BusinessProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of("processType", "premium"))
                        .branchConfig(BranchConfigBuilder.forBranch("highConfidence"))
                        .build())
                .build();
        dagEngine.addTask(highConfidenceProcessTask);
        
        // 4. 中等置信度处理任务
        BaseTaskModel mediumConfidenceProcessTask = NormalTaskModel.builder()
                .name("中等置信度处理")
                .executorKey("BusinessProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of("processType", "standard"))
                        .branchConfig(BranchConfigBuilder.forBranch("mediumConfidence"))
                        .build())
                .build();
        dagEngine.addTask(mediumConfidenceProcessTask);
        
        // 5. 验证通过处理任务
        BaseTaskModel validProcessTask = NormalTaskModel.builder()
                .name("验证通过处理")
                .executorKey("BusinessProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of("processType", "validated"))
                        .branchConfig(BranchConfigBuilder.forBranch("valid"))
                        .build())
                .build();
        dagEngine.addTask(validProcessTask);
        
        // 6. 低置信度处理任务（默认分支）
        BaseTaskModel lowConfidenceProcessTask = NormalTaskModel.builder()
                .name("低置信度处理")
                .executorKey("BusinessProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of("processType", "review"))
                        .branchConfig(BranchConfigBuilder.defaultBranch())
                        .build())
                .build();
        dagEngine.addTask(lowConfidenceProcessTask);
        
        // 7. 状态分类条件任务
        ConditionalTaskModel.BranchCondition passedBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("passed")
            .conditionExpression("DataValidation.status == 'passed'")  // 规范化格式
            .description("通过状态分支")
            .priority(1)
            .build();

        BaseTaskModel statusBranchTask = ConditionalTaskModel.builder()
                    .name("状态分类")
                    .executorKey("ConditionalTaskExecutor")
                    .branchConditions(List.of(passedBranch))
                    .defaultBranch("failed")
                    .engineType(ConditionEngineType.SIMPLE)
                    .build();
        dagEngine.addTask(statusBranchTask);
        
        // 8. 成功后续处理任务
        BaseTaskModel successFollowupTask = NormalTaskModel.builder()
                .name("成功后续处理")
                .executorKey("BusinessProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of("processType", "followup"))
                        .branchConfig(BranchConfigBuilder.forBranch("passed"))
                        .build())
                .build();
        dagEngine.addTask(successFollowupTask);
        
        // 9. 失败处理任务
        BaseTaskModel failureHandlingTask = NormalTaskModel.builder()
                .name("失败处理")
                .executorKey("BusinessProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of("processType", "error"))
                        .branchConfig(BranchConfigBuilder.forBranch("failed"))
                        .build())
                .build();
        dagEngine.addTask(failureHandlingTask);
        
        // 添加依赖关系
        // 主流程
        dagEngine.addDependency(validationTask.getId(), validationBranchTask.getId());
        
        // 验证分支
        dagEngine.addDependency(validationBranchTask.getId(), highConfidenceProcessTask.getId());
        dagEngine.addDependency(validationBranchTask.getId(), mediumConfidenceProcessTask.getId());
        dagEngine.addDependency(validationBranchTask.getId(), validProcessTask.getId());
        dagEngine.addDependency(validationBranchTask.getId(), lowConfidenceProcessTask.getId());
        
        // 状态分类 - 从高置信度处理后进行
        dagEngine.addDependency(highConfidenceProcessTask.getId(), statusBranchTask.getId());
        
        // 状态分支
        dagEngine.addDependency(statusBranchTask.getId(), successFollowupTask.getId());
        dagEngine.addDependency(statusBranchTask.getId(), failureHandlingTask.getId());
        
        // 使用CountDownLatch等待工作流完成
        CountDownLatch latch = new CountDownLatch(1);
        
        // 启动工作流
        workflowEngine.start()
            .doOnComplete(() -> {
                log.info("规范化分支工作流执行完成");
                latch.countDown();
            })
            .doOnError(error -> {
                log.error("规范化分支工作流执行失败", error);
                latch.countDown();
            })
            .subscribe();
        
        // 等待工作流完成
        latch.await();
        
        // 输出最终结果
        log.info("=== 工作流执行结果 ===");
        dagEngine.getTasks().values().forEach(task -> {
            log.info("任务: {} - 状态: {} - 输出: {}", 
                task.getName(), task.getStatus(), task.getTaskContext().getOutput());
        });
        
        // 展示规范化的改进
        log.info("\n=== 规范化条件表达式格式 ===");
        log.info("格式: executorName.outputKey operator value");
        log.info("示例:");
        log.info("  - DataValidation.isValid == true");
        log.info("  - DataValidation.confidence > 80");
        log.info("  - DataValidation.status == 'passed'");
        log.info("  - DataValidation.confidence > 50 && DataValidation.confidence <= 80");
        
        log.info("\n=== 执行器名称提取规则 ===");
        log.info("DataValidationExecutor -> DataValidation");
        log.info("BusinessProcessExecutor -> BusinessProcess");
        log.info("ConditionalTaskExecutor -> ConditionalTask");
        
        log.info("\n=== 分支配置方式 ===");
        log.info("单分支: BranchConfigBuilder.forBranch(\"valid\")");
        log.info("多分支: BranchConfigBuilder.forAnyBranch(\"high\", \"medium\")");
        log.info("默认分支: BranchConfigBuilder.defaultBranch()");
    }
} 