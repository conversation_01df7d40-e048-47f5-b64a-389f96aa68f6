package com.mlc.ai.task.engine;

import com.mlc.ai.task.model.NormalTaskModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DagEngine 测试类，使用 jgrapht 框架的功能
 */
public class DagEngineTest {

    private DagInMemoryEngine dagEngine;

    @BeforeEach
    void setUp() {
        dagEngine = DagInMemoryEngine.builder()
                                     .name("测试DAG")
                                     .description("测试重构后的DagEngine")
                                     .build();
    }

    @Test
    void testAddTask() {
        // 创建任务
        NormalTaskModel task = NormalTaskModel.builder()
                .name("测试任务")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String taskId = dagEngine.addTask(task);

        // 验证任务已添加
        assertNotNull(taskId);
        assertEquals(task.getId(), taskId);
        assertTrue(dagEngine.getTasks().containsKey(taskId));
    }

    @Test
    void testAddDependency() {
        // 创建两个任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();

        // 添加任务到DAG
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);

        // 添加依赖关系
        boolean result = dagEngine.addDependency(task1Id, task2Id);

        // 验证依赖关系
        assertTrue(result);
        assertTrue(dagEngine.getPredecessors(task2Id).contains(task1Id));
        assertTrue(dagEngine.getSuccessors(task1Id).contains(task2Id));
    }

    @Test
    void testGetEntryTasks() {
        // 创建三个任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task3 = NormalTaskModel.builder()
                .name("任务3")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);
        String task3Id = dagEngine.addTask(task3);

        // 添加依赖关系：task1 -> task2 -> task3
        dagEngine.addDependency(task1Id, task2Id);
        dagEngine.addDependency(task2Id, task3Id);

        // 获取入度为0的任务
        Set<String> entryTasks = dagEngine.getEntryTasks();

        // 验证只有task1是入度为0的任务
        assertEquals(1, entryTasks.size());
        assertTrue(entryTasks.contains(task1Id));
    }

    @Test
    void testGetExitTasks() {
        // 创建三个任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task3 = NormalTaskModel.builder()
                .name("任务3")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);
        String task3Id = dagEngine.addTask(task3);

        // 添加依赖关系：task1 -> task2 -> task3
        dagEngine.addDependency(task1Id, task2Id);
        dagEngine.addDependency(task2Id, task3Id);

        // 获取出度为0的任务
        Set<String> exitTasks = dagEngine.getExitTasks();

        // 验证只有task3是出度为0的任务
        assertEquals(1, exitTasks.size());
        assertTrue(exitTasks.contains(task3Id));
    }

    @Test
    void testGetTopologicalOrder() {
        // 创建三个任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task3 = NormalTaskModel.builder()
                .name("任务3")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);
        String task3Id = dagEngine.addTask(task3);

        // 添加依赖关系：task1 -> task2 -> task3
        dagEngine.addDependency(task1Id, task2Id);
        dagEngine.addDependency(task2Id, task3Id);

        // 获取拓扑排序
        List<String> topologicalOrder = dagEngine.getTopologicalOrder();

        // 验证拓扑排序结果
        assertEquals(3, topologicalOrder.size());
        assertEquals(task1Id, topologicalOrder.get(0));
        assertEquals(task2Id, topologicalOrder.get(1));
        assertEquals(task3Id, topologicalOrder.get(2));
    }

    @Test
    void testIsValid() {
        // 创建任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);

        // 添加依赖关系
        dagEngine.addDependency(task1Id, task2Id);

        // 验证DAG有效性
        assertTrue(dagEngine.isValid());
    }

    @Test
    void testRemoveDependency() {
        // 创建两个任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);

        // 添加依赖关系
        dagEngine.addDependency(task1Id, task2Id);

        // 验证依赖关系存在
        assertTrue(dagEngine.getPredecessors(task2Id).contains(task1Id));

        // 移除依赖关系
        boolean result = dagEngine.removeDependency(task1Id, task2Id);

        // 验证依赖关系已移除
        assertTrue(result);
        assertFalse(dagEngine.getPredecessors(task2Id).contains(task1Id));
        assertFalse(dagEngine.getSuccessors(task1Id).contains(task2Id));
    }

    @Test
    void testGetReachableTasks() {
        // 创建四个任务
        NormalTaskModel task1 = NormalTaskModel.builder()
                .name("任务1")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task2 = NormalTaskModel.builder()
                .name("任务2")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task3 = NormalTaskModel.builder()
                .name("任务3")
                .executorKey("test.executor")
                .build();
        
        NormalTaskModel task4 = NormalTaskModel.builder()
                .name("任务4")
                .executorKey("test.executor")
                .build();

        // 添加任务
        String task1Id = dagEngine.addTask(task1);
        String task2Id = dagEngine.addTask(task2);
        String task3Id = dagEngine.addTask(task3);
        String task4Id = dagEngine.addTask(task4);

        // 添加依赖关系：task1 -> task2 -> task3, task4独立
        dagEngine.addDependency(task1Id, task2Id);
        dagEngine.addDependency(task2Id, task3Id);

        // 获取从task1可达的任务
        Set<String> reachableTasks = dagEngine.getReachableTasks(task1Id);

        // 验证可达任务
        assertEquals(3, reachableTasks.size());
        assertTrue(reachableTasks.contains(task1Id));
        assertTrue(reachableTasks.contains(task2Id));
        assertTrue(reachableTasks.contains(task3Id));
        assertFalse(reachableTasks.contains(task4Id));
    }
} 