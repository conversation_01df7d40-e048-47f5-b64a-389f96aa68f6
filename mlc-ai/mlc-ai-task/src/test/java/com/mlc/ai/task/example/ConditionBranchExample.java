package com.mlc.ai.task.example;

import com.mlc.ai.core.enums.ConditionEngineType;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.executor.ConditionalTaskExecutor;
import com.mlc.ai.task.executor.ITaskExecutor;
import com.mlc.ai.task.model.ConditionalTaskModel;
import com.mlc.ai.task.model.NormalTaskModel;
import com.mlc.ai.task.model.NormalTaskModel.NormalTaskConfig;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.scheduler.strategy.ConditionBranchStrategy;
import com.mlc.ai.task.workflow.WorkflowEngine;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CountDownLatch;

/**
 * 灵活分支系统示例
 * 展示如何使用新的灵活分支条件系统
 */
@Slf4j
public class ConditionBranchExample {

    /**
     * 模拟数据处理任务执行器
     */
    public static class DataProcessExecutor implements ITaskExecutor {
        @Override
        public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
            String name = task.getName();
            String action = task.getConfig().getParams().getOrDefault("action", "process");
            
            Random random = new Random();
            
            // 模拟不同的处理结果
            String status;
            int score;
            String category;
            
            switch (action) {
                case "analyze":
                    score = random.nextInt(100);
                    status = score > 70 ? "excellent" : score > 40 ? "good" : "poor";
                    category = score > 80 ? "premium" : score > 50 ? "standard" : "basic";
                    break;
                case "validate":
                    boolean isValid = random.nextBoolean();
                    status = isValid ? "valid" : "invalid";
                    score = isValid ? random.nextInt(50) + 50 : random.nextInt(50);
                    category = isValid ? "approved" : "rejected";
                    break;
                default:
                    score = random.nextInt(100);
                    status = "completed";
                    category = "general";
            }
            
            // 将结果添加到输出
            task.getTaskContext().getOutput().put("score", score);
            task.getTaskContext().getOutput().put("status", status);
            task.getTaskContext().getOutput().put("category", category);
            task.getTaskContext().getOutput().put("timestamp", System.currentTimeMillis());
            
            String message = String.format("执行任务 [%s-%s], 结果: status=%s, score=%d, category=%s", 
                name, action, status, score, category);
            log.info("[{}] {}", context.getExecutionId(), message);
            
            return Flux.just(message);
        }
    }

    public static void main(String[] args) throws InterruptedException {
        // 创建执行上下文
        ExecutionContext executionContext = ExecutionContext.getInstance();
        
        // 创建DAG引擎
        DagInMemoryEngine dagEngine = DagInMemoryEngine.builder()
                   .name("灵活分支系统示例")
                   .description("展示如何使用新的灵活分支条件系统")
                   .build();
        
        // 创建灵活分支策略
        executionContext.addStatusStrategy(new ConditionBranchStrategy());

        // 创建独立的执行器注册表
        executionContext.registerExecutor("DataProcessExecutor", new DataProcessExecutor());
        executionContext.registerExecutor("ConditionalTaskExecutor", new ConditionalTaskExecutor());

        // 创建工作流引擎
        WorkflowEngine workflowEngine = new WorkflowEngine(dagEngine, executionContext);

        // 构建灵活分支工作流
        
        // 1. 数据分析任务
        BaseTaskModel analyzeTask = NormalTaskModel.builder()
                       .name("数据分析")
                       .executorKey("DataProcessExecutor")
                       .config(NormalTaskConfig.builder()
                                               .params(Map.of("action", "analyze"))
                                               .build())
                       .build();
        dagEngine.addTask(analyzeTask);
        
        // 2. 质量评估条件任务 - 使用多个分支条件
        ConditionalTaskModel.BranchCondition excellentBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("excellent")
            .conditionExpression("DataProcessExecutor_status == 'excellent'")
            .description("优秀质量分支")
            .priority(1)
            .build();
            
        ConditionalTaskModel.BranchCondition goodBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("good")
            .conditionExpression("DataProcessExecutor_status == 'good'")
            .description("良好质量分支")
            .priority(2)
            .build();
            
        ConditionalTaskModel.BranchCondition highScoreBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("highScore")
            .conditionExpression("DataProcessExecutor_score > 80")
            .description("高分数分支")
            .priority(3)
            .build();

        BaseTaskModel qualityCheckTask = ConditionalTaskModel.builder()
                    .name("质量评估")
                    .executorKey("ConditionalTaskExecutor")
                    .branchConditions(List.of(excellentBranch, goodBranch, highScoreBranch))
                    .defaultBranch("standard")
                    .engineType(ConditionEngineType.JAVASCRIPT)
                    .build();
        dagEngine.addTask(qualityCheckTask);
        
        // 3. 优秀质量处理任务
        BaseTaskModel excellentProcessTask = NormalTaskModel.builder()
                .name("优秀质量处理")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "premium-process",
                            "branch", "excellent"))
                        .build())
                .build();
        dagEngine.addTask(excellentProcessTask);
        
        // 4. 良好质量处理任务
        BaseTaskModel goodProcessTask = NormalTaskModel.builder()
                .name("良好质量处理")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "standard-process",
                            "branch", "good"))
                        .build())
                .build();
        dagEngine.addTask(goodProcessTask);
        
        // 5. 高分数处理任务
        BaseTaskModel highScoreProcessTask = NormalTaskModel.builder()
                .name("高分数处理")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "bonus-process",
                            "branch", "highScore"))
                        .build())
                .build();
        dagEngine.addTask(highScoreProcessTask);
        
        // 6. 标准处理任务（默认分支）
        BaseTaskModel standardProcessTask = NormalTaskModel.builder()
                .name("标准处理")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "standard-process",
                            "branch", "standard"))
                        .build())
                .build();
        dagEngine.addTask(standardProcessTask);
        
        // 7. 类别分类条件任务 - 使用简单条件引擎
        ConditionalTaskModel.BranchCondition premiumCategoryBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("premium")
            .conditionExpression("DataProcessExecutor_category == 'premium'")
            .description("高级类别分支")
            .priority(1)
            .build();
            
        ConditionalTaskModel.BranchCondition standardCategoryBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("standardCategory")
            .conditionExpression("DataProcessExecutor_category == 'standard'")
            .description("标准类别分支")
            .priority(2)
            .build();

        BaseTaskModel categoryCheckTask = ConditionalTaskModel.builder()
                    .name("类别分类")
                    .executorKey("ConditionalTaskExecutor")
                    .branchConditions(List.of(premiumCategoryBranch, standardCategoryBranch))
                    .defaultBranch("basic")
                    .engineType(ConditionEngineType.SIMPLE)
                    .build();
        dagEngine.addTask(categoryCheckTask);
        
        // 8. 高级服务任务
        BaseTaskModel premiumServiceTask = NormalTaskModel.builder()
                .name("高级服务")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "premium-service",
                            "branch", "premium"))
                        .build())
                .build();
        dagEngine.addTask(premiumServiceTask);
        
        // 9. 标准服务任务
        BaseTaskModel standardServiceTask = NormalTaskModel.builder()
                .name("标准服务")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "standard-service",
                            "branch", "standardCategory"))
                        .build())
                .build();
        dagEngine.addTask(standardServiceTask);
        
        // 10. 基础服务任务（支持多个分支）
        BaseTaskModel basicServiceTask = NormalTaskModel.builder()
                .name("基础服务")
                .executorKey("DataProcessExecutor")
                .config(NormalTaskConfig.builder()
                        .params(Map.of(
                            "action", "basic-service",
                            "branch", "basic,standard")) // 支持多个分支
                        .build())
                .build();
        dagEngine.addTask(basicServiceTask);
        
        // 添加依赖关系
        // 主流程
        dagEngine.addDependency(analyzeTask.getId(), qualityCheckTask.getId());
        
        // 质量分支
        dagEngine.addDependency(qualityCheckTask.getId(), excellentProcessTask.getId());
        dagEngine.addDependency(qualityCheckTask.getId(), goodProcessTask.getId());
        dagEngine.addDependency(qualityCheckTask.getId(), highScoreProcessTask.getId());
        dagEngine.addDependency(qualityCheckTask.getId(), standardProcessTask.getId());
        
        // 类别分类 - 从优秀质量处理后进行
        dagEngine.addDependency(excellentProcessTask.getId(), categoryCheckTask.getId());
        
        // 服务分支
        dagEngine.addDependency(categoryCheckTask.getId(), premiumServiceTask.getId());
        dagEngine.addDependency(categoryCheckTask.getId(), standardServiceTask.getId());
        dagEngine.addDependency(categoryCheckTask.getId(), basicServiceTask.getId());
        
        // 使用CountDownLatch等待工作流完成
        CountDownLatch latch = new CountDownLatch(1);
        
        // 启动工作流
        workflowEngine.start()
            .doOnComplete(() -> {
                log.info("灵活分支工作流执行完成");
                latch.countDown();
            })
            .doOnError(error -> {
                log.error("灵活分支工作流执行失败", error);
                latch.countDown();
            })
            .subscribe();
        
        // 等待工作流完成
        latch.await();
        
        // 输出最终结果
        log.info("=== 工作流执行结果 ===");
        dagEngine.getTasks().values().forEach(task -> {
            log.info("任务: {} - 状态: {} - 输出: {}", 
                task.getName(), task.getStatus(), task.getTaskContext().getOutput());
        });
    }
} 