package com.mlc.ai.task.example;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.executor.AITaskExecutor;
import com.mlc.ai.task.context.AIContextExecutor;
import com.mlc.ai.task.executor.TaskExecutorRegistry;
import com.mlc.ai.task.manager.WorkflowManager;
import com.mlc.ai.task.model.AITaskModel;
import com.mlc.base.common.utils.ContextKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.openai.OpenAiChatModel;
import reactor.core.publisher.Flux;

import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * 灵活AI任务使用示例
 */
@Slf4j
public class NewAITaskExample {

    public static void main(String[] args) throws InterruptedException {
        // 创建基础组件
        OpenAiChatModel chatModel = createMockChatModel();
        
        // 创建执行器注册表
        TaskExecutorRegistry executorRegistry = createExecutorRegistry(chatModel);
        
        // 执行示例
        executeFlexibleAITaskWorkflow(executorRegistry);
    }

    /**
     * 创建执行器注册表
     */
    private static TaskExecutorRegistry createExecutorRegistry(OpenAiChatModel chatModel) {
        TaskExecutorRegistry registry = new TaskExecutorRegistry();
        
        // 1. 注册基础AI任务执行器
        registry.registerExecutor("BasicAITaskExecutor", new AITaskExecutor());
        
        // 2. 注册自定义的代码分析执行器
        registry.registerExecutor("CodeAnalysisExecutor", new CodeAnalysisAITaskExecutor());
        
        // 3. 注册自定义的文本摘要执行器
        registry.registerExecutor("TextSummaryExecutor", new TextSummaryAITaskExecutor());
        
        log.info("所有AI任务执行器注册完成");
        return registry;
    }

    /**
     * 执行灵活AI任务工作流
     */
    private static void executeFlexibleAITaskWorkflow(TaskExecutorRegistry executorRegistry) throws InterruptedException {
        WorkflowManager workflowManager = WorkflowManager.INSTANCE;
        
        // 创建工作流
        String workflowId = workflowManager.createWorkflow("灵活AI任务工作流", "展示不同类型的AI任务执行器");
        
        // 创建基础对话任务
        AITaskModel basicTask = createBasicChatTask();
        String basicTaskId = workflowManager.addTask(workflowId, basicTask);
        
        // 创建代码分析任务
        AITaskModel codeTask = createCodeAnalysisTask();
        String codeTaskId = workflowManager.addTask(workflowId, codeTask);
        
        // 创建文本摘要任务
        AITaskModel summaryTask = createTextSummaryTask();
        String summaryTaskId = workflowManager.addTask(workflowId, summaryTask);
        
        // 设置任务依赖关系
        workflowManager.addDependency(workflowId, basicTaskId, codeTaskId);
        workflowManager.addDependency(workflowId, codeTaskId, summaryTaskId);
        
        // 创建执行上下文
        ExecutionContext executionContext = ExecutionContext.getInstance();
        
        // 设置用户输入到上下文中
        executionContext.setAttribute(ContextKey.create("userInput"), "你好，请介绍一下你自己");
        
        // 执行工作流
        CountDownLatch latch = new CountDownLatch(1);
        
        Flux<String> executionResult = workflowManager.executeWorkflow(workflowId, executionContext);
        
        executionResult.subscribe(
            result -> log.info("执行结果: {}", result),
            error -> {
                log.error("执行失败", error);
                latch.countDown();
            },
            () -> {
                log.info("工作流执行完成");
                latch.countDown();
            }
        );
        
        // 等待执行完成
        latch.await();
    }

    /**
     * 创建基础对话任务
     */
    private static AITaskModel createBasicChatTask() {
        return AITaskModel.builder()
            .name("基础对话任务")
            .executorKey("BasicAITaskExecutor")
            .modelName("gpt-3.5-turbo")
            .chatClientConfig(AITaskModel.ChatClientConfig.builder()
                                                         .temperature(0.7)
                                                         .maxTokens(300)
                                                         .enableMemory(true)
                                                         .systemPrompt("你是一个友好的AI助手")
                                                         .sessionId("basic-session").build())
            .build();
    }

    /**
     * 创建代码分析任务
     */
    private static AITaskModel createCodeAnalysisTask() {
        return AITaskModel.builder()
            .name("代码分析任务")
            .executorKey("CodeAnalysisExecutor")
            .modelName("gpt-4")
            .userRawRequest("请分析以下Java代码的复杂度和性能：\npublic void bubbleSort(int[] arr) { ... }")
            .chatClientConfig(AITaskModel.ChatClientConfig.builder()
                                                         .temperature(0.3)
                                                         .maxTokens(800)
                                                         .enableMemory(false)
                                                         .systemPrompt("你是一个专业的代码分析师")
                                                         .sessionId("code-analysis-session")
                                                         .build())
            .build();
    }

    /**
     * 创建文本摘要任务
     */
    private static AITaskModel createTextSummaryTask() {
        return AITaskModel.builder()
            .name("文本摘要任务")
            .executorKey("TextSummaryExecutor")
            .modelName("gpt-3.5-turbo")
            .userRawRequest("请总结以下技术文档的要点：Spring Boot是一个基于Spring框架的快速开发工具...")
            .multipleResults(true) // 启用流式输出
            .chatClientConfig(AITaskModel.ChatClientConfig.builder()
                                         .temperature(0.5)
                                         .maxTokens(500)
                                         .enableMemory(false)
                                         .systemPrompt("你是一个专业的技术文档摘要专家")
                                         .sessionId("summary-session")
                                         .build())
            .build();
    }

    /**
     * 创建模拟的OpenAiChatModel
     */
    private static OpenAiChatModel createMockChatModel() {
        log.warn("使用模拟的ChatModel，实际使用时需要配置真实的OpenAiChatModel");
        return null;
    }

    /**
     * 自定义代码分析AI任务执行器
     */
    static class CodeAnalysisAITaskExecutor extends AITaskExecutor {

        @Override
        protected void justInLLMExecutor(AIContextExecutor llmExecutor, AITaskModel aiTask, ExecutionContext context) {
            // 自定义流处理：为代码分析添加特殊格式
            llmExecutor.withStreamProcessor(stream ->
                stream
                    .filter(content -> content != null && !content.trim().isEmpty())
                    .map(content -> {
                        if (content.contains("```")) {
                            return "💻 [代码块] " + content;
                        } else if (content.contains("复杂度") || content.contains("性能")) {
                            return "📊 [分析] " + content;
                        } else {
                            return "📝 [说明] " + content;
                        }
                    })
            );

            // 自定义结果处理：分析代码相关指标并保存到上下文
            llmExecutor.withResultProcessor((fullResult, task, ctx) -> {
                // 分析结果中的关键信息
                boolean hasComplexityAnalysis = fullResult.contains("复杂度") || fullResult.contains("O(");
                boolean hasPerformanceAnalysis = fullResult.contains("性能") || fullResult.contains("优化");
                boolean hasCodeBlocks = fullResult.contains("```");

                // 保存分析结果到任务上下文
                Map<String, Object> analysis = Map.of(
                    "hasComplexityAnalysis", hasComplexityAnalysis,
                    "hasPerformanceAnalysis", hasPerformanceAnalysis,
                    "hasCodeBlocks", hasCodeBlocks,
                    "analysisType", "code",
                    "timestamp", System.currentTimeMillis()
                );

                // 保存到任务上下文
                task.getTaskContext().getOutput().put("aiResult", fullResult);
                task.getTaskContext().getOutput().put("codeAnalysis", analysis);
                
                // 保存到执行上下文
                ctx.setAttribute(ContextKey.create("lastAIResult"), fullResult);
                ctx.setAttribute(ContextKey.create("lastCodeAnalysis"), fullResult);

                log.info("[{}] 代码分析结果已保存", ctx.getExecutionId());

                return String.format("代码分析完成 - 复杂度分析: %s, 性能分析: %s",
                    hasComplexityAnalysis ? "✅" : "❌",
                    hasPerformanceAnalysis ? "✅" : "❌");
            });
        }
    }

    /**
     * 自定义文本摘要AI任务执行器
     */
    static class TextSummaryAITaskExecutor extends AITaskExecutor {

        @Override
        protected void justInLLMExecutor(AIContextExecutor llmExecutor, AITaskModel aiTask, ExecutionContext context) {
            // 自定义流处理：为文本摘要添加实时统计
            llmExecutor.withStreamProcessor(stream ->
                stream.doOnNext(content -> {
                        // 实时统计字数
                        int wordCount = content.split("\\s+").length;
                        aiTask.getTaskContext().getOutput().put("currentWordCount", 
                            (Integer) aiTask.getTaskContext().getOutput().getOrDefault("currentWordCount", 0) + wordCount);
                    })
                    .map(content -> "📄 [摘要] " + content)
            );

            // 自定义结果处理：生成摘要统计并保存到上下文
            llmExecutor.withResultProcessor((fullResult, task, ctx) -> {
                // 统计摘要信息
                int wordCount = fullResult.split("\\s+").length;
                int sentenceCount = fullResult.split("[.!?]+").length;
                boolean hasBulletPoints = fullResult.contains("•") || fullResult.contains("-") || fullResult.contains("1.");
                
                // 保存摘要统计
                Map<String, Object> summaryStats = Map.of(
                    "wordCount", wordCount,
                    "sentenceCount", sentenceCount,
                    "hasBulletPoints", hasBulletPoints,
                    "summaryType", "text",
                    "timestamp", System.currentTimeMillis()
                );
                
                // 保存到任务上下文
                task.getTaskContext().getOutput().put("aiResult", fullResult);
                task.getTaskContext().getOutput().put("summaryStats", summaryStats);
                task.getTaskContext().getOutput().put("textSummaryResult", fullResult);
                
                // 保存到执行上下文
                ctx.setAttribute(ContextKey.create("lastAIResult"), fullResult);
                ctx.setAttribute(ContextKey.create("lastTextSummary"), fullResult);
                
                log.info("[{}] 文本摘要结果已保存", ctx.getExecutionId());

                return String.format("摘要生成完成 - 字数: %d, 句数: %d, 要点: %s", 
                    wordCount, sentenceCount, hasBulletPoints ? "✅" : "❌");
            });
        }
    }
} 