package com.mlc.ai.task.model;

import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 普通任务模型
 * 最基本的任务类型，通过执行器执行简单的业务逻辑
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class NormalTaskModel extends BaseTaskModel {
    
    @Builder.Default
    private NormalTaskConfig config = NormalTaskConfig.builder().build();
    
    @Override
    public TaskType getTaskType() {
        return TaskType.NORMAL;
    }
    
    @Override
    public NormalTaskConfig getConfig() {
        return config;
    }
    
    @Data
    @SuperBuilder
    @EqualsAndHashCode(callSuper = true)
    public static class NormalTaskConfig extends BaseTaskConfig {
    }
} 