package com.mlc.ai.task.model;

import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 脚本任务模型
 * 用于执行自定义脚本代码，支持多种脚本语言
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ScriptTaskModel extends BaseTaskModel {
    
    // ========== 核心脚本属性 ==========
    /**
     * 脚本语言
     */
    private ScriptLanguage language;
    
    /**
     * 脚本内容
     */
    private String script;
    
    /**
     * 脚本路径
     * 如果指定了路径，则从路径加载脚本，否则使用script字段
     */
    private String scriptPath;
    
    /**
     * 脚本参数
     * 传递给脚本的参数
     */
    private Map<String, Object> scriptParams;
    
    /**
     * 脚本任务配置
     */
    @Builder.Default
    private ScriptTaskConfig config = ScriptTaskConfig.builder().build();
    
    @Override
    public TaskType getTaskType() {
        return TaskType.SCRIPT;
    }
    
    @Override
    public ScriptTaskConfig getConfig() {
        return config;
    }
    
    /**
     * 脚本任务配置
     */
    @Data
    @SuperBuilder
    @EqualsAndHashCode(callSuper = true)
    public static class ScriptTaskConfig extends BaseTaskConfig {
        /**
         * 环境变量
         */
        private Map<String, String> environmentVars;
        
        /**
         * 工作目录
         */
        private String workingDirectory;
        
        /**
         * 执行超时时间
         */
        private long executionTimeoutMs;
    }
    
    /**
     * 脚本语言类型
     */
    public enum ScriptLanguage {
        JAVASCRIPT,    // JavaScript
        PYTHON,        // Python
        GROOVY,        // Groovy
        SHELL,         // Shell脚本
        SQL,           // SQL脚本
        BATCH,         // Windows批处理
        POWERSHELL     // PowerShell
    }
} 