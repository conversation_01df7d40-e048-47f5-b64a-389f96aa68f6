package com.mlc.ai.task.scheduler.strategy;

import com.mlc.ai.task.engine.TaskStatusEvent;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.scheduler.TaskScheduler;
import lombok.extern.slf4j.Slf4j;

/**
 * 默认的任务失败处理策略
 * 当任务失败时，不做特殊处理，整个工作流会因为失败而终止
 */
@Slf4j
public class DefaultFailureStrategy implements ITaskStatusStrategy {

    @Override
    public void justHandle(TaskStatusEvent changeEvent, TaskScheduler context) {
        log.info("默认失败策略：任务 {} 已失败，整个工作流将终止", changeEvent.getTaskId());
//        taskScheduler.setFailed();
    }

    @Override
    public boolean canHandle(TaskStatusEvent changeEvent) {
        // 只处理任务从RUNNING状态变为FAILED状态的情况
        return changeEvent.getOldStatus() == TaskStatus.RUNNING && changeEvent.getNewStatus() == TaskStatus.FAILED;
    }
} 