package com.mlc.ai.task.model;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * AI任务模型
 * 用于处理需要AI能力的任务
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class AITaskModel extends BaseTaskModel {

    // ========== 核心AI属性 ==========
    /**
     * 使用的模型名称
     */
    private String modelName;

    /**
     * 模型调用参数
     */
    @Builder.Default
    private Map<String, Object> modelParams = new HashMap<>();

    /**
     * 是否生成多结果（流式输出）
     */
    @Builder.Default
    private boolean multipleResults = false;

    /**
     * 用户输入
     */
    private String userRawRequest;

    // ========== AI配置对象 ==========
    /**
     * ChatClient 配置
     */
    @Builder.Default
    private ChatClientConfig chatClientConfig = ChatClientConfig.builder().build();

    /**
     * PromptModel 配置
     */
    @Builder.Default
    private PromptModelConfig promptModelConfig = PromptModelConfig.builder().build();

    /**
     * AI任务配置（基础配置）
     */
    @Builder.Default
    private AITaskConfig config = AITaskConfig.builder().build();

    @Override
    public TaskType getTaskType() {
        return TaskType.AI;
    }

    @Override
    public BaseTaskConfig getConfig() {
        return config;
    }

    /**
     * AI任务配置
     */
    @SuperBuilder
    public static class AITaskConfig extends BaseTaskConfig {
    }

    /**
     * ChatClient 配置类
     */
    @Data
    @Builder
    public static class ChatClientConfig {
        /**
         * 模型类型
         */
        private AiModelTypeEnum modelType;

        /**
         * 模型温度参数 (0.0-2.0)
         */
        @Builder.Default
        private Double temperature = 0.7;

        /**
         * 最大令牌数
         */
        private Integer maxTokens;

        /**
         * Top-p 参数
         */
        private Double topP;

        /**
         * 频率惩罚
         */
        private Double frequencyPenalty;

        /**
         * 存在惩罚
         */
        private Double presencePenalty;

        /**
         * 是否启用内存对话
         */
        @Builder.Default
        private boolean enableMemory = true;

        /**
         * 是否启用日志记录
         */
        @Builder.Default
        private boolean enableLogging = true;

        /**
         * 会话ID（用于内存对话）
         */
        private String sessionId;

        /**
         * 自定义系统提示词
         */
        private String systemPrompt;

        /**
         * 额外的 ChatClient 选项
         */
        @Builder.Default
        private Map<String, Object> additionalOptions = new HashMap<>();
    }

    /**
     * PromptModel 配置类
     */
    @Data
    @Builder
    public static class PromptModelConfig {
        /**
         * 提示模型的键/路径
         */
        private String promptPath;

        /**
         * 输入参数映射
         */
        @Builder.Default
        private Map<String, String> inputMapping = new HashMap<>();

        /**
         * 输出参数映射
         */
        @Builder.Default
        private Map<String, String> outputMapping = new HashMap<>();

        /**
         * 是否需要JSON格式输出
         */
        @Builder.Default
        private boolean requireJsonOutput = false;

        /**
         * 额外的提示参数
         */
        @Builder.Default
        private Map<String, Object> promptParams = new HashMap<>();
    }
} 