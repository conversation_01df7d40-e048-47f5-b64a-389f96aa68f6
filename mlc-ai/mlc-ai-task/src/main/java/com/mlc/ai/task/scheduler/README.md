# TaskScheduler 重构说明

## 概述

TaskScheduler 已经进行了抽象和封装重构，将原来的单一类拆分为多个专门的管理器组件，提高了代码的可维护性和可扩展性。

## 架构改进

### 1. 组件分离

原来的 TaskScheduler 承担了太多职责，现在拆分为：

- **TaskScheduler**: 主调度器，协调各个组件
- **SchedulerStateManager**: 状态管理器，管理调度器状态和生命周期
- **TaskQueueManager**: 队列管理器，管理任务队列和运行计数
- **TaskDependencyChecker**: 依赖检查器，检查任务依赖关系
- **TaskSchedulerFactory**: 工厂类，提供便捷的创建方法

### 2. 职责明确

每个组件都有明确的职责：

#### SchedulerStateManager
- 管理调度器状态（IDLE, RUNNING, COMPLETED, FAILED, CANCELLED）
- 处理状态变化事件
- 管理完成回调
- 提供状态查询方法

#### TaskQueueManager
- 管理准备执行的任务队列
- 维护运行中的任务计数
- 提供任务选择和队列操作方法
- 与调度策略协作选择下一个执行的任务

#### TaskDependencyChecker
- 检查任务依赖关系
- 确定后继任务是否可以执行
- 提供任务状态统计
- 处理任务取消操作

## 使用方式

### 1. 基本使用

```java
// 使用默认配置
TaskScheduler scheduler = TaskSchedulerFactory.createDefault(
    dagEngine, workflowEngine, executionContext);

// 启动调度
Flux<String> result = scheduler.start();
```

### 2. 自定义配置

```java
// 高并发配置
TaskScheduler scheduler = TaskSchedulerFactory.createHighConcurrency(
    dagEngine, workflowEngine, executionContext, 20);

// 串行执行
TaskScheduler scheduler = TaskSchedulerFactory.createSerial(
    dagEngine, workflowEngine, executionContext);

// 快速失败模式
TaskScheduler scheduler = TaskSchedulerFactory.createFailFast(
    dagEngine, workflowEngine, executionContext);
```

### 3. 自定义策略

```java
ITaskSchedulerPolicy customPolicy = DefaultTaskSchedulerPolicy.builder()
    .maxParallelism(15)
    .continueOnFailure(true)
    .build();

TaskScheduler scheduler = TaskSchedulerFactory.createWithPolicy(
    dagEngine, workflowEngine, executionContext, customPolicy);
```

### 4. 状态监控

```java
// 获取调度器状态
SchedulerStatus status = scheduler.getStatus();

// 获取统计信息
String stats = scheduler.getSchedulerStats();

// 注册完成回调
scheduler.registerComplete(() -> {
    System.out.println("调度完成！");
});
```

## 扩展性

### 1. 自定义调度策略

实现 `ITaskSchedulerPolicy` 接口：

```java
public class CustomSchedulerPolicy implements ITaskSchedulerPolicy {
    @Override
    public BaseTaskModel selectNextTask(List<BaseTaskModel> readyTasks) {
        // 自定义任务选择逻辑
        return readyTasks.stream()
            .min(Comparator.comparing(task -> task.getEstimatedDuration()))
            .orElse(null);
    }
    
    @Override
    public int getMaxParallelism() {
        return Runtime.getRuntime().availableProcessors();
    }
    
    @Override
    public boolean shouldContinueScheduling(BaseTaskModel failedTask) {
        // 根据任务类型决定是否继续
        return !failedTask.isCritical();
    }
}
```

### 2. 扩展管理器组件

各个管理器组件都可以独立扩展：

- 可以为 `TaskQueueManager` 添加优先级队列
- 可以为 `SchedulerStateManager` 添加状态持久化
- 可以为 `TaskDependencyChecker` 添加复杂的依赖规则

## 优势

1. **单一职责**: 每个组件只负责一个方面的功能
2. **易于测试**: 可以独立测试每个组件
3. **易于扩展**: 可以独立扩展或替换组件
4. **配置灵活**: 通过工厂类提供多种预设配置
5. **状态清晰**: 状态管理更加明确和可控

## 向后兼容

重构保持了原有的公共API，现有代码无需修改即可使用新的实现。

## 性能优化

- 减少了状态检查的复杂度
- 优化了任务队列操作
- 改进了依赖检查的效率
- 减少了不必要的对象创建
