package com.mlc.ai.task.scheduler.policy;

import com.mlc.ai.task.model.basic.BaseTaskModel;
import java.util.Comparator;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * 默认任务调度策略实现
 */
@Getter
@Builder
public class DefaultTaskSchedulerPolicy implements ITaskSchedulerPolicy {

    /**
     * 最大并行任务数
     */
    @Builder.Default
    private final int maxParallelism = 10;
    
    /**
     * 是否在任务失败时继续调度
     */
    @Builder.Default
    private final boolean continueOnFailure = true;
    
    @Override
    public BaseTaskModel selectNextTask(List<BaseTaskModel> readyTasks) {
        if (readyTasks == null || readyTasks.isEmpty()) {
            return null;
        }

        // 根据 task Priority进行排序
        readyTasks.sort(Comparator.comparing(task -> task.getConfig().getPriority()));
        return readyTasks.get(0);
    }
    
    @Override
    public boolean shouldContinueScheduling(BaseTaskModel failedTask) {
        return continueOnFailure;
    }
} 