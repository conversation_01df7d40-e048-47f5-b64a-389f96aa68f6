package com.mlc.ai.task.executor;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务执行器注册表
 * 负责管理所有任务执行器的注册和发现
 */
@Slf4j
public class TaskExecutorRegistry {

    /**
     * 执行器注册表
     * 执行器类型 -> 执行器实例
     */
    private final Map<String, ITaskExecutor> executorMap = new ConcurrentHashMap<>();

    /**
     * 注册任务执行器
     * 
     * @param executorKey 执行器键
     * @param executor 执行器实例
     */
    public void registerExecutor(String executorKey, ITaskExecutor executor) {
        executorMap.computeIfAbsent(executorKey, key -> executor);
        log.info("注册任务执行器: {}", executorKey);
    }
    
    /**
     * 获取任务执行器
     * 
     * @param executorKey 执行器键
     * @return 执行器实例，如果不存在则返回null
     */
    public ITaskExecutor getExecutor(String executorKey) {
        ITaskExecutor executor = executorMap.get(executorKey);
        if (executor == null) {
            log.warn("找不到任务执行器: {}", executorKey);
        }
        return executor;
    }
    
    /**
     * 取消注册任务执行器
     * 
     * @param executorKey 执行器键
     * @return 是否成功取消注册
     */
    public boolean unregisterExecutor(String executorKey) {
        ITaskExecutor removed = executorMap.remove(executorKey);
        if (removed != null) {
            log.info("取消注册任务执行器: {}", executorKey);
            return true;
        }
        return false;
    }

    /**
     * 获取所有已注册的执行器
     * 
     * @return 执行器映射表的副本
     */
    public Map<String, ITaskExecutor> getAllExecutors() {
        return new HashMap<>(executorMap);
    }
}