package com.mlc.ai.task.engine;

import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import lombok.Getter;

/**
 * 任务状态变更事件
 */
@Getter
public class TaskStatusEvent {
    private final String taskId;
    private final TaskStatus oldStatus;
    private final TaskStatus newStatus;
    private final long timestamp;

    public TaskStatusEvent(String taskId, TaskStatus oldStatus, TaskStatus newStatus) {
        this.taskId = taskId;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.timestamp = System.currentTimeMillis();
    }
} 