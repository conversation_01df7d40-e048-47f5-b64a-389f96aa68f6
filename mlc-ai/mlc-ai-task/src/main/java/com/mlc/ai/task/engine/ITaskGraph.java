package com.mlc.ai.task.engine;

import com.mlc.ai.task.model.basic.BaseTaskModel;

import java.util.List;
import java.util.Set;

/**
 * 任务图接口，定义任务依赖关系图的基本操作
 */
public interface ITaskGraph {
    
    /**
     * 添加任务
     *
     * @param task 要添加的任务
     * @return 任务ID
     */
    String addTask(BaseTaskModel task);
    
    /**
     * 添加任务依赖关系
     *
     * @param fromTaskId 前置任务ID
     * @param toTaskId 后置任务ID
     * @return 是否添加成功
     */
    boolean addDependency(String fromTaskId, String toTaskId);
    
    /**
     * 移除任务依赖关系
     *
     * @param fromTaskId 前置任务ID
     * @param toTaskId 后置任务ID
     * @return 是否移除成功
     */
    boolean removeDependency(String fromTaskId, String toTaskId);
    
    /**
     * 获取任务的前置任务
     *
     * @param taskId 任务ID
     * @return 前置任务ID集合
     */
    Set<String> getPredecessors(String taskId);
    
    /**
     * 获取任务的后置任务
     *
     * @param taskId 任务ID
     * @return 后置任务ID集合
     */
    Set<String> getSuccessors(String taskId);
    
    /**
     * 获取入度为0的任务（起始任务）
     *
     * @return 入度为0的任务ID集合
     */
    Set<String> getEntryTasks();
    
    /**
     * 获取出度为0的任务（终止任务）
     *
     * @return 出度为0的任务ID集合
     */
    Set<String> getExitTasks();
    
    /**
     * 验证任务图是否有效（无环）
     *
     * @return 如果有效返回true，否则返回false
     */
    boolean isValid();
    
    /**
     * 获取拓扑排序结果
     *
     * @return 按拓扑顺序排列的任务ID列表
     */
    List<String> getTopologicalOrder();
    
    /**
     * 获取从起点任务可达的所有任务
     *
     * @param startTaskId 起点任务ID
     * @return 可达任务ID集合
     */
    Set<String> getReachableTasks(String startTaskId);
} 