package com.mlc.ai.task.model.basic;

import com.mlc.ai.core.util.BranchConfigBuilder.BranchConfig;
import lombok.Data;
import lombok.Builder;
import lombok.experimental.SuperBuilder;

import java.util.Map;
import java.util.UUID;

/**
 * 基础任务模型抽象类
 * 表示一个独立的处理单元，不包含任务间依赖关系，也不包含父子任务关系
 * 单一职责：TaskModel 仅关注自身执行，不感知上下游。
 */
@Data
@SuperBuilder
public abstract class BaseTaskModel {
    /**
     * 任务唯一标识
     */
    @Builder.Default
    private final String id = UUID.randomUUID().toString();
    
    /**
     * 任务名称
     */
    private final String name;
    
    /**
     * 任务状态
     */
    @Builder.Default
    private TaskStatus status = TaskStatus.PENDING;

    /**
     * 任务执行器的唯一标识
     * 通过 executorKey 来动态选择执行器实例，例如：executorKey = "com.mlc.ai.task.executor.BatchTaskExecutor"
     */
    private String executorKey;
    
    /**
     * 任务上下文
     */
    @Builder.Default
    private TaskContext taskContext = TaskContext.builder().build();
    
    /**
     * 获取任务类型
     * 每个子类必须实现该方法返回对应的任务类型
     * 
     * @return 任务类型
     */
    public abstract TaskType getTaskType();
    
    /**
     * 获取任务配置
     * 每个子类必须实现该方法返回对应的任务配置
     * 
     * @return 任务配置
     */
    public abstract BaseTaskConfig getConfig();
    
    /**
     * 任务状态枚举
     * 事件驱动：任务状态变更通过事件通知 DAG 引擎
     */
    public enum TaskStatus {
        PENDING,    // 初始状态
        RUNNING,    // 执行中
        COMPLETED,  // 成功
        FAILED,     // 失败（可重试）
        CANCELLED,  // 取消
        SKIPPED,    // 跳过（条件不满足）
        TIMEOUT,    // 超时
        WAITING_USER_ACTION  // 等待用户操作（人工任务特有）
    }

    /**
     * 任务类型枚举
     * 代表了系统支持的任务类型
     */
    public enum TaskType {
        NORMAL,         // 普通任务
        CONDITIONAL,    // 条件分支任务
        SUB_WORKFLOW,   // 子工作流任务
        MANUAL,         // 人工任务
        EMAIL,          // 邮件任务
        HTTP,           // HTTP调用任务
        SCRIPT,         // 脚本执行任务
        AI              // AI任务
    }
    
    /**
     * 任务配置基类
     * 所有任务配置类的父类，提供通用的配置属性
     */
    @Data
    @SuperBuilder
    public static abstract class BaseTaskConfig {
        /**
         * 任务重试次数
         */
        @Builder.Default
        private int maxRetries = 0;
        
        /**
         * 超时时间(毫秒)
         */
        @Builder.Default
        private long timeoutMs = 0;

        /**
         * 优先级
         */
        private int priority;

        /**
         * 任务差异化参数
         */
        @Builder.Default
        private Map<String, String> params = null;
        
        /**
         * 分支配置
         */
        @Builder.Default
        private BranchConfig branchConfig = null;
    }
}