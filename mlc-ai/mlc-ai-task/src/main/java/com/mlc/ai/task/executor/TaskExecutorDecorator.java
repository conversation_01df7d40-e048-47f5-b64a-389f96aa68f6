package com.mlc.ai.task.executor;

import com.google.common.base.Strings;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 任务执行器装饰器
 * 负责统一处理任务执行过程中的消息格式化
 */
@Slf4j
public class TaskExecutorDecorator implements ITaskExecutor {
    
    private final ITaskExecutor delegateExecutor;
    
    public TaskExecutorDecorator(ITaskExecutor delegateExecutor) {
        this.delegateExecutor = delegateExecutor;
    }
    
    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
        String taskName = task.getName();
        String executionId = context.getExecutionId();
        
        log.debug("[{}] TaskExecutorDecorator 开始装饰任务: {}", executionId, taskName);
        
        return Flux.concat(
            // 开始消息
            Flux.just(this.formatStartMessage(taskName)),
            
            // 委托给实际的执行器，并处理其输出
            delegateExecutor.execute(task, context)
//                .map(this::formatProgressMessage)
                .onErrorResume(error -> 
                    Flux.just(this.formatErrorMessage(taskName, error))
                )
        );
    }
    
    /**
     * 格式化任务开始消息
     */
    private String formatStartMessage(String taskName) {
        return String.format("📌 开始执行任务: %s \n", taskName);
    }
    
    /**
     * 格式化任务进度消息
     * 对于普通文本，添加适当的前缀
     */
    private String formatProgressMessage(String content) {
        if (Strings.isNullOrEmpty(content)) {
            return content;
        }

        // 对于普通文本，添加进度标识
        return "▶️ " + content;
    }
    
    /**
     * 格式化任务错误消息
     */
    private String formatErrorMessage(String taskName, Throwable error) {
        String errorMsg = error.getCause() != null ? 
            error.getCause().getMessage() : error.getMessage();
        return String.format("\n ❌ 任务执行失败: %s - %s", taskName, errorMsg);
    }
}
