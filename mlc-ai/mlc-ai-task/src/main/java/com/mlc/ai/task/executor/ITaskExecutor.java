package com.mlc.ai.task.executor;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import reactor.core.publisher.Flux;

/**
 * 任务执行器接口
 * 所有自定义任务执行器都必须实现此接口
 */
@FunctionalInterface
public interface ITaskExecutor {

    /**
     * 执行任务
     *
     * @param task    要执行的任务
     * @param context 执行上下文
     * @return 执行结果流
     */
    Flux<String> execute(BaseTaskModel task, ExecutionContext context);
} 