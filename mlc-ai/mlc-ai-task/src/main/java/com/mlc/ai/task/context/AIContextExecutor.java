package com.mlc.ai.task.context;

import com.google.common.base.Strings;
import com.mlc.ai.core.util.code.CodeBlockExtractor;
import com.mlc.ai.task.cache.ChatClientCacheManager;
import com.mlc.ai.task.model.AITaskModel;
import com.mlc.base.common.utils.ContextKey;
import io.nop.ai.core.model.ModelBasedPromptTemplate;
import io.nop.ai.core.model.PromptModel;
import io.nop.ai.core.prompt.node.IPromptSyntaxNode.IPromptSyntaxNodeVisitor;
import io.nop.ai.core.prompt.node.PromptSyntaxParser;
import io.nop.core.lang.eval.IEvalScope;
import io.nop.core.resource.component.ResourceComponentManager;
import io.nop.xlang.api.XLang;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.function.Function;

/**
 * LLM 上下文执行器
 */
@Slf4j
public class AIContextExecutor {

    /**
     * ChatClient 缓存管理器
     */
    private final ChatClientCacheManager chatClientCacheManager;

    /**
     * 流处理器函数
     */
    private Function<Flux<String>, Flux<String>> streamProcessor = flux -> flux;

    /**
     * 结果处理器函数（同时负责结果处理和上下文保存）
     */
    private ResultProcessor resultProcessor = (fullResult, aiTask, context) -> fullResult;

    public AIContextExecutor(ChatClientCacheManager chatClientCacheManager) {
        this.chatClientCacheManager = chatClientCacheManager;
    }

    /**
     * 执行 AI 任务
     */
    public Flux<String> execute(AITaskModel aiTask, ExecutionContext context) {
        try {
            String userInput = prepareUserInput(aiTask, context);
            String systemPrompt = prepareSystemPrompt(aiTask);
            ChatClient chatClient = chatClientCacheManager.getChatClient(aiTask.getChatClientConfig());

            log.info("[{}] AI任务配置 - 模型: {}, 温度: {}", context.getExecutionId(), aiTask.getModelName(), aiTask.getChatClientConfig().getTemperature());

            ChatClient.ChatClientRequestSpec requestSpec = chatClient.prompt();
            if (!Strings.isNullOrEmpty(systemPrompt)) {
                requestSpec = requestSpec.system(systemPrompt);
            }
            final ChatClient.ChatClientRequestSpec finalRequestSpec = requestSpec.user(userInput);

            // 根据是否流式处理选择不同的数据源
            Flux<String> resultStream = aiTask.isMultipleResults()
                ? requestSpec.stream().content()
                : Mono.fromCallable(() -> finalRequestSpec.call().content())
                      .subscribeOn(Schedulers.boundedElastic())
                      .flux();

            return resultStream
                .transform(streamProcessor)  // 应用流处理器
                .scan(new StringBuilder(), StringBuilder::append)  // 累积内容
                .map(StringBuilder::toString)
                .takeLast(1)  // 只取最后一个完整结果
                .flatMap(fullResult -> {
                    String processedMessage = resultProcessor.process(fullResult, aiTask, context);
                    return Flux.just(String.format("✅ AI任务完成: %s \n", processedMessage));
                }).onErrorResume(e -> {
                    log.error("[{}] AI任务执行失败: {}", context.getExecutionId(), aiTask.getName(), e);
                    return Flux.error(new RuntimeException("AI任务执行失败: " + e.getMessage()));
                });
        } catch (Exception e) {
            log.error("[{}] AI任务准备阶段失败: {}", context.getExecutionId(), aiTask.getName(), e);
            return Flux.error(new RuntimeException("AI任务准备阶段失败: " + e.getMessage()));
        }
    }

    /**
     * 准备用户输入
     */
    private String prepareUserInput(AITaskModel aiTask, ExecutionContext context) {
        String userInput = aiTask.getUserRawRequest();

        // 如果没有提供用户输入，则从上下文中获取
        if (Strings.isNullOrEmpty(userInput)) {
            userInput = context.getAttribute(ContextKey.create("previousTaskRequest"));
        }

        if (Strings.isNullOrEmpty(userInput)) {
            throw new RuntimeException("用户输入不能为空");
        }
        
        return userInput;
    }

    /**
     * 准备系统提示词
     */
    private String prepareSystemPrompt(AITaskModel aiTaskModel) {
        if (aiTaskModel.getChatClientConfig().getSystemPrompt() != null) {
            return aiTaskModel.getChatClientConfig().getSystemPrompt();
        }

        AITaskModel.PromptModelConfig promptModelConfig = aiTaskModel.getPromptModelConfig();
        if (promptModelConfig.getPromptPath() == null) {
            return null;
        }

        PromptModel promptModel = (PromptModel) ResourceComponentManager.instance().loadComponentModel(promptModelConfig.getPromptPath());
        IEvalScope iEvalScope = new ModelBasedPromptTemplate(promptModel).prepareInputs(
                                aiTaskModel.getPromptModelConfig().getPromptParams(), XLang.newEvalScope());
        StringBuilder sb = new StringBuilder();
        promptModel.getTemplate().accept(new IPromptSyntaxNodeVisitor() {
            @Override
            public void visitText(PromptSyntaxParser.TextNode expr) {
                sb.append(expr.getText());
            }

            @Override
            public void visitVariable(PromptSyntaxParser.VariableNode expr) {
                String name = expr.getVarName();
                Object value = iEvalScope.getValueByPropPath(name);
                if (value != null) sb.append(value);
            }
        });
        return sb.toString();
    }

    /**
     * 设置流处理器
     */
    public AIContextExecutor withStreamProcessor(Function<Flux<String>, Flux<String>> processor) {
        this.streamProcessor = processor;
        return this;
    }

    /**
     * 设置结果处理器
     */
    public AIContextExecutor withResultProcessor(ResultProcessor processor) {
        this.resultProcessor = processor;
        return this;
    }

    /**
     * 预定义的代码块提取流处理器
     * 如果内容包含代码块，提取第一个代码块；否则返回原始内容
     *
     * @return 流处理器函数
     */
    public static Flux<String> codeBlockExtractorProcessor(Flux<String> flux) {
        return flux.map(content -> {
            var codeBlock = CodeBlockExtractor.parseContent(content);
            if (codeBlock.getCodeBlocks().isEmpty()) {
                // 如果没有代码块，返回原始内容（可能是进度信息或其他内容）
                return content;
            } else {
                // 如果有代码块，返回第一个代码块的内容
                return codeBlock.getCodeBlocks().get(0).getCode();
            }
        });
    }

    /**
     * 结果处理器接口
     * 负责处理AI结果，返回处理消息
     */
    @FunctionalInterface
    public interface ResultProcessor {
        /**
         * 处理AI结果
         * 
         * @param fullResult AI返回的完整结果
         * @param aiTask AI任务
         * @param context 执行上下文
         * @return 处理完成的消息
         */
        String process(String fullResult, AITaskModel aiTask, ExecutionContext context);
    }
} 