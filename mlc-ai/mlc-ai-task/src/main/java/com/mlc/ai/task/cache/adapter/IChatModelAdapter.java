package com.mlc.ai.task.cache.adapter;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.task.model.AITaskModel.ChatClientConfig;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;

/**
 * ChatModel 适配器接口
 * 用于抽象不同模型的配置逻辑，支持扩展新的模型类型
 */
public interface IChatModelAdapter {

    /**
     * 判断是否支持指定的模型类型
     * 
     * @param modelType 模型类型
     * @param chatModel ChatModel 实例
     * @return 是否支持
     */
    boolean supports(AiModelTypeEnum modelType, ChatModel chatModel);

    /**
     * 配置 ChatClient 选项
     * 
     * @param builder ChatClient 构建器
     * @param chatModel ChatModel 实例
     * @param config 配置信息
     */
    void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config);

    /**
     * 获取适配器优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取适配器名称
     * 
     * @return 适配器名称
     */
    String getAdapterName();
} 