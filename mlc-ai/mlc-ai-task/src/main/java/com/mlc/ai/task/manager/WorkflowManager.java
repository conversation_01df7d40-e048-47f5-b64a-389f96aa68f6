package com.mlc.ai.task.manager;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.workflow.WorkflowEngine;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工作流管理器
 * 负责工作流的创建、存储、查询和执行
 * 作为系统对外的主要接口，提供高层次的工作流管理功能
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WorkflowManager {

    public static final WorkflowManager INSTANCE = new WorkflowManager();

    /**
     * 工作流定义表
     * 工作流ID -> DAG引擎
     */
    private final Map<String, DagInMemoryEngine> dagEngineInstances = new ConcurrentHashMap<>();

    /**
     * 正在执行的工作流实例
     * 执行ID -> 工作流引擎
     */
    private final Map<String, WorkflowEngine> runningInstances = new ConcurrentHashMap<>();


    /**
     * 创建新的工作流
     *
     * @param name        工作流名称
     * @param description 工作流描述
     * @return 新创建的工作流ID
     */
    public String createWorkflow(String name, String description) {
        DagInMemoryEngine dagEngine = DagInMemoryEngine.builder().name(name).description(description).build();

        String dagEngineId = dagEngine.getEngineId();
        dagEngineInstances.put(dagEngineId, dagEngine);
        log.info("创建工作流: {}, ID: {}", name, dagEngineId);
        return dagEngineId;
    }

    /**
     * 获取工作流定义
     *
     * @param dagEngineId 工作流ID
     * @return 工作流定义
     */
    public DagInMemoryEngine getWorkflow(String dagEngineId) {
        return dagEngineInstances.get(dagEngineId);
    }

    /**
     * 向工作流添加任务
     *
     * @param dagEngineId 工作流ID
     * @param task       任务
     * @return 添加的任务ID
     */
    public String addTask(String dagEngineId, BaseTaskModel task) {
        DagInMemoryEngine dagEngine = dagEngineInstances.get(dagEngineId);
        if (dagEngine == null) {
            throw new IllegalArgumentException("工作流不存在: " + dagEngineId);
        }

        return dagEngine.addTask(task);
    }

    /**
     * 添加任务依赖关系
     *
     * @param dagEngineId  工作流ID
     * @param fromTaskId  源任务ID
     * @param toTaskId    目标任务ID
     * @return 是否添加成功
     */
    public boolean addDependency(String dagEngineId, String fromTaskId, String toTaskId) {
        DagInMemoryEngine dagEngine = dagEngineInstances.get(dagEngineId);
        if (dagEngine == null) {
            throw new IllegalArgumentException("工作流不存在: " + dagEngineId);
        }

        return dagEngine.addDependency(fromTaskId, toTaskId);
    }

    /**
     * 动态向执行中的工作流添加任务
     *
     * @param executionId    执行ID
     * @param task           任务
     * @param predecessorIds 前驱任务ID集合
     * @param successorIds   后继任务ID集合
     * @return 添加的任务ID
     */
    public String dynamicAddTask(String executionId, BaseTaskModel task, Set<String> predecessorIds, Set<String> successorIds) {
        WorkflowEngine workflowEngine = runningInstances.get(executionId);
        if (workflowEngine == null) {
            throw new IllegalArgumentException("执行实例不存在: " + executionId);
        }

        DagInMemoryEngine dagEngine = workflowEngine.getDagEngine();
        return dagEngine.dynamicAddTask(task, predecessorIds, successorIds);
    }


    /**
     * 内部执行工作流方法
     * 
     * @param dagEngineId 工作流ID
     * @param executionContext 执行上下文
     * @return 执行结果流
     */
    public Flux<String> executeWorkflow(String dagEngineId, ExecutionContext executionContext) {

        if (executionContext == null) {
            return Flux.error(new IllegalArgumentException("执行上下文不能为空"));
        }
        DagInMemoryEngine dagEngine = dagEngineInstances.get(dagEngineId);
        if (dagEngine == null) {
            return Flux.error(new IllegalArgumentException("工作流不存在: " + dagEngineId));
        }

        if (!dagEngine.isValid()) {
            return Flux.error(new IllegalArgumentException("工作流包含循环依赖，无法执行"));
        }

        // 创建工作流引擎
        WorkflowEngine workflowEngine = new WorkflowEngine(dagEngine, executionContext);

        String executionId = executionContext.getExecutionId();
        // 保存执行实例
        runningInstances.put(executionId, workflowEngine);

        // 输出执行计划
        this.generateExecutionPlan(dagEngineId).forEach(taskId ->
                log.info("工作流执行计划: {} -> {}", executionId, taskId));

        // 执行完成后清理资源
        Flux<String> resultFlux = workflowEngine.start()
                .doFinally(signal -> {
                    log.info("工作流执行完成: {}, executionId: {}", dagEngine.getName(), executionId);
                    runningInstances.remove(executionId);
                });

        return Flux.concat(
                Flux.just(String.format("🔥 启动工作流: %s, 运行实例编码: %s \n", dagEngine.getName(), executionId)),
                resultFlux
        );
    }

    /**
     * 取消工作流执行
     *
     * @param executionId 执行ID
     * @return 是否取消成功
     */
    public boolean cancelExecution(String executionId) {
        WorkflowEngine workflowEngine = runningInstances.get(executionId);
        if (workflowEngine == null) {
            log.warn("要取消的工作流执行实例不存在: {}", executionId);
            return false;
        }

        workflowEngine.cancel();
        log.info("取消工作流执行: {}", executionId);
        return true;
    }

    /**
     * 删除工作流定义
     *
     * @param dagEngineId 工作流ID
     * @return 是否删除成功
     */
    public boolean deleteWorkflow(String dagEngineId) {
        // 检查是否有正在运行的实例
        boolean hasRunningInstance = runningInstances.values().stream()
                .anyMatch(engine -> engine.getDagEngine().getEngineId().equals(dagEngineId));

        if (hasRunningInstance) {
            log.warn("工作流有正在运行的实例，无法删除: {}", dagEngineId);
            return false;
        }

        DagInMemoryEngine removed = dagEngineInstances.remove(dagEngineId);
        if (removed != null) {
            log.info("删除工作流: {}", dagEngineId);
            return true;
        }
        return false;
    }

    /**
     * 生成工作流执行计划
     *
     * @param dagEngineId 工作流ID
     * @return 执行计划（按拓扑顺序排列的任务ID列表）
     */
    public List<String> generateExecutionPlan(String dagEngineId) {
        DagInMemoryEngine dagEngine = dagEngineInstances.get(dagEngineId);
        if (dagEngine == null) {
            throw new IllegalArgumentException("工作流不存在: " + dagEngineId);
        }

        return dagEngine.getTopologicalOrder();
    }
}