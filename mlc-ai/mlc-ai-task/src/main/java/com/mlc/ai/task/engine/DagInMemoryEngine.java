package com.mlc.ai.task.engine;

import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.jgrapht.Graph;
import org.jgrapht.graph.DirectedAcyclicGraph;
import org.jgrapht.graph.DefaultEdge;
import org.jgrapht.traverse.TopologicalOrderIterator;

/**
 * DAG引擎 - 拓扑管理&事件源
 * 负责:
 * 1. 维护任务拓扑结构
 * 2. 生成任务调度计划
 * 3. 管理任务依赖关系
 * 4. 支持运行时动态调整DAG结构
 * 5. 支持事件驱动的任务调度
 */
@Slf4j
@Getter
@Builder
public class DagInMemoryEngine implements ITaskGraph {

    /**
     * DAG引擎唯一标识
     */
    @Builder.Default
    private final String engineId = UUID.randomUUID().toString();

    /**
     * DAG引擎名称
     */
    private final String name;

    /**
     * DAG引擎描述
     */
    private String description;

    /**
     * JGraphT DAG图实例
     */
    @Builder.Default
    private final Graph<String, DefaultEdge> graph = new DirectedAcyclicGraph<>(DefaultEdge.class);

    /**
     * 所有任务集合
     * 任务ID -> 任务对象
     */
    @Builder.Default
    private final Map<String, BaseTaskModel> tasks = new ConcurrentHashMap<>();

    /**
     * 任务状态监听器
     * 监听任务状态变化，用于事件驱动的任务调度
     */
    @Builder.Default
    private final List<Consumer<TaskStatusEvent>> statusListeners = new CopyOnWriteArrayList<>();

    /**
     * 读写锁，用于保护DAG结构的修改操作
     */
    private final ReentrantReadWriteLock dagLock = new ReentrantReadWriteLock();

    // 同步执行图形操作，确保写锁
    private <T> T executeGraphOperation(Supplier<T> operation) {
        dagLock.writeLock().lock();
        try {
            return operation.get();
        } finally {
            dagLock.writeLock().unlock();
        }
    }

    // 同步执行图形读操作，确保读锁
    private <T> T executeGraphReadOperation(Supplier<T> operation) {
        dagLock.readLock().lock();
        try {
            return operation.get();
        } finally {
            dagLock.readLock().unlock();
        }
    }

    /**
     * 添加任务到DAG
     *
     * @param task 要添加的任务
     * @return 添加的任务ID
     */
    @Override
    public String addTask(BaseTaskModel task) {
        return executeGraphOperation(() -> {
            String taskId = task.getId();
            tasks.put(taskId, task);
            graph.addVertex(taskId);
            log.debug("添加任务:{} 到 dag:{}", taskId, name);
            return taskId;
        });
    }

    /**
     * 添加任务间的依赖关系
     *
     * @param fromTaskId 源任务ID
     * @param toTaskId   目标任务ID
     * @return 是否添加成功
     */
    @Override
    public boolean addDependency(String fromTaskId, String toTaskId) {
        return executeGraphOperation(() -> {
            if (!tasks.containsKey(fromTaskId) || !tasks.containsKey(toTaskId)) {
                log.warn("无法添加依赖项：DAG 中不存在此任务");
                return false;
            }

            // 先检查是否会形成环
            if (willFormCycle(fromTaskId, toTaskId)) {
                log.warn("无法添加依赖项：将在 DAG 中创建循环");
                return false;
            }

            // 添加边到图中
            DefaultEdge edge = graph.addEdge(fromTaskId, toTaskId);
            if (edge != null) {
                log.debug("在 DAG: {} 中添加依赖项：{} -> {}", name, fromTaskId, toTaskId);
                return true;
            }
            
            return false;
        });
    }

    /**
     * 删除任务间的依赖关系
     *
     * @param fromTaskId 源任务ID
     * @param toTaskId   目标任务ID
     * @return 是否删除成功
     */
    @Override
    public boolean removeDependency(String fromTaskId, String toTaskId) {
        return executeGraphOperation(() -> {
            if (!tasks.containsKey(fromTaskId) || !tasks.containsKey(toTaskId)) {
                return false;
            }

            DefaultEdge edge = graph.getEdge(fromTaskId, toTaskId);
            if (edge != null) {
                boolean removed = graph.removeEdge(edge);
                if (removed) {
                    log.debug("在 DAG: {} 中删除依赖项：{} -> {}", name, fromTaskId, toTaskId);
                }
                return removed;
            }
            return false;
        });
    }

    /**
     * 动态添加任务和依赖到正在运行的DAG
     * 支持在DAG执行过程中动态调整结构
     *
     * @param task           要添加的任务
     * @param predecessorIds 前驱任务ID集合
     * @param successorIds   后继任务ID集合
     * @return 添加的任务ID
     */
    public String dynamicAddTask(BaseTaskModel task, Set<String> predecessorIds, Set<String> successorIds) {
        return executeGraphOperation(() -> {
            String taskId = addTask(task);

            // 添加前驱依赖
            if (predecessorIds != null) {
                for (String predId : predecessorIds) {
                    addDependency(predId, taskId);
                }
            }

            // 添加后继依赖
            if (successorIds != null) {
                for (String succId : successorIds) {
                    addDependency(taskId, succId);
                }
            }

            log.info("动态添加任务 {} 到 DAG: {}，包含 {} 个前置任务和 {} 个后置任务",
                     taskId, name,
                     predecessorIds != null ? predecessorIds.size() : 0,
                     successorIds != null ? successorIds.size() : 0);

            return taskId;
        });
    }

    /**
     * 获取任务的所有前驱任务
     *
     * @param taskId 任务ID
     * @return 前驱任务ID集合
     */
    @Override
    public Set<String> getPredecessors(String taskId) {
        return executeGraphReadOperation(() -> graph.incomingEdgesOf(taskId).stream()
                                                .map(graph::getEdgeSource)
                                                .collect(Collectors.toSet()));
    }

    /**
     * 获取任务的所有后继任务
     *
     * @param taskId 任务ID
     * @return 后继任务ID集合
     */
    @Override
    public Set<String> getSuccessors(String taskId) {
        return executeGraphReadOperation(() -> graph.outgoingEdgesOf(taskId).stream()
                                                .map(graph::getEdgeTarget)
                                                .collect(Collectors.toSet()));
    }

    /**
     * 获取所有入度为0的任务（起点任务）
     *
     * @return 入度为0的任务ID集合
     */
    @Override
    public Set<String> getEntryTasks() {
        return executeGraphReadOperation(() -> graph.vertexSet().stream()
                                                .filter(taskId -> graph.inDegreeOf(taskId) == 0)
                                                .collect(Collectors.toSet()));
    }

    /**
     * 获取所有出度为0的任务（终点任务）
     *
     * @return 出度为0的任务ID集合
     */
    @Override
    public Set<String> getExitTasks() {
        return executeGraphReadOperation(() -> graph.vertexSet().stream()
                                                .filter(taskId -> graph.outDegreeOf(taskId) == 0)
                                                .collect(Collectors.toSet()));
    }

    /**
     * 检查添加边是否会导致环
     *
     * @param fromTaskId 源任务ID
     * @param toTaskId   目标任务ID
     * @return 是否会形成环
     */
    private boolean willFormCycle(String fromTaskId, String toTaskId) {
        // 如果toTaskId是fromTaskId的祖先节点，则添加边会形成环
        Set<String> visited = new HashSet<>();
        Queue<String> queue = new LinkedList<>();
        queue.offer(toTaskId);

        while (!queue.isEmpty()) {
            String current = queue.poll();
            visited.add(current);

            for (DefaultEdge edge : graph.incomingEdgesOf(current)) {
                String pred = graph.getEdgeSource(edge);
                if (pred.equals(fromTaskId)) {
                    return true; // 找到环
                }
                if (!visited.contains(pred)) {
                    queue.offer(pred);
                }
            }
        }
        return false;
    }

    /**
     * 检查DAG是否有效（无环）
     *
     * @return 如果DAG有效返回true，否则返回false
     */
    @Override
    public boolean isValid() {
        return executeGraphReadOperation(() -> {
            // DirectedAcyclicGraph 本身保证无环，所以始终有效
            return true;
        });
    }

    /**
     * 获取DAG的拓扑排序结果
     *
     * @return 按拓扑顺序排列的任务ID列表
     */
    @Override
    public List<String> getTopologicalOrder() {
        return executeGraphReadOperation(() -> {
            if (!isValid()) {
                log.error("无法获取拓扑顺序：DAG 包含循环");
                return Collections.emptyList();
            }

            TopologicalOrderIterator<String, DefaultEdge> topologicalOrderIterator =
                    new TopologicalOrderIterator<>(graph);
            List<String> orderedTasks = new ArrayList<>();
            topologicalOrderIterator.forEachRemaining(orderedTasks::add);
            return orderedTasks;
        });
    }

    /**
     * 生成调度计划
     *
     * @param startTaskId 起点任务ID
     * @return 调度计划中的任务ID列表
     */
    public List<String> generateTaskSchedule(String startTaskId) {
        return executeGraphReadOperation(() -> {
            if (!tasks.containsKey(startTaskId)) {
                return Collections.emptyList();
            }

            // 获取从startTaskId可达的所有任务
            Set<String> reachable = getReachableTasks(startTaskId);

            // 使用TopologicalOrderIterator进行排序，但只包含可达的任务
            List<String> fullTopologicalOrder = getTopologicalOrder();
            return fullTopologicalOrder.stream()
                    .filter(reachable::contains)
                    .collect(Collectors.toList());
        });
    }

    /**
     * 获取从起点任务可达的所有任务
     *
     * @param startTaskId 起点任务ID
     * @return 可达任务ID集合
     */
    @Override
    public Set<String> getReachableTasks(String startTaskId) {
        return executeGraphReadOperation(() -> {
            Set<String> reachable = new HashSet<>();
            Queue<String> queue = new LinkedList<>();

            queue.offer(startTaskId);
            reachable.add(startTaskId);

            while (!queue.isEmpty()) {
                String current = queue.poll();

                for (DefaultEdge edge : graph.outgoingEdgesOf(current)) {
                    String next = graph.getEdgeTarget(edge);
                    if (!reachable.contains(next)) {
                        reachable.add(next);
                        queue.offer(next);
                    }
                }
            }

            return reachable;
        });
    }

    /**
     * 获取可执行的任务集合
     * 在事件驱动模型中，当任务状态变更后，需要重新计算可执行任务
     *
     * @return 可执行的任务ID集合
     */
    public Set<String> getExecutableTasks() {
        return executeGraphReadOperation(() -> {
            Set<String> executable = new HashSet<>();

            for (Map.Entry<String, BaseTaskModel> entry : tasks.entrySet()) {
                String taskId = entry.getKey();
                BaseTaskModel task = entry.getValue();

                // 任务必须处于PENDING状态才能执行
                if (task.getStatus() == TaskStatus.PENDING) {
                    // 检查所有前驱任务是否已完成
                    boolean allPredecessorsCompleted = true;

                    for (DefaultEdge edge : graph.incomingEdgesOf(taskId)) {
                        String predId = graph.getEdgeSource(edge);
                        BaseTaskModel pred = tasks.get(predId);
                        if (pred == null || pred.getStatus() != TaskStatus.COMPLETED) {
                            allPredecessorsCompleted = false;
                            break;
                        }
                    }

                    if (allPredecessorsCompleted) {
                        executable.add(taskId);
                    }
                }
            }

            return executable;
        });
    }

    /**
     * 添加任务状态变化监听器
     *
     * @param listener 监听器
     */
    public void addStatusChangeListener(Consumer<TaskStatusEvent> listener) {
        statusListeners.add(listener);
    }

    /**
     * 通知任务状态变化
     * 当任务状态变化时，通知所有监听器
     *
     * @param taskId     任务ID
     * @param oldStatus  旧状态
     * @param newStatus  新状态
     */
    public void notifyStatusChange(String taskId, TaskStatus oldStatus, TaskStatus newStatus) {
        TaskStatusEvent event = new TaskStatusEvent(taskId, oldStatus, newStatus);
        statusListeners.forEach(listener -> listener.accept(event));
    }

    @Override
    public String toString() {
        return executeGraphReadOperation(graph::toString);
    }
}