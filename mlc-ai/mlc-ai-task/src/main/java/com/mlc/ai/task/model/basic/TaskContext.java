package com.mlc.ai.task.model.basic;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 任务上下游协调和引擎预处理能力
 * 通过 TaskContext 传递数据，而非任务间直接耦合。
 */
@Getter
@Setter
@Builder
public class TaskContext {

    /**
     * 数据转换器接口
     * 用于在任务间转换数据格式
     */
    @FunctionalInterface
    public interface DataTransformer {
        /**
         * 转换数据
         *
         * @param input 输入数据
         * @return 转换后的数据
         */
        Map<String, Object> transform(Map<String, Object> input);
    }

    /**
     * 任务上下文ID，用于跟踪和调试
     */
    @Builder.Default
    private String id = UUID.randomUUID().toString();

    /**
     * 输入参数（如上游任务结果）
     */
    @Builder.Default
    private Map<String, Object> input = new HashMap<>();

    /**
     * 输出结果
     */
    @Builder.Default
    private Map<String, Object> output = new HashMap<>();

    /**
     * 异常信息
     */
    private Throwable error;

    /**
     * 上游任务ID列表，由引擎自动填充
     */
    @Builder.Default
    private Map<String, BaseTaskModel> upstreamTasks = new HashMap<>();
    
    /**
     * 下游任务ID列表，由引擎自动填充
     */
    @Builder.Default
    private Map<String, BaseTaskModel> downstreamTasks = new HashMap<>();

    /**
     * 任务元数据，由引擎填充，包含任务执行环境、状态等信息
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 转换器映射表，用于在任务间自动转换数据格式
     * 键：目标任务ID，值：转换器实例
     */
    @Builder.Default
    private Map<String, DataTransformer> transformers = new HashMap<>();

    /**
     * 添加数据转换器，用于将当前任务输出转换为下游任务需要的格式
     *
     * @param targetTaskId 目标任务ID
     * @param transformer 转换器实例
     */
    public void addTransformer(String targetTaskId, DataTransformer transformer) {
        transformers.put(targetTaskId, transformer);
    }


    /**
     * 合并上游任务的输出作为当前任务的输入
     * 由引擎在任务执行前自动调用
     */
    public void mergeUpstreamOutputs() {
        upstreamTasks.forEach((taskId, task) -> {
            if (task.getTaskContext() != null && task.getTaskContext().getOutput() != null) {
                // 检查是否有针对当前任务的数据转换器
                Map<String, Object> transformedOutput;
                DataTransformer transformer = task.getTaskContext().transformers.get(this.id);
                if (transformer != null) {
                    transformedOutput = transformer.transform(task.getTaskContext().getOutput());
                } else {
                    transformedOutput = task.getTaskContext().getOutput();
                }
                
                // 使用执行器Key作为前缀，防止同名键冲突并提高可读性
                transformedOutput.forEach((key, value) -> 
                    input.put(task.getExecutorKey() + "." + key, value)
                );
            }
        });
    }
    
    /**
     * 获取上游任务的输出值
     * 
     * @param taskId 上游任务ID
     * @param key 输出键
     * @return 输出值，如果不存在则返回null
     */
    public Object getUpstreamOutput(String taskId, String key) {
        BaseTaskModel upstreamTask = upstreamTasks.get(taskId);
        if (upstreamTask != null && upstreamTask.getTaskContext() != null) {
            return upstreamTask.getTaskContext().getOutput().get(key);
        }
        return null;
    }
}
