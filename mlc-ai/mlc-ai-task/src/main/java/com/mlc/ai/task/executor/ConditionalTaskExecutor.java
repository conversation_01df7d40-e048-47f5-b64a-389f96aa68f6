package com.mlc.ai.task.executor;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.core.util.condition.IConditionEngine;
import com.mlc.ai.core.util.condition.ConditionEngineFactory;
import com.mlc.ai.task.model.ConditionalTaskModel.BranchCondition;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.ConditionalTaskModel;
import java.util.ArrayList;
import java.util.Comparator;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条件分支任务执行器
 * 负责根据条件表达式评估结果设置分支结果
 * 支持多个分支条件和不同的条件引擎
 */
@Slf4j
public class ConditionalTaskExecutor implements ITaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext executionContext) {
        // 验证任务类型
        if (!(task instanceof ConditionalTaskModel conditionalTask)) {
            return Flux.error(new IllegalArgumentException("任务类型必须是ConditionalTaskModel"));
        }

        // 获取分支条件列表
        List<ConditionalTaskModel.BranchCondition> branchConditions = conditionalTask.getBranchConditions();
        if (branchConditions == null || branchConditions.isEmpty()) {
            return Flux.error(new IllegalArgumentException("分支条件列表不能为空"));
        }

        log.info("[{}] 开始评估条件分支，共{}个分支条件", executionContext.getExecutionId(), branchConditions.size());

        try {
            // 获取条件引擎
            IConditionEngine engine = ConditionEngineFactory.getEngine(conditionalTask.getEngineType());
            
            // 准备上下文变量
            Map<String, Object> conditionContext = this.prepareConditionContext(conditionalTask);
            
            // 评估分支条件
            String selectedBranch = this.evaluateBranchConditions(branchConditions, engine, conditionContext, executionContext);
            
            // 如果没有匹配的分支，使用默认分支
            if (selectedBranch == null) {
                selectedBranch = conditionalTask.getDefaultBranch();
                log.info("[{}] 没有匹配的分支条件，使用默认分支: {}", executionContext.getExecutionId(), selectedBranch);
            }
            
            // 将选中的分支存储到任务上下文
            conditionalTask.getTaskContext().getOutput().put("selectedBranch", selectedBranch);

            log.info("[{}] 条件分支评估完成，选中分支: {}", executionContext.getExecutionId(), selectedBranch);
            
            return Flux.just("🐳 策略评估完成 \n");
        } catch (Exception e) {
            log.error("[{}] 条件分支评估失败: {}", executionContext.getExecutionId(), e.getMessage(), e);
            return Flux.error(new RuntimeException("条件分支评估失败: " + e.getMessage(), e));
        }
    }

    /**
     * 评估分支条件
     *
     * @param branchConditions 分支条件列表
     * @param engine 条件引擎
     * @param conditionContext 条件上下文
     * @param executionContext 执行上下文
     * @return 选中的分支ID，如果没有匹配的分支则返回null
     */
    private String evaluateBranchConditions(List<ConditionalTaskModel.BranchCondition> branchConditions,
                                           IConditionEngine engine,
                                           Map<String, Object> conditionContext,
                                           ExecutionContext executionContext) throws IConditionEngine.ConditionEvaluationException {
        

        // 复制分支条件列表，避免修改原始数据
        ArrayList<BranchCondition> arrayConditions = new ArrayList<>(branchConditions);
        // 按优先级排序分支条件
        arrayConditions.sort(Comparator.comparingInt(BranchCondition::getPriority));

        for (ConditionalTaskModel.BranchCondition branchCondition : arrayConditions) {
            String branchId = branchCondition.getBranchId();
            String expression = branchCondition.getConditionExpression();
            
            log.info("[{}] 评估分支条件 [{}]: {}", executionContext.getExecutionId(), branchId, expression);
            
            try {
                boolean result = engine.evaluate(expression, conditionContext);
                
                log.info("[{}] 分支条件 [{}] 评估结果: {} -> {}", executionContext.getExecutionId(), branchId, expression, result);
                
                if (result) {
                    log.info("[{}] 选中分支: {}", executionContext.getExecutionId(), branchId);
                    return branchId;
                }
            } catch (IConditionEngine.ConditionEvaluationException e) {
                log.error("[{}] 分支条件 [{}] 评估失败: {}", executionContext.getExecutionId(), branchId, e.getMessage());
                throw e;
            }
        }

        // 没有匹配的分支
        return null;
    }

    /**
     * 准备条件上下文，将任务上下文数据整理为条件引擎可用的格式
     * 规范化格式：executorName.outputKey
     *
     * @param task 任务模型
     * @return 条件上下文
     */
    private Map<String, Object> prepareConditionContext(ConditionalTaskModel task) {
        Map<String, Object> context = new HashMap<>();
        
        // 添加配置参数
        if (task.getConfig().getParams() != null) {
            context.putAll(task.getConfig().getParams());
        }
        
        // 添加任务输入参数
        Map<String, Object> taskInput = task.getTaskContext().getInput();
        context.putAll(taskInput);

        // 添加上游任务输出
        Map<String, BaseTaskModel> upstreamTasks = task.getTaskContext().getUpstreamTasks();
        for (Map.Entry<String, BaseTaskModel> entry : upstreamTasks.entrySet()) {
            BaseTaskModel upstreamTask = entry.getValue();
            String executorName = upstreamTask.getExecutorKey();

            Map<String, Object> upstreamOutput = upstreamTask.getTaskContext().getOutput();
            for (Map.Entry<String, Object> outputEntry : upstreamOutput.entrySet()) {
                String outputKey = outputEntry.getKey();
                Object outputValue = outputEntry.getValue();
                
                // 规范化变量引用：executorName.outputKey
                String variableKey = executorName + "_" + outputKey;
                context.put(variableKey, outputValue);
                
                log.debug("[{}] 添加变量: {} = {}", task.getId(), variableKey, outputValue);
            }
        }
        
        return context;
    }
}