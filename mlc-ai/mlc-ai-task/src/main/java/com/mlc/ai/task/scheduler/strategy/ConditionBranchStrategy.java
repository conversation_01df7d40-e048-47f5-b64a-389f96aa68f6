package com.mlc.ai.task.scheduler.strategy;

import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.engine.TaskStatusEvent;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.model.ConditionalTaskModel;
import com.mlc.ai.task.scheduler.TaskScheduler;
import com.mlc.ai.core.util.BranchConfigBuilder.BranchConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * 分支策略处理器
 * 基于分支ID的条件分支处理
 */
@Slf4j
public class ConditionBranchStrategy implements ITaskStatusStrategy {
    
    @Override
    public void justHandle(TaskStatusEvent event, TaskScheduler context) {
        String taskId = event.getTaskId();
        DagInMemoryEngine dagEngine = context.getDagEngine();

        // 获取当前任务
        BaseTaskModel task = dagEngine.getTasks().get(taskId);
        if (task == null) {
            log.warn("找不到任务: {}", taskId);
            return;
        }
        
        // 验证任务类型
        if (!(task instanceof ConditionalTaskModel conditionalTask)) {
            log.debug("任务不是条件分支类型: {}", taskId);
            return;
        }

        // 获取选中的分支ID
        Object selectedBranchObj = conditionalTask.getTaskContext().getOutput().get("selectedBranch");
        if (selectedBranchObj == null) {
            log.warn("条件任务没有输出选中的分支: {}", taskId);
            return;
        }
        
        String selectedBranch = selectedBranchObj.toString();
        
        log.info("条件分支任务 {} 选中分支: {}", taskId, selectedBranch);
        
        // 获取后继任务
        Set<String> successors = dagEngine.getSuccessors(taskId);
        if (successors.isEmpty()) {
            log.info("条件分支任务 {} 没有后继任务", taskId);
            return;
        }
        
        // 根据选中的分支调整DAG执行路径
        for (String successorId : successors) {
            BaseTaskModel successor = dagEngine.getTasks().get(successorId);
            if (successor == null) {
                continue;
            }
            
            // 检查任务的分支配置
            boolean shouldExecute = shouldExecuteTask(successor, selectedBranch);
            
            // 如果任务不应该执行，则跳过
            if (!shouldExecute) {
                log.info("跳过任务 {}, 因为不匹配选中的分支: {}", successorId, selectedBranch);
                
                // 标记任务状态为SKIPPED
                dagEngine.notifyStatusChange(successorId, successor.getStatus(), TaskStatus.SKIPPED);
            } else {
                log.info("任务 {} 匹配选中的分支: {}, 将被执行", successorId, selectedBranch);
            }
        }
    }

    @Override
    public boolean canHandle(TaskStatusEvent event) {
        // 只处理COMPLETED状态变更
        return event.getNewStatus() == TaskStatus.COMPLETED;
    }
    
    /**
     * 判断任务是否应该执行
     * 使用规范化的分支配置方式
     *
     * @param task 任务
     * @param selectedBranch 选中的分支ID
     * @return 是否应该执行
     */
    private boolean shouldExecuteTask(BaseTaskModel task, String selectedBranch) {
        BranchConfig branchConfig = task.getConfig().getBranchConfig();
        if (branchConfig != null) {
            return branchConfig.matches(selectedBranch);
        }
        
        // 如果任务没有分支配置，默认执行
        log.debug("任务 {} 没有分支配置，默认执行", task.getId());
        return true;
    }
} 