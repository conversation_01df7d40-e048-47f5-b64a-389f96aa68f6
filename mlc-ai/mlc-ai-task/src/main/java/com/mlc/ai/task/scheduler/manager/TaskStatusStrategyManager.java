package com.mlc.ai.task.scheduler.manager;

import com.mlc.ai.task.scheduler.TaskScheduler;
import com.mlc.ai.task.scheduler.strategy.ITaskStatusStrategy;
import com.mlc.ai.task.engine.TaskStatusEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务状态策略管理器
 * 负责管理和执行任务状态变化的处理策略
 */
@Slf4j
@Getter
public class TaskStatusStrategyManager {

    /**
     * 状态处理策略列表
     */
    private final List<ITaskStatusStrategy> strategies = new ArrayList<>();

    /**
     * 添加状态处理策略
     *
     * @param strategy 要添加的策略
     */
    public void addStrategy(ITaskStatusStrategy strategy) {
        strategies.add(strategy);
    }

    /**
     * 处理任务状态变化事件
     *
     * @param event 状态变化事件
     * @param taskScheduler 任务调度器
     */
    public void handleTaskStatusChange(TaskStatusEvent event, TaskScheduler taskScheduler) {
        // 查找并执行适用的策略
        for (ITaskStatusStrategy strategy : strategies) {
            if (strategy.canHandle(event)) {
                log.debug("使用策略 {} 处理任务 {} 状态变化: {} -> {}",
                        strategy.getClass().getSimpleName(),
                        event.getTaskId(),
                        event.getOldStatus(),
                        event.getNewStatus());
                
                try {
                    strategy.justHandle(event, taskScheduler);
                } catch (Exception e) {
                    log.error("执行状态处理策略异常", e);
                }
            }
        }
    }

    /**
     * 清除所有策略
     */
    public void clearStrategies() {
        strategies.clear();
    }
} 