package com.mlc.ai.task.model;

import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * 邮件任务模型
 * 用于发送电子邮件通知
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class EmailTaskModel extends BaseTaskModel {
    
    // ========== 核心邮件属性 ==========
    /**
     * 邮件发送人
     */
    private String from;
    
    /**
     * 邮件接收人列表
     */
    private List<String> to;
    
    /**
     * 邮件主题
     */
    private String subject;
    
    /**
     * 邮件内容
     */
    private String content;
    
    /**
     * 是否为HTML格式
     */
    @Builder.Default
    private boolean isHtml = false;
    
    /**
     * 邮件任务配置
     */
    @Builder.Default
    private EmailTaskConfig config = EmailTaskConfig.builder().build();
    
    @Override
    public TaskType getTaskType() {
        return TaskType.EMAIL;
    }
    
    @Override
    public EmailTaskConfig getConfig() {
        return config;
    }
    
    /**
     * 邮件任务配置
     */
    @Data
    @SuperBuilder
    @EqualsAndHashCode(callSuper = true)
    public static class EmailTaskConfig extends BaseTaskConfig {
        /**
         * 抄送人列表
         */
        private List<String> cc;
        
        /**
         * 密送人列表
         */
        private List<String> bcc;
        
        /**
         * 邮件模板ID
         * 如果使用模板，则内容会被模板渲染
         */
        private String templateId;
        
        /**
         * 模板参数
         * 用于替换模板中的变量
         */
        private Map<String, Object> templateParams;
        
        /**
         * 附件列表
         */
        private List<Attachment> attachments;
    }
    
    /**
     * 邮件附件
     */
    @Data
    @Builder
    public static class Attachment {
        /**
         * 附件名称
         */
        private String name;
        
        /**
         * 附件类型
         */
        private String contentType;
        
        /**
         * 附件路径
         * 可以是本地路径或URL
         */
        private String path;
        
        /**
         * 附件内容
         * 二进制数据的Base64编码
         */
        private String content;
    }
} 