package com.mlc.ai.task.model;

import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 子工作流任务模型
 * 用于触发另一个完整的工作流
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class SubWorkflowTaskModel extends BaseTaskModel {
    
    /**
     * 子工作流ID
     */
    private String subWorkflowId;
    
    /**
     * 子工作流版本
     */
    private String subWorkflowVersion;
    
    /**
     * 是否等待子工作流完成
     * 如果为true，则主工作流会等待子工作流完成后才继续
     * 如果为false，则主工作流会在子工作流启动后立即继续
     */
    @Builder.Default
    private boolean waitForCompletion = true;
    
    /**
     * 子工作流失败时是否使主工作流失败
     */
    @Builder.Default
    private boolean failParentOnFailure = true;
    
    /**
     * 子工作流任务配置
     */
    @Builder.Default
    private SubWorkflowTaskConfig config = SubWorkflowTaskConfig.builder().build();
    
    @Override
    public TaskType getTaskType() {
        return TaskType.SUB_WORKFLOW;
    }
    
    @Override
    public SubWorkflowTaskConfig getConfig() {
        return config;
    }
    
    /**
     * 子工作流任务配置
     */
    @Data
    @SuperBuilder
    @EqualsAndHashCode(callSuper = true)
    public static class SubWorkflowTaskConfig extends BaseTaskConfig {
        /**
         * 输入参数映射
         * 主工作流参数名 -> 子工作流参数名
         */
        private Map<String, String> inputMapping;
        
        /**
         * 输出参数映射
         * 子工作流输出名 -> 主工作流参数名
         */
        private Map<String, String> outputMapping;
    }
} 