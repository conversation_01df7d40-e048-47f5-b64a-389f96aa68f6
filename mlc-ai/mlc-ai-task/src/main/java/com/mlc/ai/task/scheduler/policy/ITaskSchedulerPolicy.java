package com.mlc.ai.task.scheduler.policy;

import com.mlc.ai.task.model.basic.BaseTaskModel;

import java.util.List;

/**
 * 任务调度策略接口
 * 用于定义任务如何被调度执行
 */
public interface ITaskSchedulerPolicy {
    
    /**
     * 从待执行任务列表中选择下一个要执行的任务
     *
     * @param readyTasks 准备好执行的任务列表
     * @return 选中的任务，如果无法选择则返回null
     */
    BaseTaskModel selectNextTask(List<BaseTaskModel> readyTasks);
    
    /**
     * 获取最大并行执行任务数
     *
     * @return 最大并行数
     */
    int getMaxParallelism();
    
    /**
     * 决定是否继续调度新任务
     * 例如当出现任务失败时，可以决定是否继续执行其它任务
     *
     * @param failedTask 失败的任务
     * @return 是否继续调度
     */
    boolean shouldContinueScheduling(BaseTaskModel failedTask);
} 