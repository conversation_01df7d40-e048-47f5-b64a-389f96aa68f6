package com.mlc.ai.task.scheduler;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.scheduler.policy.ITaskSchedulerPolicy;
import com.mlc.ai.task.engine.TaskStatusEvent;
import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.scheduler.policy.DefaultTaskSchedulerPolicy;
import com.mlc.ai.task.scheduler.manager.SchedulerStatusManager;
import com.mlc.ai.task.scheduler.manager.TaskQueueManager;
import com.mlc.ai.task.scheduler.manager.TaskDependencyChecker;
import com.mlc.ai.task.scheduler.status.SchedulerStatus;
import com.mlc.ai.task.scheduler.status.SchedulerStatusEvent;
import com.mlc.ai.task.workflow.WorkflowEngine;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;


/**
 * 任务调度器
 * 负责：
 * 1. 协调各个管理器组件
 * 2. 控制任务执行流程
 * 3. 处理任务状态变化事件
 * 4. 管理调度器生命周期
 */
@Slf4j
@Getter
public class TaskScheduler {

    /**
     * 调度器唯一标识
     */
    private final String schedulerId = UUID.randomUUID().toString();

    /**
     * DAG引擎
     */
    private final DagInMemoryEngine dagEngine;

    /**
     * 工作流引擎
     */
    private final WorkflowEngine workflowEngine;

    /**
     * 执行上下文
     */
    private final ExecutionContext executionContext;

    /**
     * 调度策略
     */
    private final ITaskSchedulerPolicy schedulerPolicy;

    /**
     * 状态管理器
     */
    private final SchedulerStatusManager schedulerStatusManager;

    /**
     * 任务队列管理器
     */
    private final TaskQueueManager queueManager;

    /**
     * 任务依赖检查器
     */
    private final TaskDependencyChecker dependencyChecker;

    /**
     * 任务状态变化事件流，用于监听任务状态变化
     */
    private Flux<TaskStatusEvent> statusChangeFlux;

    /**
     * 构造函数
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     */
    public TaskScheduler(DagInMemoryEngine dagEngine, WorkflowEngine workflowEngine, ExecutionContext executionContext) {
        this(dagEngine, workflowEngine, executionContext, DefaultTaskSchedulerPolicy.builder().build());
    }

    /**
     * 构造函数
     *
     * @param dagEngine DAG引擎
     * @param workflowEngine 工作流引擎
     * @param executionContext 执行上下文
     * @param schedulerPolicy 调度策略
     */
    public TaskScheduler(DagInMemoryEngine dagEngine, WorkflowEngine workflowEngine,
                        ExecutionContext executionContext, ITaskSchedulerPolicy schedulerPolicy) {
        this.dagEngine = dagEngine;
        this.workflowEngine = workflowEngine;
        this.executionContext = executionContext;
        this.schedulerPolicy = schedulerPolicy;

        // 初始化管理器组件
        this.schedulerStatusManager = new SchedulerStatusManager(schedulerId);
        this.queueManager = new TaskQueueManager(dagEngine);
        this.dependencyChecker = new TaskDependencyChecker(dagEngine, queueManager, executionContext.getExecutionId());

        initializeCall();
    }

    /**
     * 初始化调度器
     */
    public void initializeCall() {
        // 添加任务状态变化监听器
        statusChangeFlux = Flux.create(sink -> dagEngine.addStatusChangeListener(sink::next));
        statusChangeFlux.subscribe(this::onTaskStatusChange);

        // 订阅状态管理器的状态变化事件
        schedulerStatusManager.getStatusFlux().subscribe(this::onSchedulerStatusChange);
    }

    /**
     * 处理调度器状态变化事件
     *
     * @param event 调度器状态变化事件
     */
    private void onSchedulerStatusChange(SchedulerStatusEvent event) {
        log.info("[{}] 调度器状态变更: {} -> {}",
                 executionContext.getExecutionId(), event.getOldStatus(), event.getNewStatus());

        // 根据不同的状态变化执行相应的操作
        SchedulerStatus newStatus = event.getNewStatus();

        if (newStatus == SchedulerStatus.FAILED) {
            // 清空准备队列
            queueManager.clearQueue();
        } else if (newStatus == SchedulerStatus.CANCELLED) {
            // 清空准备队列
            queueManager.clearQueue();

            // 取消所有运行中的任务
            dependencyChecker.cancelRunningTasks();

            schedulerStatusManager.setCompleted(true);
        }
    }

    /**
     * 注册完成回调
     *
     * @param callback 回调函数
     */
    public void registerComplete(Runnable callback) {
        schedulerStatusManager.registerCompletionCallback(callback);
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    public SchedulerStatus getStatus() {
        return schedulerStatusManager.getCurrentStatus();
    }

    /**
     * 启动调度器
     *
     * @return 执行结果流
     */
    public Flux<String> start() {
        if (!schedulerStatusManager.canStart()) {
            return Flux.error(new IllegalStateException("调度器已经在运行中"));
        }

        if (!dagEngine.isValid()) {
            return Flux.error(new IllegalArgumentException("DAG包含循环依赖，无法执行"));
        }

        // 初始化调度状态
        schedulerStatusManager.changeStatus(SchedulerStatus.RUNNING);
        schedulerStatusManager.setCompleted(false);

        log.info("[{}] 开始调度任务流: {}", executionContext.getExecutionId(), dagEngine.getName());

        // 初始化任务队列
        queueManager.initializeQueue();

        // 创建执行流
        return Flux.create(sink -> {
            // 注册完成回调
            registerComplete(() -> {
                boolean hasFailures = dependencyChecker.hasFailedTasks();

                if (hasFailures) {
                    schedulerStatusManager.changeStatus(SchedulerStatus.FAILED);
                    sink.next("😭️ 任务流执行失败");
                } else {
                    schedulerStatusManager.changeStatus(SchedulerStatus.COMPLETED);
                    sink.next("🥳 任务流执行成功");
                }

                sink.complete();
            });

            // 启动任务调度
            scheduleNextTasks(sink);
        });
    }

    /**
     * 调度下一批任务执行
     *
     * @param sink 输出接收器
     */
    private void scheduleNextTasks(FluxSink<String> sink) {
        // 首先检查是否已标记完成，避免重复调度
        if (schedulerStatusManager.isCompleted()) {
            return;
        }

        // 检查调度器状态，如果已经失败，不再调度新任务
        if (schedulerStatusManager.isFailed()) {
            if (!schedulerStatusManager.isCompleted()) {
                schedulerStatusManager.setCompleted(true);
                log.info("[{}] 任务流执行失败，不再调度新任务", executionContext.getExecutionId());
                schedulerStatusManager.notifyCompletion();
            }
            return;
        }

        // 没有可执行的任务，且没有正在执行的任务，可能执行完成
        if (queueManager.isIdle()) {
            // 检查任务执行状态
            boolean allFinished = dependencyChecker.areAllTasksFinished();

            // 如果所有任务都已经结束（成功、跳过、取消或失败），则标记调度完成
            if (allFinished && !schedulerStatusManager.isCompleted()) {
                schedulerStatusManager.setCompleted(true);
                log.info("[{}] 任务流执行完成，触发完成回调", executionContext.getExecutionId());
                schedulerStatusManager.notifyCompletion();
                return;
            }
        }

        // 计算可以并行执行的任务数量
        int availableSlots = schedulerPolicy.getMaxParallelism() - queueManager.getRunningTaskCount();

        // 从准备队列中取出任务执行
        for (int i = 0; i < availableSlots; i++) {
            // 选择下一个要执行的任务
            BaseTaskModel selectedTask = queueManager.selectNextTask(schedulerPolicy);
            if (selectedTask == null) {
                break;
            }

            log.debug("[{}] 选择任务执行: {} (优先级: {})", executionContext.getExecutionId(),
                     selectedTask.getId(), selectedTask.getConfig().getPriority());

            // 执行任务
            workflowEngine.executeTask(selectedTask)
                          .subscribe(
                              sink::next, // 输出执行结果
                              error -> {
                                  log.error("[{}] 任务执行出错: {}", executionContext.getExecutionId(), selectedTask.getId(), error);
                                  scheduleNextTasks(sink);
                              },
                              () -> scheduleNextTasks(sink)
                          );
        }

        // 如果还有等待执行的任务，但没有可用的执行槽位，延迟一段时间后再次尝试
        if (!queueManager.isQueueEmpty() && availableSlots == 0) {
            Mono.delay(java.time.Duration.ofMillis(100))
                .subscribe(ignored -> scheduleNextTasks(sink));
        }
    }

    /**
     * 取消调度
     */
    public void cancel() {
        if (schedulerStatusManager.isRunning()) {
            schedulerStatusManager.changeStatus(SchedulerStatus.CANCELLED);
        }
    }

    /**
     * 设置调度器失败状态
     */
    public void setFailed() {
        if (schedulerStatusManager.isRunning()) {
            schedulerStatusManager.changeStatus(SchedulerStatus.FAILED);
        }
    }

    /**
     * 检查任务的后继任务是否可以执行
     *
     * @param taskId 任务ID
     */
    private void checkSuccessors(String taskId) {
        List<String> readySuccessors = dependencyChecker.checkSuccessors(taskId);
        log.debug("[{}] 任务 {} 完成后，有 {} 个后继任务准备就绪",
                 executionContext.getExecutionId(), taskId, readySuccessors.size());
    }

    /**
     * 任务状态变化处理
     *
     * @param event 状态变化事件
     */
    private void onTaskStatusChange(TaskStatusEvent event) {
        String taskId = event.getTaskId();
        TaskStatus oldStatus = event.getOldStatus();
        TaskStatus newStatus = event.getNewStatus();

        log.debug("[{}] 任务 {} 状态变更: {} -> {}",
                  executionContext.getExecutionId(), taskId, oldStatus, newStatus);

        // 更新任务状态 - 这一步很关键，确保任务模型的状态被正确设置
        BaseTaskModel task = dagEngine.getTasks().get(taskId);
        if (task != null) {
            task.setStatus(newStatus);
            log.debug("[{}] 更新任务 {} 状态为: {}", executionContext.getExecutionId(), taskId, newStatus);
        }

        // 使用工作流引擎的状态策略管理器来处理状态变化
        // 策略可能会修改依赖关系等，所以必须在检查后继任务之前调用
        executionContext.getStatusStrategyManager().handleTaskStatusChange(event, this);

        /*
         * COMPLETED 和 SKIPPED 必须检查后继任务的状态
         * 完成：任务成功结束，后继任务可能满足依赖条件，需要被调度。
         * 跳过：任务未执行但被标记为终结，需根据业务逻辑判断是否允许后继任务继续（例如分支流程中的条件跳过可能触发其他路径）。
         */
        if (newStatus == TaskStatus.COMPLETED || newStatus == TaskStatus.SKIPPED) {
            queueManager.decrementRunningTasks();
            checkSuccessors(taskId);
        } else if (newStatus == TaskStatus.FAILED) {
            queueManager.decrementRunningTasks();

            // 根据调度策略决定是否继续调度
            if (!schedulerPolicy.shouldContinueScheduling(task)) {
                setFailed();
            } else {
                checkSuccessors(taskId);
            }

            log.error("[{}] 任务 {} 执行失败", executionContext.getExecutionId(), taskId);
        } else if (newStatus == TaskStatus.CANCELLED) {
            log.info("[{}] 任务 {} 被取消", executionContext.getExecutionId(), taskId);
        } else if (newStatus == TaskStatus.TIMEOUT) {
            queueManager.decrementRunningTasks();

            if (!schedulerPolicy.shouldContinueScheduling(task)) {
                setFailed();
            }

            log.error("[{}] 任务 {} 超时", executionContext.getExecutionId(), taskId);
        }
    }

    /**
     * 获取调度器统计信息
     *
     * @return 统计信息字符串
     */
    public String getSchedulerStats() {
        return String.format("调度器状态: %s, %s, %s", schedulerStatusManager.getCurrentStatus(),
                             queueManager.getStatusInfo(), dependencyChecker.getDependencyStats());
    }
} 