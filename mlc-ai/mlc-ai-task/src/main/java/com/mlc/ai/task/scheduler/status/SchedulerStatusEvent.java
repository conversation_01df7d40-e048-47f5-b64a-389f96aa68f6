package com.mlc.ai.task.scheduler.status;

import lombok.Getter;

/**
 * 调度器状态变更事件
 */
@Getter
public class SchedulerStatusEvent {
    private final String schedulerId;
    private final SchedulerStatus oldStatus;
    private final SchedulerStatus newStatus;
    private final long timestamp;

    public SchedulerStatusEvent(String schedulerId, SchedulerStatus oldStatus,
                             SchedulerStatus newStatus) {
        this.schedulerId = schedulerId;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.timestamp = System.currentTimeMillis();
    }
} 