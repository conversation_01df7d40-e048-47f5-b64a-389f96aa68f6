package com.mlc.ai.task.scheduler.strategy;

import com.mlc.ai.task.engine.TaskStatusEvent;
import com.mlc.ai.task.scheduler.TaskScheduler;

/**
 * 任务状态处理策略接口
 * 定义如何处理任务状态变化事件
 */
public interface ITaskStatusStrategy {
    
    /**
     * 处理任务状态变化事件
     *
     * @param event 状态变化事件
     * @param context 策略执行上下文
     */
    void justHandle(TaskStatusEvent event, TaskScheduler context);
    
    /**
     * 判断该策略是否可以处理指定的状态变化事件
     *
     * @param event 状态变化事件
     * @return 如果可以处理返回true，否则返回false
     */
    boolean canHandle(TaskStatusEvent event);
} 