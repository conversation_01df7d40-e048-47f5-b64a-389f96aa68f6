package com.mlc.ai.task.cache;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.task.cache.adapter.IChatModelAdapter;
import com.mlc.ai.task.cache.adapter.DeepSeekChatModelAdapter;
import com.mlc.ai.task.cache.adapter.OpenAiChatModelAdapter;
import com.mlc.ai.task.cache.adapter.VertexAiGeminiChatModelAdapter;
import com.mlc.ai.task.model.AITaskModel;
import com.mlc.ai.task.model.AITaskModel.ChatClientConfig;
import io.nop.api.core.util.Guard;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.model.ChatModel;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ChatClient 缓存管理器
 * 负责 ChatClient 实例的创建、缓存和管理
 * 支持多种模型类型，通过模型类型自动选择合适的 ChatModel
 */
@Slf4j
public class ChatClientCacheManager {

    /**
     * ChatClient 缓存
     */
    private final Map<String, ChatClient> chatClientCache = new ConcurrentHashMap<>();

    /**
     * 可用的 ChatModel 列表
     */
    private final List<ChatModel> availableChatModels;

    /**
     * 内置适配器列表（按优先级排序）
     */
    private final List<IChatModelAdapter> adapters = new ArrayList<>(List.of(
        new DeepSeekChatModelAdapter(),
        new OpenAiChatModelAdapter(),
        new VertexAiGeminiChatModelAdapter()
    ));

    /**
     * 构造函数 - 接受多个 ChatModel
     */
    public ChatClientCacheManager(List<ChatModel> chatModels) {
        Guard.notEmpty(chatModels, "chatModels 不能为空");
        this.availableChatModels = chatModels;

        log.info("初始化 ChatClientCacheManager，可用模型: {}", 
            chatModels.stream().map(model -> model.getClass().getSimpleName()).toList());
        log.info("已加载适配器: {}", adapters.stream().map(IChatModelAdapter::getAdapterName).toList());
    }

    /**
     * 构造函数 - 接受可变参数 ChatModel
     */
    public ChatClientCacheManager(ChatModel... chatModels) {
        this(List.of(chatModels));
    }

    /**
     * 获取或创建 ChatClient
     */
    public ChatClient getChatClient(AITaskModel.ChatClientConfig config) {
        String cacheKey = generateCacheKey(config);
        
        return chatClientCache.computeIfAbsent(cacheKey, key -> {
            log.info("创建新的ChatClient实例，配置: {}", config);
            return this.createChatClient(config);
        });
    }

    /**
     * 创建 ChatClient
     */
    private ChatClient createChatClient(AITaskModel.ChatClientConfig config) {
        // 根据配置选择合适的 ChatModel
        ChatModel selectedModel = this.selectChatModel(config);
        
        ChatClient.Builder builder = ChatClient.builder(selectedModel);
        
        // 根据模型类型配置选项
        configureChatOptions(builder, selectedModel, config);
        
        // 配置顾问
        configureAdvisors(builder, config);
        
        return builder.build();
    }

    /**
     * 根据配置选择合适的 ChatModel
     */
    private ChatModel selectChatModel(ChatClientConfig config) {
        AiModelTypeEnum modelType = config.getModelType();

        ChatModel defaultChatModel = availableChatModels.get(0);
        if (modelType == null) {
            log.debug("未指定模型类型，使用默认模型: {}", defaultChatModel.getClass().getSimpleName());
            return defaultChatModel;
        }
        
        // 遍历可用模型，查找匹配的适配器
        for (ChatModel chatModel : availableChatModels) {
            Optional<IChatModelAdapter> adapter = this.findAdapter(modelType, chatModel);
            if (adapter.isPresent()) {
                log.debug("选择模型: {} 用于类型: {} (适配器: {})", 
                    chatModel.getClass().getSimpleName(), modelType, adapter.get().getAdapterName());
                return chatModel;
            }
        }
        
        log.warn("未找到匹配的模型类型: {}，使用默认模型: {}", modelType, defaultChatModel.getClass().getSimpleName());
        return defaultChatModel;
    }

    /**
     * 查找支持指定模型类型和 ChatModel 的适配器
     */
    private Optional<IChatModelAdapter> findAdapter(AiModelTypeEnum modelType, ChatModel chatModel) {
        if (modelType == null || chatModel == null) {
            return Optional.empty();
        }

        return adapters.stream()
            .filter(adapter -> adapter.supports(modelType, chatModel))
            .findFirst(); // 返回第一个匹配的适配器（优先级最高）
    }

    /**
     * 配置 ChatClient 选项
     */
    private void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config) {
        AiModelTypeEnum modelType = config.getModelType();
        if (modelType == null) {
            log.debug("未指定模型类型，跳过选项配置");
            return;
        }

        // 查找合适的适配器
        Optional<IChatModelAdapter> adapter = findAdapter(modelType, chatModel);
        if (adapter.isPresent()) {
            adapter.get().configureChatOptions(builder, chatModel, config);
        } else {
            log.warn("未找到适配器用于模型类型: {} 和模型: {}", modelType, chatModel.getClass().getSimpleName());
        }
    }

    /**
     * 配置顾问
     */
    private void configureAdvisors(ChatClient.Builder builder, ChatClientConfig config) {
        if (config.isEnableMemory()) {
//            ChatMemory chatMemory = MessageWindowChatMemory.builder().build();
//            builder.defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build());
        }

        if (config.isEnableLogging()) {
            builder.defaultAdvisors(new SimpleLoggerAdvisor());
        }
    }

    /**
     * 注册新的适配器（支持运行时扩展）
     */
    public synchronized void registerAdapter(IChatModelAdapter adapter) {
        if (adapter == null) {
            throw new IllegalArgumentException("适配器不能为空");
        }

        // 检查是否已存在同名适配器
        boolean exists = adapters.stream()
            .anyMatch(existing -> existing.getAdapterName().equals(adapter.getAdapterName()));
        
        if (exists) {
            log.warn("适配器 {} 已存在，将被替换", adapter.getAdapterName());
            adapters.removeIf(existing -> existing.getAdapterName().equals(adapter.getAdapterName()));
        }

        adapters.add(adapter);
        adapters.sort(Comparator.comparingInt(IChatModelAdapter::getPriority));
        
        log.info("已注册适配器: {} (优先级: {})", adapter.getAdapterName(), adapter.getPriority());
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(AITaskModel.ChatClientConfig config) {
        return String.format("chatClient_%s_%s_%s_%s_%s",
            config.getModelType() != null ? config.getModelType() : "default",
            config.getSessionId() != null ? config.getSessionId() : "default",
            config.getTemperature(),
            config.isEnableMemory(),
            config.isEnableLogging());
    }
}