package com.mlc.ai.task.scheduler.manager;

import com.mlc.ai.task.scheduler.status.SchedulerStatusEvent;
import com.mlc.ai.task.scheduler.status.SchedulerStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 调度器状态管理器
 * 负责管理调度器的状态变化和状态事件
 */
@Slf4j
@Getter
public class SchedulerStatusManager {

    /**
     * 调度器ID
     */
    private final String schedulerId;

    /**
     * 当前状态
     */
    private volatile SchedulerStatus currentStatus = SchedulerStatus.IDLE;

    /**
     * 整体任务执行是否完成
     */
    private final AtomicBoolean completed = new AtomicBoolean(false);

    /**
     * 完成回调列表
     */
    private final List<Runnable> completionCallbacks = new ArrayList<>();

    /**
     * 调度器状态变化事件流
     */
    private final Flux<SchedulerStatusEvent> statusFlux;

    /**
     * 调度器状态变化事件接收器
     */
    private FluxSink<SchedulerStatusEvent> statusSink;

    /**
     * 构造函数
     *
     * @param schedulerId 调度器ID
     */
    public SchedulerStatusManager(String schedulerId) {
        this.schedulerId = schedulerId;
        this.statusFlux = Flux.create(sink -> this.statusSink = sink);
    }

    /**
     * 检查是否已完成
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return completed.get();
    }

    /**
     * 设置完成状态
     *
     * @param completed 是否完成
     */
    public void setCompleted(boolean completed) {
        this.completed.set(completed);
    }

    /**
     * 通知状态变化
     *
     * @param newStatus 新状态
     */
    public void changeStatus(SchedulerStatus newStatus) {
        SchedulerStatus oldStatus = this.currentStatus;
        this.currentStatus = newStatus;

        if (statusSink != null) {
            SchedulerStatusEvent event = new SchedulerStatusEvent(schedulerId, oldStatus, newStatus);
            statusSink.next(event);
        }

        log.debug("[{}] 调度器状态变更: {} -> {}", schedulerId, oldStatus, newStatus);
    }

    /**
     * 注册完成回调
     *
     * @param callback 回调函数
     */
    public void registerCompletionCallback(Runnable callback) {
        completionCallbacks.add(callback);
    }

    /**
     * 触发完成回调
     */
    public void notifyCompletion() {
        completionCallbacks.forEach(callback -> {
            try {
                callback.run();
            } catch (Exception e) {
                log.error("[{}] 执行完成回调时发生异常", schedulerId, e);
            }
        });
    }

    /**
     * 检查是否可以启动
     *
     * @return 是否可以启动
     */
    public boolean canStart() {
        return currentStatus == SchedulerStatus.IDLE;
    }

    /**
     * 检查是否正在运行
     *
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return currentStatus == SchedulerStatus.RUNNING;
    }

    /**
     * 检查是否已失败
     *
     * @return 是否已失败
     */
    public boolean isFailed() {
        return currentStatus == SchedulerStatus.FAILED;
    }

    /**
     * 检查是否已取消
     *
     * @return 是否已取消
     */
    public boolean isCancelled() {
        return currentStatus == SchedulerStatus.CANCELLED;
    }
}
