package com.mlc.ai.task.cache.adapter;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.task.model.AITaskModel.ChatClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.vertexai.gemini.VertexAiGeminiChatModel;
import org.springframework.ai.vertexai.gemini.VertexAiGeminiChatOptions;

/**
 * Vertex AI Gemini ChatModel 适配器
 */
@Slf4j
public class VertexAiGeminiChatModelAdapter implements IChatModelAdapter {

    @Override
    public boolean supports(AiModelTypeEnum modelType, ChatModel chatModel) {
        return modelType == AiModelTypeEnum.GEMINI && 
               chatModel instanceof VertexAiGeminiChatModel;
    }

    @Override
    public void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config) {
        if (!(chatModel instanceof VertexAiGeminiChatModel)) {
            log.warn("ChatModel 不是 VertexAiGeminiChatModel 类型，跳过配置");
            return;
        }

        VertexAiGeminiChatOptions options = buildVertexAiGeminiChatOptions(config);
        if (options != null) {
            builder.defaultOptions(options);
            log.debug("已配置 Vertex AI Gemini Chat Options: 温度={}, 最大令牌={}", 
                config.getTemperature(), config.getMaxTokens());
        }
    }

    @Override
    public int getPriority() {
        return 10;
    }

    @Override
    public String getAdapterName() {
        return "VertexAiGeminiChatModelAdapter";
    }

    /**
     * 构建 Vertex AI Gemini Chat Options
     * 注意：Vertex AI Gemini 可能不支持 frequencyPenalty 和 presencePenalty
     */
    private VertexAiGeminiChatOptions buildVertexAiGeminiChatOptions(ChatClientConfig config) {
        VertexAiGeminiChatOptions.Builder optionsBuilder = VertexAiGeminiChatOptions.builder();
        
        if (config.getTemperature() != null) {
            optionsBuilder.temperature(config.getTemperature());
        }
        if (config.getMaxTokens() != null) {
            optionsBuilder.maxOutputTokens(config.getMaxTokens());
        }
        if (config.getTopP() != null) {
            optionsBuilder.topP(config.getTopP());
        }

        return optionsBuilder.build();
    }
} 