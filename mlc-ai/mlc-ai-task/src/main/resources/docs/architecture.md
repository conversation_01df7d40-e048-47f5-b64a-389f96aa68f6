# 任务编排系统架构设计

## 1. 核心架构

任务编排系统是一个基于 DAG（有向无环图）的任务调度和执行框架，支持复杂业务流程的定义、执行和监控。系统设计遵循以下核心原则：

- **单一职责**：每个组件专注于自己的职责，任务模型仅关注任务本身的执行逻辑。
- **依赖倒置**：通过接口抽象解耦组件之间的依赖关系。
- **事件驱动**：任务状态变化通过事件机制通知 DAG 引擎。
- **可扩展性**：支持动态添加任务和依赖，灵活扩展工作流定义。

系统架构总览：

```
+---------------------------+     +---------------------------+
|                           |     |                           |
|    Workflow Manager       |     |    Task Executor Registry |
|                           |     |                           |
+------------+--------------+     +--------------+------------+
             |                                   |
             v                                   v
+------------+--------------+     +--------------+------------+
|                           |     |                           |
|     DAG Engine            |     |    Task Executors         |
|  (Topology Management)    |<--->|    (Task Execution)       |
|                           |     |                           |
+------------+--------------+     +--------------+------------+
             |                                   ^
             v                                   |
+------------+--------------+                    |
|                           |                    |
|  Task Execution Engine    +--------------------+
|  (Scheduling & Execution) |
|                           |
+------------+--------------+
             |
             v
+------------+--------------+
|                           |
|     Execution Context     |
|                           |
+---------------------------+
```

## 2. 核心组件

### 2.1 工作流管理器（WorkflowManager）

工作流管理器是系统对外的主要接口，提供高级工作流管理功能：

- 创建和管理工作流定义
- 添加和删除任务
- 管理任务依赖关系
- 触发工作流执行
- 监控工作流执行状态

```
+---------------------------------------------+
|              WorkflowManager                |
+---------------------------------------------+
| - workflowDefinitions: Map<String, DagEngine>
| - runningInstances: Map<String, TaskExecutionEngine>
+---------------------------------------------+
| + createWorkflow(name, description): String |
| + addTask(workflowId, task): String         |
| + addDependency(workflowId, from, to): bool |
| + executeWorkflow(workflowId, params): Flux |
| + cancelExecution(executionId): bool        |
| + dynamicAddTask(execId, task, preds, succs)|
+---------------------------------------------+
```

### 2.2 DAG 引擎（DagEngine）

DAG 引擎负责维护任务的拓扑结构，是工作流定义的核心：

- 维护任务之间的依赖关系（基于 JGraphT 框架）
- 检测循环依赖
- 生成拓扑排序
- 生成执行计划
- 支持动态修改 DAG 结构

```
+---------------------------------------------+
|                 DagEngine                   |
+---------------------------------------------+
| - tasks: Map<String, BaseTaskModel>        |
| - graph: Graph<String, DefaultEdge>        |
| - statusListeners: List<Consumer<Event>>   |
| - dagLock: ReentrantReadWriteLock           |
+---------------------------------------------+
| + addTask(task): String                     |
| + addDependency(from, to): bool             |
| + removeDependency(from, to): bool          |
| + dynamicAddTask(task, preds, succs): String|
| + getTopologicalOrder(): List<String>       |
| + generateSchedule(startId): List<String>   |
| + getExecutableTasks(): Set<String>         |
+---------------------------------------------+
```

### 2.3 任务执行引擎（TaskExecutionEngine）

任务执行引擎负责任务的调度和执行：

- 根据 DAG 结构调度任务
- 管理任务执行状态
- 处理任务执行异常和重试
- 处理任务完成后的后续操作
- 支持并行任务执行

```
+---------------------------------------------+
|            TaskExecutionEngine              |
+---------------------------------------------+
| - dagEngine: DagEngine                      |
| - context: ExecutionContext                 |
| - executorProvider: Function<String, ITaskExecutor>
| - readyTasksQueue: Queue<String>            |
| - runningTasks: AtomicInteger               |
+---------------------------------------------+
| + start(): Flux<String>                     |
| + cancel(): void                            |
| - scheduleNextTasks(sink): void             |
| - executeTask(task, sink): Flux<String>     |
| - onTaskStatusChange(event): void           |
+---------------------------------------------+
```

### 2.4 任务模型（TaskModel）

任务模型表示一个独立的处理单元，关注自身的执行逻辑：

- 任务基本信息（ID、名称、类型）
- 任务配置参数
- 任务状态
- 任务执行器引用

```
+---------------------------------------------+
|                TaskModel                    |
+---------------------------------------------+
| - id: String                                |
| - name: String                              |
| - type: TaskType                            |
| - config: TaskConfig                        |
| - status: TaskStatus                        |
| - executorKey: String                       |
+---------------------------------------------+
| + enum TaskType: NORMAL, CONDITIONAL, SUB_WORKFLOW
| + enum TaskStatus: PENDING, RUNNING, COMPLETED...
| + class TaskConfig: params, timeoutMs, maxRetries...
+---------------------------------------------+
```

### 2.5 任务执行器（TaskExecutor）

任务执行器负责任务的具体执行逻辑：

- 执行不同类型的任务处理逻辑
- 处理任务超时和错误
- 返回任务执行结果

```
+---------------------------------------------+
|          <<interface>> ITaskExecutor        |
+---------------------------------------------+
| + execute(task, context): Flux<String>      |
+---------------------------------------------+
         ^
         |
         |
+---------------------------------------------+
|            DefaultTaskExecutor              |
+---------------------------------------------+
| + execute(task, context): Flux<String>      |
| # processNormalTask(task, context): String  |
| # processConditionalTask(task, context): String
| # processSubWorkflowTask(task, context): String
+---------------------------------------------+
```

### 2.6 执行上下文（ExecutionContext）

执行上下文存储工作流执行过程中的状态和数据：

- 执行实例 ID
- 上下文属性（输入/输出参数）
- 任务状态变更监听器
- 错误状态标志

```
+---------------------------------------------+
|            ExecutionContext                 |
+---------------------------------------------+
| - executionId: String                       |
| - attributes: Map<ContextKey<?>, Object>    |
| - errorOccurred: AtomicBoolean              |
| - taskStatusListeners: Map<String, Set<Listener>>
+---------------------------------------------+
| + getAttribute(key): <T> T                  |
| + setAttribute(key, value): <T> void        |
| + notifyTaskStatusChange(id, old, new): void|
+---------------------------------------------+
```

## 3. 关键流程

### 3.1 工作流创建流程

```
┌─────────┐      ┌─────────┐       ┌─────────┐
│  Client  │      │WorkflowMgr│      │DagEngine│
└────┬────┘      └────┬────┘       └────┬────┘
     │                │                  │
     │ createWorkflow │                  │
     │───────────────>│                  │
     │                │                  │
     │                │ create DagEngine │
     │                │─────────────────>│
     │                │                  │
     │                │                  │
     │                │ workflowId       │
     │                │<─────────────────│
     │                │                  │
     │ workflowId     │                  │
     │<───────────────│                  │
     │                │                  │
     │ addTask        │                  │
     │───────────────>│                  │
     │                │                  │
     │                │ addTask          │
     │                │─────────────────>│
     │                │                  │
     │                │ taskId           │
     │                │<─────────────────│
     │                │                  │
     │ taskId         │                  │
     │<───────────────│                  │
     │                │                  │
     │ addDependency  │                  │
     │───────────────>│                  │
     │                │                  │
     │                │ addDependency    │
     │                │─────────────────>│
     │                │                  │
     │                │ result           │
     │                │<─────────────────│
     │                │                  │
     │ result         │                  │
     │<───────────────│                  │
     │                │                  │
└────┬────┘      └────┬────┘       └────┬────┘
```

### 3.2 工作流执行流程

```
┌────────┐    ┌────────┐    ┌────────┐    ┌────────┐    ┌────────┐
│ Client  │    │WorkflowMgr│    │TaskExecEng│   │DagEngine│    │TaskExecutor│
└───┬────┘    └────┬───┘    └────┬───┘    └────┬───┘    └────┬───┘
    │              │              │             │             │
    │executeWorkflow│             │             │             │
    │──────────────>│             │             │             │
    │              │              │             │             │
    │              │create ExecutionContext│    │             │
    │              │──────────────│             │             │
    │              │              │             │             │
    │              │create TaskExecutionEngine  │             │
    │              │──────────────>│             │             │
    │              │              │             │             │
    │              │              │initialize   │             │
    │              │              │───────────>│             │
    │              │              │<───────────│             │
    │              │              │             │             │
    │              │start         │             │             │
    │              │──────────────>│             │             │
    │              │              │             │             │
    │              │              │get executable tasks       │
    │              │              │───────────>│             │
    │              │              │<───────────│             │
    │              │              │             │             │
    │              │              │scheduleNextTasks          │
    │              │              │────────────────┐          │
    │              │              │                │          │
    │              │              │<───────────────┘          │
    │              │              │             │             │
    │              │              │executeTask  │             │
    │              │              │─────────────────────────>│
    │              │              │             │             │
    │              │              │             │             │
    │              │              │task results │             │
    │              │              │<─────────────────────────│
    │              │              │             │             │
    │              │              │update task status        │
    │              │              │───────────>│             │
    │              │              │<───────────│             │
    │              │              │             │             │
    │              │              │continue with next tasks  │
    │              │              │────────────────┐          │
    │              │              │                │          │
    │              │              │<───────────────┘          │
    │              │              │             │             │
    │              │execution results           │             │
    │              │<─────────────│             │             │
    │              │              │             │             │
    │execution results            │             │             │
    │<─────────────│              │             │             │
    │              │              │             │             │
└───┬────┘    └────┬───┘    └────┬───┘    └────┬───┘    └────┬───┘
```

### 3.3 动态任务添加流程

```
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│ Client   │    │WorkflowMgr│    │TaskExecEng│    │DagEngine │
└────┬────┘    └────┬────┘    └────┬────┘    └────┬────┘
     │              │              │              │
     │dynamicAddTask│              │              │
     │──────────────>              │              │
     │              │              │              │
     │              │getRunningInstance           │
     │              │───────────────>             │
     │              │<───────────────│              │
     │              │              │              │
     │              │getDagEngine  │              │
     │              │───────────────>             │
     │              │              │              │
     │              │              │              │
     │              │              │dynamicAddTask│
     │              │              │──────────────>
     │              │              │              │
     │              │              │              │
     │              │              │taskId        │
     │              │              │<──────────────
     │              │              │              │
     │              │taskId        │              │
     │              │<──────────────              │
     │              │              │              │
     │taskId        │              │              │
     │<─────────────│              │              │
     │              │              │              │
└────┬────┘    └────┬────┘    └────┬────┘    └────┬────┘
```

## 4. 核心功能

### 4.1 依赖关系管理

DAG 引擎通过 JGraphT 框架管理任务间的依赖关系：

- 使用 DirectedAcyclicGraph 确保图的无环性
- 通过 DefaultEdge 表示任务间的依赖关系
- 利用 JGraphT 的内置算法进行拓扑排序和图遍历
- 支持高效的前驱和后继任务查询

这种结构使得：

- 快速找到任务的前驱和后继任务
- 高效地检测循环依赖
- 快速生成拓扑排序
- 支持动态添加/删除依赖
- 利用成熟的图算法库提高性能和可靠性

### 4.2 任务调度

任务执行引擎基于事件驱动的调度机制：

1. 将入度为 0 的任务加入可执行队列
2. 从可执行队列中取出任务执行
3. 当任务完成时，更新其后继任务的入度
4. 将入度变为 0 的后继任务加入可执行队列
5. 重复步骤 2-4 直到所有任务执行完成

这种机制支持：

- 自动根据依赖关系调度任务
- 并行执行无依赖关系的任务
- 动态调整执行计划

### 4.3 状态管理与事件通知

系统采用事件驱动机制管理任务状态：

1. 任务执行完成后，通过事件通知 DAG 引擎
2. DAG 引擎更新任务状态和依赖关系
3. 根据状态变化触发后续任务执行

这种机制实现了：

- 任务与 DAG 引擎的解耦
- 灵活的状态变化处理
- 支持多种监听器订阅状态变化

### 4.4 错误处理与恢复

系统提供了完善的错误处理机制：

- 任务级重试：配置任务重试次数和策略
- 错误传播：任务失败信息通过事件机制传播
- 工作流级错误处理：根据配置决定是否继续执行后续任务
- 超时处理：为任务设置超时时间，自动中断超时任务

### 4.5 动态调整 DAG

系统支持在工作流执行过程中动态调整 DAG 结构：

- 添加新任务
- 添加/删除依赖关系
- 根据条件跳过任务
- 支持分支和合并处理

这种灵活性使得系统能够：

- 根据运行时条件调整执行路径
- 处理需要动态生成的子任务
- 支持复杂的业务场景


## 核心特性

- **基于 DAG 的任务编排**：通过有向无环图描述任务间的依赖关系
- **事件驱动设计**：任务状态变化通过事件在组件之间传递
- **灵活的执行模型**：支持重试、超时机制、条件执行等
- **动态工作流**：支持在运行时动态修改工作流结构
- **自定义任务执行器**：可扩展的执行器体系，支持不同类型任务的定制化处理
- **上下文连贯性**：任务间可共享数据，支持数据转换和预处理
- **JSON 定义工作流**：支持从 JSON 格式定义和导入工作流