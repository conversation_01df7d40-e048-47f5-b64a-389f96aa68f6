<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>flowweb-zhongzhi</groupId>
    <artifactId>mlc-ai</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>mlc-ai-task</artifactId>
  <modelVersion>4.0.0</modelVersion>

  <dependencies>
    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-ai-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.openjdk.nashorn</groupId>
      <artifactId>nashorn-core</artifactId>
      <version>15.4</version>
    </dependency>
  </dependencies>

</project>