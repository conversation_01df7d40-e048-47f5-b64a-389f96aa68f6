/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.ai.dsl;

import io.nop.ai.core.model.PromptModel;
import io.nop.api.core.ApiConfigs;
import io.nop.api.core.config.AppConfig;
import io.nop.core.initialize.CoreInitialization;
import io.nop.core.lang.xml.XNode;
import io.nop.core.unittest.BaseTestCase;
import io.nop.xlang.xdef.IXDefNode;
import io.nop.xlang.xdef.IXDefinition;
import io.nop.xlang.xmeta.SchemaLoader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

@Slf4j
public class LoadXdefTest extends BaseTestCase {

    @BeforeAll
    public static void setUp() {
        AppConfig.getConfigProvider().updateConfigValue(ApiConfigs.CFG_EXCEPTION_FILL_STACKTRACE, true);
        CoreInitialization.initialize();
    }

    @AfterAll
    public static void tearDown() {
        CoreInitialization.destroy();
    }

    @Test
    public void load() {
     final Map<String, PromptModel> definitions = new HashMap<>();

    // <ChildKey, List<ParentKey>>
     final Map<String, List<String>> parentMap = new HashMap<>();

    // <ParentKey, List<ChildKey>>
     final Map<String, List<String>> childrenMap = new HashMap<>();

     final Set<String> topLevelKeys = new HashSet<>();

        log.info("信息：尝试从 orm.xdef 加载模型定义。");
        try {
            // /nop/schema/orm/orm.xdef
            IXDefinition ixDefinition = SchemaLoader.loadXDefinition("/nop/schema/orm/orm.xdef");
            XNode node = ixDefinition.toNode();
            System.out.println("XDef节点信息: " + node.jsonText());


            // 预先加载YAML中的name和template信息，以便后续填充
//            Map<String, Map<String, Object>> yamlData = loadYamlData();
//            if (yamlData == null || yamlData.isEmpty()) {
//                log.warn("警告：未找到或无法解析 YAML 定义文件，将只加载结构关系但没有名称和模板。");
//            } else {
//                log.info("成功加载YAML数据，包含以下键: {}", yamlData.keySet());
//            }

            // 从IXDefinition中加载根节点（orm）
            String rootKey = ixDefinition.getTagName();

            // 将根节点添加到定义和顶层Keys中
//            addDefinition(rootKey, yamlData);
            topLevelKeys.add(rootKey);
            log.info("添加根节点: {}", rootKey);

            // 使用广度优先搜索遍历XDef树结构
            Queue<KeyNodePair> queue = new LinkedList<>();
            // 首先将根节点的所有子节点加入队列
            Map<String, ? extends IXDefNode> rootChildren = ixDefinition.getChildren();

            if (rootChildren != null && !rootChildren.isEmpty()) {
                log.info("根节点 {} 有 {} 个子节点", rootKey, rootChildren.size());
                for (Map.Entry<String, ? extends IXDefNode> entry : rootChildren.entrySet()) {
                    String childKey = entry.getKey();
                    IXDefNode childNode = entry.getValue();
                    queue.add(new KeyNodePair(childKey, childNode));

                    // 添加父子关系
                    parentMap.computeIfAbsent(childKey, k -> new ArrayList<>()).add(rootKey);
                    childrenMap.computeIfAbsent(rootKey, k -> new ArrayList<>()).add(childKey);
                    log.debug("添加父子关系: {} -> {}", rootKey, childKey);
                }
            } else {
                log.warn("根节点 {} 没有子节点", rootKey);
            }

            // 广度优先遍历处理所有节点
            while (!queue.isEmpty()) {
                KeyNodePair pair = queue.poll();
                String currentKey = pair.key;
                IXDefNode currentNode = pair.node;

                // 将当前节点添加到定义中
//                addDefinition(currentKey, yamlData);

                // 处理该节点的子节点
                Map<String, ? extends IXDefNode> children = currentNode.getChildren();
                if (children != null && !children.isEmpty()) {
                    log.debug("节点 {} 有 {} 个子节点", currentKey, children.size());
                    for (Map.Entry<String, ? extends IXDefNode> entry : children.entrySet()) {
                        String childKey = entry.getKey();
                        IXDefNode childNode = entry.getValue();
                        queue.add(new KeyNodePair(childKey, childNode));

                        // 添加父子关系
                        parentMap.computeIfAbsent(childKey, k -> new ArrayList<>()).add(currentKey);
                        childrenMap.computeIfAbsent(currentKey, k -> new ArrayList<>()).add(childKey);
                        log.debug("添加父子关系: {} -> {}", currentKey, childKey);
                    }
                }
            }

            // 重新计算顶层Keys（没有父级的定义）
            topLevelKeys.clear();
            for (String key : definitions.keySet()) {
                if (!parentMap.containsKey(key)) {
                    topLevelKeys.add(key);
                }
            }

            log.info("成功从 orm.xdef 加载模型定义。总共 {} 个定义, 顶层节点: {}", definitions.size(), topLevelKeys);
            log.debug("父子关系映射: {}", parentMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static class KeyNodePair {
        String key;
        IXDefNode node;

        KeyNodePair(String key, IXDefNode node) {
            this.key = key;
            this.node = node;
        }
    }
}
