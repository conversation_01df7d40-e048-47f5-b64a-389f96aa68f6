package com.mlc.ai.dsl;

import com.mlc.ai.dsl.transform.AiOrmConfig;
import com.mlc.ai.dsl.transform.AiOrmModelNormalizer;
import io.nop.api.core.ApiConfigs;
import io.nop.api.core.config.AppConfig;
import io.nop.core.initialize.CoreInitialization;
import io.nop.core.lang.xml.XNode;
import io.nop.core.lang.xml.parse.XNodeParser;
import io.nop.core.unittest.BaseTestCase;
import io.nop.orm.model.OrmModel;
import io.nop.xlang.xdsl.DslModelParser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

@Slf4j
public class ParseAIDslTest extends BaseTestCase {

    @BeforeAll
    public static void setUp() {
        AppConfig.getConfigProvider().updateConfigValue(ApiConfigs.CFG_EXCEPTION_FILL_STACKTRACE, true);
        CoreInitialization.initialize();
    }

    @AfterAll
    public static void tearDown() {
        CoreInitialization.destroy();
    }

    // 普通的DSL解析
    public void testNormalParse() {
        String dsl = "parse this text";
        // 断言结果
        assert dsl.equals("parsed text");
    }

    // 解析带多对多关系的DSL
    @Test
    public void testManyToManyParse() {
        String dsl = """
            <orm>
                <entities>
                    <entity name="Vehicle" displayName="车辆" tableName="vehicle">
                        <columns>
                            <column name="rowid" displayName="ID" primary="true" mandatory="true" stdDomain="auto_id"
                                    stdSqlType="BIGINT"/>
                            <column name="plateNumber" displayName="车牌号" primary="false" mandatory="true" stdDomain="text"
                                    stdSqlType="VARCHAR(20)"/>
                            <column name="vehicleType" displayName="车辆类型" primary="false" mandatory="true" stdDomain="flat_menu"
                                    stdSqlType="VARCHAR(20)"/>
                            <column name="purchaseDate" displayName="购置日期" primary="false" mandatory="true" stdDomain="date"
                                    stdSqlType="DATE"/>
                            <column name="status" displayName="状态" primary="false" mandatory="true" stdDomain="flat_menu"
                                    stdSqlType="VARCHAR(20)"/>
                        </columns>
                        <relations>
                            <to-many name="maintenanceRecords" displayName="维修记录" refEntityName="MaintenanceRecord">
                                <join>
                                    <on leftProp="rowid" rightProp="vehicleId"/>
                                </join>
                            </to-many>
                            <to-many name="driverAssignments" displayName="驾驶员分配" refEntityName="VehicleDriver">
                                <join>
                                    <on leftProp="rowid" rightProp="vehicleId"/>
                                </join>
                            </to-many>
                        </relations>
                    </entity>
                    <entity name="Driver" displayName="驾驶员" tableName="driver">
                        <columns>
                            <column name="rowid" displayName="ID" primary="true" mandatory="true" stdDomain="auto_id"
                                    stdSqlType="BIGINT"/>
                            <column name="name" displayName="姓名" primary="false" mandatory="true" stdDomain="text"
                                    stdSqlType="VARCHAR(50)"/>
                            <column name="licenseNumber" displayName="驾驶证号" primary="false" mandatory="true" stdDomain="text"
                                    stdSqlType="VARCHAR(20)"/>
                            <column name="licenseType" displayName="驾照类型" primary="false" mandatory="true" stdDomain="flat_menu"
                                    stdSqlType="VARCHAR(20)"/>
                            <column name="phone" displayName="联系电话" primary="false" mandatory="true" stdDomain="telephone"
                                    stdSqlType="VARCHAR(20)"/>
                        </columns>
                        <relations>
                            <to-many name="vehicleAssignments" displayName="车辆分配" refEntityName="VehicleDriver">
                                <join>
                                    <on leftProp="rowid" rightProp="driverId"/>
                                </join>
                            </to-many>
                        </relations>
                    </entity>
                    <entity name="VehicleDriver" displayName="车辆驾驶员关系" tableName="vehicle_driver">
                        <columns>
                            <column name="rowid" displayName="ID" primary="true" mandatory="true" stdDomain="auto_id"
                                    stdSqlType="BIGINT"/>
                            <column name="vehicleId" displayName="车辆ID" primary="false" mandatory="true" stdDomain="number"
                                    stdSqlType="BIGINT"/>
                            <column name="driverId" displayName="驾驶员ID" primary="false" mandatory="true" stdDomain="number"
                                    stdSqlType="BIGINT"/>
                            <column name="assignDate" displayName="分配日期" primary="false" mandatory="true" stdDomain="date"
                                    stdSqlType="DATE"/>
                            <column name="isActive" displayName="是否有效" primary="false" mandatory="true" stdDomain="boolean"
                                    stdSqlType="BOOLEAN"/>
                        </columns>
                        <relations>
                            <to-one name="vehicle" displayName="车辆" refEntityName="Vehicle">
                                <join>
                                    <on leftProp="vehicleId" rightProp="rowid"/>
                                </join>
                            </to-one>
                            <to-one name="driver" displayName="驾驶员" refEntityName="Driver">
                                <join>
                                    <on leftProp="driverId" rightProp="rowid"/>
                                </join>
                            </to-one>
                        </relations>
                    </entity>
                    <entity name="MaintenanceRecord" displayName="维修记录" tableName="maintenance_record">
                        <columns>
                            <column name="rowid" displayName="ID" primary="true" mandatory="true" stdDomain="auto_id"
                                    stdSqlType="BIGINT"/>
                            <column name="vehicleId" displayName="车辆ID" primary="false" mandatory="true" stdDomain="number"
                                    stdSqlType="BIGINT"/>
                            <column name="maintenanceDate" displayName="维修日期" primary="false" mandatory="true" stdDomain="date"
                                    stdSqlType="DATE"/>
                            <column name="cost" displayName="维修费用" primary="false" mandatory="true" stdDomain="money"
                                    stdSqlType="DECIMAL(10,2)"/>
                            <column name="description" displayName="维修描述" primary="false" mandatory="false" stdDomain="text"
                                    stdSqlType="VARCHAR(500)"/>
                        </columns>
                        <relations>
                            <to-one name="vehicle" displayName="车辆" refEntityName="Vehicle">
                                <join>
                                    <on leftProp="vehicleId" rightProp="rowid"/>
                                </join>
                            </to-one>
                        </relations>
                    </entity>
                </entities>
            </orm>
            """;
        // 解析DSL
        XNode xNode = XNodeParser.instance().parseFromText(null, dsl);

        AiOrmConfig config = new AiOrmConfig();
        config.setBasePackageName("app");

        AiOrmModelNormalizer aiOrmModelNormalizer = new AiOrmModelNormalizer();
        aiOrmModelNormalizer.fixNameForOrmNode(xNode);

        aiOrmModelNormalizer.normalizeOrm(xNode, config);

        OrmModel ormModel = (OrmModel) new DslModelParser().parseFromNode(xNode);
        // 断言结果
        assert ormModel.getEntities().size() == 4;
    }

    // 解析带树形结构的DSL
    public void testTreeParse() {
        String dsl = "parse this text with tree structure";
        // 解析DSL
        // 断言结果
        assert dsl.equals("parsed text with tree structure");
    }

}
