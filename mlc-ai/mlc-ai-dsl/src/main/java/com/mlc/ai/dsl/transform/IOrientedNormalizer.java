package com.mlc.ai.dsl.transform;

import io.nop.core.lang.xml.XNode;

/**
 * 接口：面向特定领域的规范化器
 * 该接口定义了两个方法：transform和validate
 * transform方法用于转换解析节点
 * validate方法用于验证解析节点
 */
public interface IOrientedNormalizer {


    /**
     * 转换解析节点
     * @param parseNode 需要转换的解析节点
     * @return 转换后的解析节点
     */
    XNode transform(XNode parseNode);


    /**
     * 验证解析节点
     * @param validateNode 需要验证的解析节点
     * @return true 如果验证通过，否则返回 false
     */
    boolean validate(XNode validateNode);
}
