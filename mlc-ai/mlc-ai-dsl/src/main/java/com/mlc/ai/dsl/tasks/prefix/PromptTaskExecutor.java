package com.mlc.ai.dsl.tasks.prefix;

import com.mlc.ai.dsl.model.AbstractPromptAgentModel;
import com.mlc.ai.dsl.tasks.AbstractTaskExecutor;
import com.mlc.ai.dsl.tasks.context.ContextKeys;
import com.mlc.ai.dsl.tasks.context.ContextKeys.FailureInfo;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import io.nop.ai.core.model.PromptModel;
import io.nop.core.lang.eval.IEvalScope;
import java.util.HashMap;
import java.util.Queue;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 提示任务执行器
 */
@Slf4j
public class PromptTaskExecutor extends AbstractTaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {

        Object executionQueue = task.getTaskContext().getInput().get("PlanBuildExecutor.executionQueue");
        if (executionQueue == null) {
            log.warn("[{}] PromptTaskExecutor 未找到执行队列", context.getExecutionId());
            return Flux.error(new IllegalStateException("❌ 上下文中未包含执行队列"));
        }
        Queue<String> beExecutedQueue = (Queue<String>) executionQueue;

        final AbstractPromptAgentModel promptAgentModel = context.getAttribute(ContextKeys.ABSTRACT_PROMPT_AGENT_MODEL);
        List<KeyMessagePair> keyMessagePairs = context.getAttribute(ContextKeys.KEY_MESSAGE_PAIRS);
        if (keyMessagePairs == null) {
            return Flux.error(new IllegalStateException("❌ 上下文中未包含 keyMessagePairs"));
        }

        Flux<String> initialMessageStream = Flux.just(
            "🔄 将按以下顺序为您处理请求：" + beExecutedQueue.stream().map(key -> "@" + key).collect(Collectors.joining(" -> ")) + "\n\n"
        );

        Flux<String> processingStepsStream =
            Flux.fromIterable(beExecutedQueue)
                .concatMap(promptKey -> { // 顺序执行每个步骤
                    // 子任务的处理不影响主任务的状态
                    PromptModel promptModel = promptAgentModel.getPromptModel(promptKey);
                    if (promptModel == null) {
                        AtomicReference<FailureInfo> failureInfoRef = context.getAttribute(ContextKeys.FAILURE_INFO_REF);
                        failureInfoRef.set(new FailureInfo(promptKey, new IllegalArgumentException("找不到提示定义：" + promptKey)));
                        return Flux.error(new IllegalArgumentException("❌ 找不到提示定义：" + promptKey));
                    }

                    // 从解析结果中查找对应的用户消息
                    KeyMessagePair keyMessagePair = keyMessagePairs.stream().filter(pair -> pair.getKey().equals(promptKey)).findFirst().get();
                    log.info("[{}] LLM 用户消息 @{}: {}", context.getExecutionId(), promptKey, keyMessagePair.getUserMessage());

                    String stepStartMessage = String.format("▶️ 正在处理: @%s (%s)...\n", promptKey, promptModel.getDescription());

                    IEvalScope iEvalScope = promptModel.getPromptTemplate().prepareInputs(new HashMap<>());
                    String prompt = promptModel.getPromptTemplate().generatePrompt(iEvalScope);

                    Flux<String> baseLlmStream = promptAgentModel.getLlmChatClient().prompt()
                                                                 .system(prompt)
                                                                 .user(keyMessagePair.getUserMessage())
                                                                 .stream()
                                                                 .content();
                    // --- 缓存流以允许多个订阅 ---
                    // 一个订阅将数据块转发到输出 Flux。
                    // 另一个订阅收集完整结果进行解析。
                    Flux<String> cachedLlmStream = baseLlmStream.cache();

                    // 收集完整结果
                    Mono<String> collectedResultMono = cachedLlmStream.collect(Collectors.joining());

                    // 处理 LLM 结果
                    Flux<String> postProcessingStream = collectedResultMono.flatMapMany(resultDsl -> {
                        log.trace("[{}] LLM 原始回复 @{}:\n{}", context.getExecutionId(), promptKey, resultDsl);
                        AtomicReference<String> currentDslRef = context.getAttribute(ContextKeys.CURRENT_STEP_DSL_REF);
                        currentDslRef.set(resultDsl);

                        // 使用流式处理方法
                        return promptAgentModel.processingDSLStream(resultDsl, promptModel)
                            .onErrorResume(parseError -> {
                                log.error("[{}] 解析 LLM 结果失败: @{}. 处理中止并将回滚。", context.getExecutionId(), promptKey, parseError);
                                AtomicReference<FailureInfo> failureInfoRef = context.getAttribute(ContextKeys.FAILURE_INFO_REF);
                                failureInfoRef.set(new FailureInfo(promptKey, parseError));
                                return Flux.empty(); // 交给上层处理
                            });
                    });

                    return Flux.concat(
                        Flux.just(stepStartMessage),
//                        cachedLlmStream // 流式输出 LLM 结果
                        postProcessingStream // 处理完成消息或错误
                    );
                }).onErrorResume(error -> {
                    // 获取失败信息
                    AtomicReference<FailureInfo> failureInfoRef = context.getAttribute(ContextKeys.FAILURE_INFO_REF);
                    FailureInfo failureInfo = failureInfoRef.get();
                    String promptKey = failureInfo != null ? failureInfo.getFailingStepKey() : "unknown";
                    
                    log.error("[{}] LLM 调用或结果处理时出错: @{}. 处理中止并将回滚。", context.getExecutionId(), promptKey, error);

                    // 返回错误信息，但不包装原始错误，因为 postProcessingStream 已经包装了
                    if (error instanceof RuntimeException && error.getMessage().startsWith("❌")) {
                        return Flux.just(error.getMessage());
                    } else {
                        return Flux.just(String.format("❌ 处理失败: @%s，错误: %s。处理中止。\n", promptKey, error.getMessage()));
                    }
                });

        // --- 合并所有流 ---
        return Flux.concat(initialMessageStream, processingStepsStream);
    }
} 