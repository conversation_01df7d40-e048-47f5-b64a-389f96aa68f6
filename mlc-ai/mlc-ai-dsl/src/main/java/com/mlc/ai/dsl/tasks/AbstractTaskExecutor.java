package com.mlc.ai.dsl.tasks;

import com.mlc.ai.task.executor.ITaskExecutor;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务执行器的抽象基类，提供通用功能。
 */
@Slf4j
public abstract class AbstractTaskExecutor implements ITaskExecutor {

    @AllArgsConstructor
    @Getter
    public class KeyMessagePair {
        private String key;
        private String userMessage;
    }
}