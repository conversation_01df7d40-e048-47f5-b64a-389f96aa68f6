package com.mlc.ai.dsl;

import java.io.Serial;
import lombok.Getter;

public class PromptExceptions {

    @Getter
    public static class DefinitionNotFoundException extends RuntimeException {
        @Serial
        private static final long serialVersionUID = -8127993462576151624L;
        private final String key;

        public DefinitionNotFoundException(String key) {
            super("Definition not found: @" + key);
            this.key = key;
        }
    }

    @Getter
    public static class CyclicDependencyException extends RuntimeException {
        @Serial private static final long serialVersionUID = 5917041700219689865L;
        private final String cycleInfo;

        public CyclicDependencyException(String cycleInfo) {
            super("Cyclic dependency detected: " + cycleInfo);
            this.cycleInfo = cycleInfo;
        }
    }

    @Getter
    public static class MissingParentException extends RuntimeException {
        @Serial private static final long serialVersionUID = -7733759784169859664L;
        private final String missingKey;
        private final String parentKey;

        public MissingParentException(String missingKey, String parentKey) {
            super("Missing parent definition '@" + parentKey + "' in current schema for key '@" + missingKey + "'");
            this.missingKey = missingKey;
            this.parentKey = parentKey;
        }
    }
}
