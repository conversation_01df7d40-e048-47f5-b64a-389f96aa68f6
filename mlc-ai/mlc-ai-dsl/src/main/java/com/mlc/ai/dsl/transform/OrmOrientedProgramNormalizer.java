package com.mlc.ai.dsl.transform;

import io.nop.core.lang.xml.XNode;
import io.nop.orm.model.OrmModel;
import io.nop.xlang.xdsl.DslModelParser;

/**
 * 面向ORM的规范化器
 * 该类实现了IOrientedNormalizer接口，负责转换和验证解析节点
 */
public class OrmOrientedProgramNormalizer implements IOrientedNormalizer {

    @Override
    public XNode transform(XNode parseNode) {
        AiOrmConfig config = new AiOrmConfig();
        config.setBasePackageName("app");

        AiOrmModelNormalizer aiOrmModelNormalizer = new AiOrmModelNormalizer();

        // 处理名称冲突
        aiOrmModelNormalizer.fixNameForOrmNode(parseNode);
        // 格式化orm节点
        aiOrmModelNormalizer.normalizeOrm(parseNode, config);
        return parseNode;
    }

    @Override
    public boolean validate(XNode node) {
        try {
            OrmModel ormModel = (OrmModel) new DslModelParser().parseFromNode(node);
            if (ormModel != null) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }
}
