package com.mlc.ai.dsl.transform;

import com.mlc.ai.dsl.utils.AiCoderHelper;
import io.nop.commons.type.StdSqlType;
import io.nop.commons.util.StringHelper;
import io.nop.core.lang.xml.XNode;
import io.nop.dao.dialect.SQLDataType;
import io.nop.xlang.xdef.IStdDomainHandler;
import io.nop.xlang.xdef.domain.StdDomainRegistry;
import io.nop.xlang.xpath.XPathHelper;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AiOrmModelNormalizer {

    private static final String PRIMARY_KEY_ID = "rowid";
    private static final XNode PRIMARY_KEY_COLUMN = XNode.make("column");

    static {
        PRIMARY_KEY_COLUMN.setAttr("code", PRIMARY_KEY_ID);
        PRIMARY_KEY_COLUMN.setAttr("displayName", "主键");
        PRIMARY_KEY_COLUMN.setAttr("stdDomain", "pkId");
        PRIMARY_KEY_COLUMN.setAttr("mandatory", true);
        PRIMARY_KEY_COLUMN.setAttr("name", PRIMARY_KEY_ID);
        PRIMARY_KEY_COLUMN.setAttr("precision", 32);
        PRIMARY_KEY_COLUMN.setAttr("primary", true);
        PRIMARY_KEY_COLUMN.setAttr("stdDataType", "string");
        PRIMARY_KEY_COLUMN.setAttr("stdSqlType", StdSqlType.VARCHAR);
        PRIMARY_KEY_COLUMN.setAttr("tagSet", "seq");
        PRIMARY_KEY_COLUMN.setAttr("propId", 1);
    }

    public XNode fixNameForOrmNode(XNode node) {
        if (node == null)
            return null;
        XNode entities = normalizeEntities(node);
        if (entities != null) {
            for (XNode entity : entities.getChildren()) {
                fixNameForEntityNode(entity);
            }
        }
        return node;
    }

    protected XNode fixNameForEntityNode(XNode entity) {
        String entityName = entity.attrText("name");
        String name = AiCoderHelper.camelCaseName(StringHelper.lastPart(entityName, '.'), true);

        entity.setAttr("name", name);

        XNode columns = entity.makeChild("columns");
        for (XNode col : columns.getChildren()) {
            fixNameForColNode(col);
        }

        return entity;
    }

    protected XNode fixNameForColNode(XNode col) {
        String colName = col.attrText("name");
        String name = AiCoderHelper.camelCaseName(colName, false);
        String stdDomain = col.attrText("stdDomain");
        if (stdDomain != null) {
            stdDomain = stdDomain.trim().toLowerCase(Locale.ROOT);
            IStdDomainHandler domainHandler = getStdDomainHandler(stdDomain);
            if (domainHandler != null) {
                col.setAttr("stdDomain", stdDomain);
            } else {
                col.removeAttr("stdDomain");
            }
        }

        // 处理 sqlType, stdSqlType
        String stdSqlType = col.attrText("stdSqlType");

        if (!StringHelper.isEmpty(stdSqlType)) {
            stdSqlType = stdSqlType.trim().toUpperCase();
            stdSqlType = StringHelper.replace(stdSqlType, " AUTO_INCREMENT", "");
            SQLDataType dataType = SQLDataType.parse(stdSqlType);
            col.setAttr("stdSqlType", AiOrmSqlType.getStdSqlType(dataType.getName()));
            if (dataType.isAllowPrecision())
                col.setAttr("precision", dataType.getPrecision());
            if (dataType.isAllowScale())
                col.setAttr("scale", dataType.getScale());
        } else {
            col.setAttr("stdSqlType", StdSqlType.VARCHAR);
            if (!col.hasAttr("precision"))
                col.setAttr("precision", 50);
        }
        col.setAttr("name", name);
        return col;
    }

    protected IStdDomainHandler getStdDomainHandler(String stdDomain) {
        return StdDomainRegistry.instance().getStdDomainHandler(stdDomain);
    }


    public XNode normalizeOrm(XNode node, AiOrmConfig config) {
        node.setAttr("x:schema", "/nop/schema/orm/orm.xdef");
        node.setAttr("xmlns:x", "/nop/schema/xdsl.xdef");

        if (config.getBasePackageName() != null)
            node.setAttr("ext:basePackageName", config.getBasePackageName());

        XNode entities = normalizeEntities(node);
        if (entities != null) {
            for (XNode entity : entities.getChildren()) {
                decideMiddleEntity(entities, entity);
                normalizeEntity(entity);
            }
        }

        log.debug("nop.ai.normalize:node=\n{}", node.xml());
        return node;
    }

    private XNode normalizeEntities(XNode node) {
        XNode entities = node.childByTag("entities");
        if (entities == null) {
            if (node.childByTag("entity") != null) {
                entities = XNode.make("entities");
                node.wrapChildren(entities);
            }
        }
        return entities;
    }


    public XNode normalizeEntity(XNode entity) {
        String entityName = entity.attrText("name");
        String code = AiCoderHelper.underscoreName(StringHelper.lastPart(entityName, '.'), true);
        entity.setAttr("registerShortName", true);

        String name = AiCoderHelper.camelCaseName(code, true);
        if (StringHelper.isEmpty(entity.attrText("displayName")))
            entity.setAttr("displayName", name);

        entity.setAttr("className", "io.nop.orm.support.DynamicOrmEntity");

        XNode columns = entity.childByTag("columns");
        int nextId = 1;

        List<XNode> columnList = columns.getChildren();

        // 处理主键
        columnList.removeIf(column -> column.attrText("name").equalsIgnoreCase(PRIMARY_KEY_ID));
        columnList.add(0, PRIMARY_KEY_COLUMN); // 主键放在第一位

        for (XNode col : columnList) {
            String colName = col.attrText("name");
            col.setAttr("code", AiCoderHelper.underscoreName(colName, true));
            col.setAttr("propId", nextId++);
        }

        return entity;
    }

    /**
     * 中间表判定: 至少两个 to-one 关联，查找到 to-one 对应的 refEntityName：entity，
     *           如果对应的 entity 中 to-many rightProp = to-one leftProp, 并且数据 == 2
     */
    private void decideMiddleEntity(XNode entities, XNode findEntity) {
        XNode relations = findEntity.element("relations");
        if (relations == null || !relations.hasChild()){
            return;
        }
        List<XNode> toOneRelations = relations.findAllByTag("to-one");
        if (toOneRelations.isEmpty() || toOneRelations.size() < 2) {
            return;
        }

        AtomicInteger toOneCount = new AtomicInteger();
        toOneRelations.forEach(toOne -> {
            String refEntityName = toOne.attrText("refEntityName");
            String entityLeftProp = (String)toOne.selectOne(XPathHelper.parseXSelector("/to-one/join/on@leftProp"));
            String middleEntityName = findEntity.attrText("name");

            // 从所有的 entity 中查找 refEntityName
            Object refEntitySelect = entities.selectOne(XPathHelper.parseXSelector("/entities/entity[@name=='" + refEntityName + "']"));
            if (refEntitySelect == null) {
                return;
           }

            // 查找 refEntityName 对应的 entity 中的 to-many
            Object refEntityNode = ((XNode) refEntitySelect).selectOne(
                XPathHelper.parseXSelector("/entity/relations/to-many[@refEntityName=='" + middleEntityName + "']"));
            if (refEntityNode == null) return;

            // 查找 to-many 的 rightProp，判断是否和 to-one 的 leftProp 一致
            Object o = ((XNode) refEntityNode).selectOne(
                XPathHelper.parseXSelector("/to-many/join/on[@rightProp=='" + entityLeftProp + "']"));
            if (o != null) {
                toOneCount.getAndIncrement();
            }
        });

        if (toOneCount.get() == 2) {
            findEntity.setAttr("tagSet", "many-to-many,no-web");
        }
    }
}