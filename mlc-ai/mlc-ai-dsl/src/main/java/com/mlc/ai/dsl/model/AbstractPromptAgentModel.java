package com.mlc.ai.dsl.model;

import com.mlc.ai.dsl.tasks.context.ContextKeys;
import com.mlc.ai.dsl.tasks.context.ContextKeys.FailureInfo;
import com.mlc.ai.dsl.tasks.prefix.FinalizeExecutor;
import com.mlc.ai.dsl.tasks.prefix.InputParseExecutor;
import com.mlc.ai.dsl.tasks.prefix.PlanBuildExecutor;
import com.mlc.ai.dsl.tasks.prefix.PromptTaskExecutor;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.manager.WorkflowManager;
import com.mlc.ai.task.model.NormalTaskModel;
import com.mlc.ai.task.scheduler.strategy.JumpToFinalTaskStrategy;
import io.nop.ai.core.model.PromptModel;
import io.nop.core.resource.component.ResourceComponentManager;
import jakarta.inject.Inject;
import java.util.HashMap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import reactor.core.publisher.Flux;

/**
 * 提示代理业务模型的抽象基类
 * 处理流程控制、依赖关系解析和会话状态管理
 */
@Slf4j
public abstract class AbstractPromptAgentModel implements IPromptModelProvider {

    @Getter
    protected ChatClient llmChatClient;

    @Inject
    protected OpenAiChatModel chatModel;

    protected static final Map<String, String> tagNameToPathMap = new HashMap<>();

    // 会话状态管理
    // Map<SessionID，完整 Dsl 字符串>
    protected final Map<String, String> sessionCurrentDsl = new ConcurrentHashMap<>();

    /**
     * 初始化 LLM 客户端
     */
    public void init() {
        // 初始化 LLM 客户端
        if (this.llmChatClient == null) {
            this.llmChatClient = ChatClient.builder(chatModel)
                                   .defaultAdvisors(
                                       MessageChatMemoryAdvisor.builder(MessageWindowChatMemory.builder().build()).build(), // 内存对话
                                       new SimpleLoggerAdvisor()) // 内置日志记录器
                                   .defaultOptions(OpenAiChatOptions.builder().temperature(0.7).build()).build();
        }
    }

    @Override
    public PromptModel getPromptModel(String key) {
        if (key == null || key.isEmpty()) {
            log.warn("请求的键为空或无效。");
            return null;
        }
        if (tagNameToPathMap.containsKey(key)) {
            return (PromptModel) ResourceComponentManager.instance().loadComponentModel(tagNameToPathMap.get(key));
        }
        return null;
    }

    /**
     * 解析键，根据依赖关系和当前状态构建执行计划，
     * 通过 LLM 按顺序流式执行提示，并更新会话状态。
     * </p>
     * 以严格的上下文依赖性和原子性处理用户请求。
     * 执行计划好的提示序列。如果任何步骤失败，整个操作将回滚到请求之前的状态。
     *
     * @param sessionId 用户会话的唯一标识符。
     * @param userInput 用户的输入文本，可能包含多个 @key 引用。
     * @return 一个 Flux<String>，流式返回执行过程的更新、结果或错误消息。
     */
    public final Flux<String> processRequest(String sessionId, String userInput) {
        final String initialDsl = this.sessionCurrentDsl.getOrDefault(sessionId, "");

        log.info("[{}] 开始处理请求。初始 Keys: {}", sessionId, initialDsl);

        // 初始化业务相关的上下文状态
        AtomicReference<String> currentDslRef = new AtomicReference<>(initialDsl);
        AtomicReference<FailureInfo> failureInfoRef = new AtomicReference<>();


        // 创建执行上下文
        ExecutionContext executionContext = ExecutionContext.getInstance();

        // 将业务对象和状态存入 attributes
        executionContext.setAttribute(ContextKeys.USER_INPUT, userInput);
        executionContext.setAttribute(ContextKeys.INITIAL_DSL, initialDsl);
        executionContext.setAttribute(ContextKeys.CURRENT_STEP_DSL_REF, currentDslRef);
        executionContext.setAttribute(ContextKeys.FAILURE_INFO_REF, failureInfoRef);
        executionContext.setAttribute(ContextKeys.ABSTRACT_PROMPT_AGENT_MODEL, this);

        // 创建执行器注册表
        executionContext.registerExecutor("InputParseExecutor", new InputParseExecutor());
        executionContext.registerExecutor("PlanBuildExecutor", new PlanBuildExecutor());
        executionContext.registerExecutor("PromptTaskExecutor", new PromptTaskExecutor());
        executionContext.registerExecutor("FinalizeExecutor", new FinalizeExecutor());

        WorkflowManager workflowManager = WorkflowManager.INSTANCE;
        // 创建标准工作流
        String dagEngineId = this.createStandardWorkflow(workflowManager, executionContext);

        // 执行工作流
        return workflowManager.executeWorkflow(dagEngineId, executionContext)
            .doOnComplete(() -> this.sessionCurrentDsl.put(sessionId, currentDslRef.get()));
    }

    /**
     * 创建标准处理工作流
     *
     * @return 标准工作流实例
     */
    private String createStandardWorkflow(WorkflowManager workflowManager, ExecutionContext executionContext) {

        // 创建工作流
        String dagEngineId = workflowManager.createWorkflow("orm 提示词工作流执行器", "orm 提示词工作流执行器");

        String inputParseTaskId = workflowManager
            .addTask(dagEngineId, NormalTaskModel.builder().name("输入解析").executorKey("InputParseExecutor").build());

        String planBuildTaskId = workflowManager
            .addTask(dagEngineId, NormalTaskModel.builder().name("计划构建").executorKey("PlanBuildExecutor").build());

        String executionTaskId = workflowManager
            .addTask(dagEngineId, NormalTaskModel.builder().name("任务执行").executorKey("PromptTaskExecutor").build());

        String finalizeTaskId = workflowManager
            .addTask(dagEngineId, NormalTaskModel.builder().name("最终处理").executorKey("FinalizeExecutor").build());

        executionContext.addStatusStrategy(new JumpToFinalTaskStrategy(finalizeTaskId));

        // 设置依赖关系
        workflowManager.addDependency(dagEngineId, inputParseTaskId, planBuildTaskId);
        workflowManager.addDependency(dagEngineId, planBuildTaskId, executionTaskId);
        workflowManager.addDependency(dagEngineId, executionTaskId, finalizeTaskId);

        return dagEngineId;
    }


    /**
     * 流式处理DSL，支持实时反馈
     * 
     * @param dslResult 需要处理的DSL结果
     * @param promptModel 提示模型
     * @return 流式处理结果和最终的新Schema键集合
     */
    public abstract Flux<String> processingDSLStream(String dslResult, PromptModel promptModel);
} 