package com.mlc.ai.dsl.transform;

import io.nop.commons.collections.CaseInsensitiveMap;
import io.nop.commons.type.StdSqlType;
import java.util.Map;


public enum AiOrmSqlType {
    BOOLEAN("BOOLEAN", StdSqlType.BOOLEAN),
    TINYINT("TINYINT", StdSqlType.TINYINT),
    SMALLINT("SMALLINT", StdSqlType.SMALLINT),
    INTEGER("INTEGER", StdSqlType.INTEGER),

    INT("INT", StdSqlType.INTEGER),

    BIGINT("BIGINT", StdSqlType.BIGINT),
    DECIMAL("DECIMAL", StdSqlType.DECIMAL),
    FLOAT("FLOAT", StdSqlType.FLOAT),
    REAL("REAL", StdSqlType.FLOAT),
    DOUBLE("DOUBLE", StdSqlType.DOUBLE),

    NUMERIC("NUMERIC", StdSqlType.DECIMAL),

    DATE("DATE", StdSqlType.DATE),

    TIME("TIME", StdSqlType.TIME),

    DATETIME("DATETIME", StdSqlType.DATETIME),

    TIMESTAMP("TIMESTAMP", StdSqlType.TIMESTAMP),
    CHAR("CHAR", StdSqlType.VARCHAR),

    VARCHAR("VARCHAR", StdSqlType.VARCHAR),

    TEXT("TEXT", StdSqlType.VARCHAR),

    BLOB("BLOB", StdSqlType.BLOB),

    CLOB("CLOB", StdSqlType.CLOB),

    VARBINARY("VARBINARY", StdSqlType.VARBINARY)
    ;

    private final String text;
    private final StdSqlType stdSqlType;

    AiOrmSqlType(String text, StdSqlType stdSqlType) {
        this.text = text;
        this.stdSqlType = stdSqlType;
    }

    public String toString() {
        return text;
    }

    public String getText() {
        return text;
    }

    public StdSqlType getStdSqlType() {
        return stdSqlType;
    }

    static final Map<String, AiOrmSqlType> textMap = new CaseInsensitiveMap<>();

    static {
        for (AiOrmSqlType sqlType : values()) {
            textMap.put(sqlType.getText(), sqlType);
        }
    }

    public static StdSqlType getStdSqlType(String text) {
        AiOrmSqlType sqlType = textMap.get(text);
        if (sqlType == null)
            throw new RuntimeException("Unknown SQL type: " + text);
        return sqlType.getStdSqlType();
    }
}
