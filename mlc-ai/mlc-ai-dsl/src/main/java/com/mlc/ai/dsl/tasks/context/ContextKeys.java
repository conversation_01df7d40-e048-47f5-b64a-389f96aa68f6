package com.mlc.ai.dsl.tasks.context;

import com.mlc.ai.dsl.model.AbstractPromptAgentModel;
import com.mlc.ai.dsl.tasks.AbstractTaskExecutor.KeyMessagePair;
import com.mlc.base.common.utils.ContextKey;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 定义 ExecutionContext 中使用的类型安全键常量。
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ContextKeys {

    // 用户输入的相关键
    public static final ContextKey<String> USER_INPUT = ContextKey.create("userInput");

    // USER_INPUT解析得到的键值对
    public static final ContextKey<List<KeyMessagePair>> KEY_MESSAGE_PAIRS = ContextKey.create("keyMessagePairs");

    // 任务执行器的初始化dsl
    public static final ContextKey<String> INITIAL_DSL = ContextKey.create("initialDsl");

    // 任务执行器的当前执行状态
    public static final ContextKey<AtomicReference<String>> CURRENT_STEP_DSL_REF = ContextKey.create("currentStepDslRef");

    // 失败信息（包含步骤key和错误原因）
    public static final ContextKey<AtomicReference<FailureInfo>> FAILURE_INFO_REF = ContextKey.create("failureInfoRef");

    // 当前执行器的上下文
    public static final ContextKey<AbstractPromptAgentModel> ABSTRACT_PROMPT_AGENT_MODEL = ContextKey.create("abstractPromptAgentModel");


    @Getter
    @AllArgsConstructor
    public static class FailureInfo {
        /**
         * 失败的步骤键
         */
        private String failingStepKey;

        /**
         * 失败原因
         */
        private Throwable failureReason;
    }
}