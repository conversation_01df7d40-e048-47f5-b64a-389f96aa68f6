package com.mlc.ai.dsl.tasks.prefix;

import com.mlc.ai.dsl.model.AbstractPromptAgentModel;
import com.mlc.ai.dsl.tasks.AbstractTaskExecutor;
import com.mlc.ai.dsl.tasks.context.ContextKeys;
import com.mlc.ai.dsl.tasks.context.ContextKeys.FailureInfo;
import com.mlc.ai.dsl.utils.ExecutionPlanBuilder;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 计划构建执行器
 */
@Slf4j
public class PlanBuildExecutor extends AbstractTaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {

        try {
            log.info("[{}] 开始构建执行计划", context.getExecutionId());

            List<KeyMessagePair> keyMessagePairs = context.getAttribute(ContextKeys.KEY_MESSAGE_PAIRS);
             if (keyMessagePairs == null) {
                 return Flux.error(new IllegalStateException("❌ 上下文中未包含 keyMessagePairs"));
            }

            // 从解析的键消息对中提取键
            List<String> requestedKeys = keyMessagePairs.stream()
                    .map(KeyMessagePair::getKey)
                    .collect(Collectors.toList());

            if (requestedKeys.isEmpty()) {
                return Flux.error(new IllegalArgumentException("❌ 没有解析到请求的键"));
            }

            log.debug("[{}] 请求的键: {}", context.getExecutionId(), requestedKeys);

            final AbstractPromptAgentModel abstractPromptAgentModel = context.getAttribute(ContextKeys.ABSTRACT_PROMPT_AGENT_MODEL);

            Queue<String> executionPlan =
                ExecutionPlanBuilder.buildExecutionPlan(requestedKeys, abstractPromptAgentModel);

            if (executionPlan.isEmpty()) {
                log.info("[{}] 执行计划为空，可能所有请求的键已在当前模式中", context.getExecutionId());
            } else {
                log.info("[{}] 构建的执行计划: {}", context.getExecutionId(), executionPlan);
            }

            task.getTaskContext().getOutput().put("executionQueue", executionPlan);

            return Flux.just(String.format("📋 执行计划已生成，包含 %d 个任务\n", executionPlan.size()));
        } catch (Exception e) {

            // 设置错误状态
            log.error("[{}] 计划构建器执行时出错", context.getExecutionId(), e);
            // 获取失败相关的引用来设置错误状态
            AtomicReference<FailureInfo> failureInfoRef = context.getAttribute(ContextKeys.FAILURE_INFO_REF);
            if (failureInfoRef != null) {
                failureInfoRef.set(new FailureInfo("plan_build", e));
            }
            return Flux.error(new RuntimeException("❌ 计划构建器执行失败: " + e.getMessage() + "\n", e));
        }
    }
} 