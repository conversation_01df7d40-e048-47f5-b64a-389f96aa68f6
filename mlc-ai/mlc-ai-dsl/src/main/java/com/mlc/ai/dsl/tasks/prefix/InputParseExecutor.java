package com.mlc.ai.dsl.tasks.prefix;

import com.google.common.base.Strings;
import com.mlc.ai.dsl.tasks.AbstractTaskExecutor;
import com.mlc.ai.dsl.tasks.context.ContextKeys;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 输入解析执行器
 */
@Slf4j
public class InputParseExecutor extends AbstractTaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
        try {
            String userInput = context.getAttribute(ContextKeys.USER_INPUT);
            if (Strings.isNullOrEmpty(userInput)) {
                log.error("[{}] 在上下文属性中未找到用户输入信息。", context.getExecutionId());
                return Flux.error(new IllegalStateException("❌ 在上下文属性中未找到用户输入信息"));
            }

            log.info("[{}] 解析用户输入: {}", context.getExecutionId(), userInput);

            List<KeyMessagePair> keyMessagePairs = parseKeys(userInput);
            if (keyMessagePairs.isEmpty()) {
                log.error("[{}] 解析出的键值对为空，用户输入: {}", context.getExecutionId(), userInput);
                return Flux.error(new IllegalStateException("❌ 请正确输入需要执行的模型定义，使用 '@' 符号引用，例如：@entity 需求1 @column 需求2\n"));
            }

            context.setAttribute(ContextKeys.KEY_MESSAGE_PAIRS, keyMessagePairs);
            log.info("[{}] 解析出的 Keys: {}", context.getExecutionId(), keyMessagePairs);

            return Flux.empty(); // 成功但不输出消息
        } catch (Exception e) {
            return Flux.error(new RuntimeException("❌ 解析用户输入时出错 - " + e.getMessage(), e));
        }
    }

    /**
    * 解析用户输入，提取键值对
    * 格式：@key1 message1 @key2 message2 ...
    * @param input 用户输入字符串
    * @return 键值对列表
    */
    private List<KeyMessagePair> parseKeys(String input) {
        List<KeyMessagePair> keyMessagePairs = new ArrayList<>();

        // 更精确的正则表达式，支持多行文本
        Pattern pattern = Pattern.compile("@([a-zA-Z0-9_-]+)\\s+([^@]+?)(?=\\s*@[a-zA-Z0-9_-]+\\s+|$)");
        Matcher matcher = pattern.matcher(input);

        int lastEnd = 0;
        while (matcher.find()) {
            // 处理 Key 前面的消息（如果存在且不全是空格）
            if (matcher.start() > lastEnd && !input.substring(lastEnd, matcher.start()).trim().isEmpty()) {
                log.warn("键 @{} 之前的输入被忽略：{}", matcher.group(1), input.substring(lastEnd, matcher.start()).trim());
            }

            String key = matcher.group(1).trim();
            String userMessage = matcher.group(2).trim();

            if (Strings.isNullOrEmpty(key) || Strings.isNullOrEmpty(userMessage)) {
                log.warn("无效的键值对：@{} {}", key, userMessage);
                return null;
            }

            keyMessagePairs.add(new KeyMessagePair(key, userMessage));
            lastEnd = matcher.end();
        }

        // 检查最后一段文本是否未被处理（并且不全是空格）
        if (lastEnd < input.length() && !input.substring(lastEnd).trim().isEmpty()) {
            log.warn("忽略无键关联的输入: {}", input.substring(lastEnd).trim());
        }

        return keyMessagePairs;
    }
} 