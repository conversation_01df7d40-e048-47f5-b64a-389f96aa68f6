package com.mlc.ai.dsl.tasks.prefix;

import com.mlc.ai.dsl.tasks.AbstractTaskExecutor;
import com.mlc.ai.dsl.tasks.context.ContextKeys;
import com.mlc.ai.dsl.tasks.context.ContextKeys.FailureInfo;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.concurrent.atomic.AtomicReference;

/**
 * 最终状态处理执行器
 */
@Slf4j
public class FinalizeExecutor extends AbstractTaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
        // 创建执行结果流
        Flux<String> resultFlux;

        AtomicReference<FailureInfo> failureInfoRef = context.getAttribute(ContextKeys.FAILURE_INFO_REF);
        FailureInfo failureInfo = failureInfoRef.get();
        
        if (failureInfo != null) {
            String failingKey = failureInfo.getFailingStepKey();
            String failureReason = failureInfo.getFailureReason().getMessage();

            log.warn("[{}] 执行在步骤 @{} 失败，原因: {}. 状态已回滚至请求前。", context.getExecutionId(), failingKey, failureReason);

            String initialDsl = context.getAttribute(ContextKeys.INITIAL_DSL);

            log.trace("[{}] 回滚状态， DSL:\n{}", context.getExecutionId(), initialDsl);

            resultFlux = Flux.just("\n--- 执行 中止 ---\n",
                             String.format("处理在步骤 '@%s' 失败，所有本次请求的操作均已撤销。原因: %s\n", 
                             failingKey, failureReason));
        } else {
            AtomicReference<String> finalDslRef = context.getAttribute(ContextKeys.CURRENT_STEP_DSL_REF);

            if (finalDslRef != null) {
                log.trace("[{}] 准备提交的 DSL:\n{}", context.getExecutionId(), finalDslRef.get());

                resultFlux = Flux.just("\n ✅ 执行完毕，所有步骤均已成功处理。\n");
            } else {
                log.error("[{}] 无法获取最终状态或模型实例，无法提交状态变更！", context.getExecutionId());
                resultFlux = Flux.just("\n 执行完毕，所有步骤已处理，但无法提交最终状态。\n");
            }
        }

        return resultFlux;
    }
} 