package com.mlc.ai.dsl.model.orm;

import com.google.common.base.Strings;
import com.mlc.ai.core.util.code.CodeBlockExtractor;
import com.mlc.ai.core.util.code.CodeBlockExtractor.CodeBlock;
import com.mlc.ai.core.util.code.CodeBlockExtractor.ParseResult;
import com.mlc.ai.dsl.model.AbstractPromptAgentModel;
import com.mlc.ai.dsl.transform.IOrientedNormalizer;
import com.mlc.ai.dsl.transform.OrmOrientedProgramNormalizer;
import com.mlc.ai.dsl.utils.XDefLoaderUtil;
import com.mlc.ai.dsl.utils.XDefLoaderUtil.MapRelationCollector;
import io.nop.ai.core.model.PromptModel;
import io.nop.api.core.annotations.ioc.DelayMethod;
import io.nop.core.lang.xml.XNode;
import io.nop.core.lang.xml.parse.XNodeParser;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * ORM领域的提示代理业务模型
 * 负责处理ORM相关的提示和DSL解析
 */
@Slf4j
public class OrmPromptAgentModel extends AbstractPromptAgentModel{

    private static final String XDEF_PATH = "/mlc/schema/orm/ai/orm.xdef";

    static {
        // 注册ORM相关的提示定义
        tagNameToPathMap.put("orm", "/mlc/ai/prompts/orm/coordinate/orm-core.prompt.yaml");
        tagNameToPathMap.put("domains", "/mlc/ai/prompts/orm/orm-domains.prompt.yaml");
        tagNameToPathMap.put("entities", "/mlc/ai/prompts/orm/orm-entities.prompt.yaml");
        tagNameToPathMap.put("columns", "/mlc/ai/prompts/orm/orm-columns.prompt.yaml");
    }

    // xdef文件的关系收集器
    private final MapRelationCollector parentCollector = XDefLoaderUtil.createParentCollector();
    private final MapRelationCollector childrenCollector = XDefLoaderUtil.createChildrenCollector();

    @DelayMethod
    @Override
    public void init() {
        super.init();

        boolean loaded =
            XDefLoaderUtil.loadFromXdef(XDEF_PATH, List.of(parentCollector, childrenCollector));
        if (!loaded) {
            log.error("ORM定义初始化失败");
        }
    }

    @Override
    public List<String> getParents(String key) {
        return parentCollector.getRelations(key);
    }

    @Override
    public List<String> getChildren(String key) {
        return childrenCollector.getRelations(key);
    }

    @Override
    public Flux<String> processingDSLStream(String dslResult, PromptModel promptModel) {
        if (Strings.isNullOrEmpty(dslResult)) {
            return Flux.empty();
        }

        ParseResult parseResult = CodeBlockExtractor.parseContent(dslResult);
        List<CodeBlock> codeBlocks = parseResult.getCodeBlocks();
        
        if (codeBlocks.isEmpty()) {
            return Flux.empty();
        }

        CodeBlock codeBlock = codeBlocks.get(0);
        return Flux.create(sink -> {
            try {
                // 发送处理开始状态
                sink.next("\n ⌛正在解析DSL... \n");
                
                // 解析XML
                XNode parseNode = XNodeParser.instance().parseFromText(null, codeBlock.getCode());
                sink.next("\n ⌛DSL解析完成，开始规范化数据... \n");

                IOrientedNormalizer orientedNormalizer = new OrmOrientedProgramNormalizer();

                XNode transformNode = orientedNormalizer.transform(parseNode);

                // 验证 dsl 语法
                sink.next("\n ⌛正在验证DSL语法... \n");

                if (!orientedNormalizer.validate(transformNode)) {
                    log.error("DSL解析失败，无法生成ORM模型");
                    sink.error(new IllegalStateException("DSL解析失败，无法生成ORM模型"));
                    return;
                }

                sink.next("\n ✅ DSL语法验证完成，ORM模型生成成功！\n");

                // 使用处理后的XML重建内容
//                Map<Integer, String> processedCodeMap = new HashMap<>();
//                processedCodeMap.put(0, parseNode.xml());
//                String result = parseResult.rebuildWithProcessedCode(processedCodeMap);

                sink.next(dslResult); // new OrmOrientedUserNormalizer().transform(parseNode);
                sink.complete();
            } catch (Exception e) {
                log.error("处理DSL时出错", e);
                sink.error(e);
            }
        });
    }
}