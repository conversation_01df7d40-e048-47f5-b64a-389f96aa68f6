package com.mlc.ai.dsl.model;

import io.nop.ai.core.model.PromptModel;
import java.util.List;

/**
 * 定义提供者接口
 * 负责为特定领域（如ORM、元数据等）提供定义
 */
public interface IPromptModelProvider {

    /**
     * 获取特定键的提示定义
     */
    PromptModel getPromptModel(String key);
    
    /**
     * 获取特定键的父级键列表
     */
    List<String> getParents(String key);
    
    /**
     * 获取特定键的子级键列表
     */
    List<String> getChildren(String key);

} 