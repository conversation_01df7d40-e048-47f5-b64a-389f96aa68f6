package com.mlc.ai.dsl.utils;

import com.mlc.ai.dsl.model.AbstractPromptAgentModel;
import com.mlc.ai.dsl.PromptExceptions.CyclicDependencyException;
import com.mlc.ai.dsl.PromptExceptions.DefinitionNotFoundException;
import com.mlc.ai.dsl.PromptExceptions.MissingParentException;
import io.nop.ai.core.model.PromptModel;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;

/**
 * 执行计划构建工具类
 * 负责根据依赖关系构建任务执行顺序，确保正确的依赖处理
 */
@Slf4j
public class ExecutionPlanBuilder {

    /**
     * 使用拓扑排序（Kahn 算法）构建执行计划
     * 1. 发现与请求相关的所有节点（请求的节点 + 所需的祖先节点）
     * 2. 识别实际需要处理的节点（尚未在模式中或未明确请求）
     * 3. 执行依赖关系验证（确保父节点存在或已计划）
     * 4. 为需要处理的节点构建依赖关系图
     * 5. 执行拓扑排序以获取执行顺序
     * 6. 检测循环
     *
     * @param requestedKeys 用户请求的键列表
     * @param provider 定义提供者实现
     * @return 有序的执行队列
     */
    public static Queue<String> buildExecutionPlan(List<String> requestedKeys, AbstractPromptAgentModel provider) {
        
        // 1. 发现阶段：查找所有相关键（请求的键 + 所有祖先键）
        Set<String> relevantKeys = new HashSet<>();
        Queue<String> discoveryQueue = new LinkedList<>(requestedKeys);
        Set<String> visitedForDiscovery = new HashSet<>(requestedKeys);

        while(!discoveryQueue.isEmpty()) {
            String key = discoveryQueue.poll();

            PromptModel promptModel = provider.getPromptModel(key);
            if (promptModel == null) {
                throw new DefinitionNotFoundException(key + " (在依赖发现过程中)");
            }
            relevantKeys.add(key); // 将键本身添加到相关集合中

            List<String> parents = provider.getParents(key);
            for (String parentKey : parents) {
                // 如果存在结构循环，避免在发现时出现无限循环（稍后进行实际检查）
                if (visitedForDiscovery.add(parentKey)) {
                    // 检查父定义是否存在
                    if (provider.getPromptModel(parentKey) == null) {
                        throw new DefinitionNotFoundException(parentKey + " (作为 @" + key + " 的父级)");
                    }
                    discoveryQueue.add(parentKey);
                }
            }
        }
        log.debug("发现的相关 Keys (请求+父级): {}", relevantKeys);

        // 2. 识别要处理的节点：过滤相关键
        Set<String> nodesToProcess = new HashSet<>();
        for (String key : relevantKeys) {
            // 处理条件：1.它不在当前架构中（需要创建 !initialSchemaKeys.contains(key) || ） 2.是用户明确请求的（可能需要更新/重新生成）
            if (requestedKeys.contains(key)) {
                nodesToProcess.add(key);
                PromptModel promptModel = provider.getPromptModel(key);
                if (promptModel == null){
                    throw new DefinitionNotFoundException(key);
                }
            }
        }
        log.debug("需要处理的 Keys (不在当前 Schema 或被明确请求): {}", nodesToProcess);

        if (nodesToProcess.isEmpty()) {
            return new LinkedList<>();
        }

        // 3. 依赖性验证（排序前检查）
        for (String node : nodesToProcess) {
            List<String> parents = provider.getParents(node);
            for (String parentKey : parents) {
                // 如果它尚未在模式中并且不会在本次运行中被处理，则需要一个父级
                // !initialSchemaKeys.contains(parentKey) &&
                if (!nodesToProcess.contains(parentKey)) {
                    // 此节点所需的父节点不可用，且不计划创建/更新
                    throw new MissingParentException(node, parentKey);
                }
            }
        }
        log.debug("通过父级依赖验证 (父级要么已存在，要么在处理计划中)");

        // 4. 构建依赖图（仅适用于 nodesToProcess）并计算入度
        Map<String, Integer> inDegree = new HashMap<>();
        // 邻接表：父级 -> 子级
        Map<String, List<String>> adj = new HashMap<>();

        // 初始化图结构
        for (String node : nodesToProcess) {
            inDegree.put(node, 0);
            adj.put(node, new ArrayList<>());
        }

        // 根据 nodesToProcess 内的父关系填充图形边和入度
        for (String node : nodesToProcess) {
            List<String> parents = provider.getParents(node);
            for (String parent : parents) {
                // 仅考虑正在处理的节点之间的边
                if (nodesToProcess.contains(parent)) {
                    // 添加边父级->节点
                    adj.get(parent).add(node);
                    // 增加子级的入度
                    inDegree.put(node, inDegree.get(node) + 1);
                }
            }
        }
        log.trace("构建的依赖图 (邻接表): {}，入度表: {}", adj, inDegree);

        // 5. 拓扑排序（Kahn算法）
        Queue<String> topoQueue = new LinkedList<>();
        // 将所有入度为 0 的节点添加到队列中
        for (String node : nodesToProcess) {
            if (inDegree.get(node) == 0) {
                topoQueue.add(node);
            }
        }

        Queue<String> finalExecutionQueue = new LinkedList<>();
        int processedCount = 0;
        
        while (!topoQueue.isEmpty()) {
            String u = topoQueue.poll();
            finalExecutionQueue.add(u);
            processedCount++;

            for (String v : adj.getOrDefault(u, Collections.emptyList())) {
                inDegree.put(v, inDegree.get(v) - 1);
                if (inDegree.get(v) == 0) {
                    topoQueue.add(v);
                }
            }
        }

        // 6. 循环检测
        if (processedCount != nodesToProcess.size()) {
            Set<String> cycleNodes = nodesToProcess.stream()
                                                  .filter(s -> inDegree.getOrDefault(s, -1) > 0)
                                                  .collect(Collectors.toSet());
            log.error("拓扑排序失败：检测到循环依赖！涉及的节点可能包括: {}", cycleNodes);
            throw new CyclicDependencyException("检测到循环依赖，涉及节点: " + cycleNodes);
        }

        log.debug("拓扑排序成功，最终执行队列: {}", finalExecutionQueue);
        return finalExecutionQueue;
    }
} 