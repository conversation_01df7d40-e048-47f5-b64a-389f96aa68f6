package com.mlc.ai.dsl.utils;

import io.nop.xlang.xdef.IXDefNode;
import io.nop.xlang.xdef.IXDefinition;
import io.nop.xlang.xmeta.SchemaLoader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.function.BiConsumer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * XDef文件加载工具类
 * 负责加载XDef文件并构建关系图
 */
@Slf4j
public class XDefLoaderUtil {

    /**
     * 从XDef文件加载定义并构建关系
     *
     * @param xdefPath XDef文件路径
     * @param relationCollectors 关系收集器列表，每个收集器负责一种关系的收集
     * @return 是否加载成功
     */
    public static boolean loadFromXdef(String xdefPath, List<RelationCollector> relationCollectors) {
        log.info("尝试从XDef加载模型定义: {}", xdefPath);
        try {
            IXDefinition ixDefinition = SchemaLoader.loadXDefinition(xdefPath);
            if (ixDefinition == null) {
                log.error("无法加载 {} 定义。", xdefPath);
                return false;
            }

            // 根节点为 TagName, 即XDef的顶层节点
            String rootKey = ixDefinition.getTagName();

            // 首先将根节点的所有子节点加入队列
            Map<String, ? extends IXDefNode> rootChildren = ixDefinition.getChildren();
            if (rootChildren == null || rootChildren.isEmpty()) {
                log.warn("XDef 文件 {} 没有顶层定义节点。", xdefPath);
                return false;
            }

            // 使用 BFS 遍历 XDef 结构
            Queue<KeyNodePair> queue = new LinkedList<>();
            rootChildren.forEach((key, node) -> {
                queue.add(new KeyNodePair(key, node));
                // 为每个收集器添加关系
                for (RelationCollector collector : relationCollectors) {
                    collector.addRelation(rootKey, key);
                }
            });

            // BFS 遍历
            while (!queue.isEmpty()) {
                KeyNodePair current = queue.poll();

                Map<String, ? extends IXDefNode> children = current.node.getChildren();
                if (children != null && !children.isEmpty()) {
                    children.forEach((childKey, childNode) -> {
                        queue.add(new KeyNodePair(childKey, childNode));
                        // 为每个收集器添加关系
                        for (RelationCollector collector : relationCollectors) {
                            collector.addRelation(current.key, childKey);
                        }
                    });
                }
            }

            return true;
        } catch (Exception e) {
            log.error("从 {} 加载模型定义时发生错误：", xdefPath, e);
            return false;
        }
    }

    /**
     * 关系收集器接口
     */
    public interface RelationCollector {
        /**
         * 添加一个关系
         *
         * @param parentKey 父级键
         * @param childKey 子级键
         */
        void addRelation(String parentKey, String childKey);
    }

    /**
     * 基于Map的关系收集器
     */
    public static class MapRelationCollector implements RelationCollector {
      @Getter
      private final Map<String, List<String>> container;
      private final BiConsumer<String, String> relationProcessor;

        /**
         * 创建一个基于Map的关系收集器
         * 
         * @param container 存储关系的容器
         * @param relationProcessor 关系处理函数，接收parent和child参数
         */
        public MapRelationCollector(Map<String, List<String>> container, 
                                  BiConsumer<String, String> relationProcessor) {
            this.container = container;
            this.relationProcessor = relationProcessor;
        }

        @Override
        public void addRelation(String parentKey, String childKey) {
            // 避免自引用
            if (parentKey.equals(childKey)) {
                log.warn("检测到并忽略了自引用关系: '@{}' -> '@{}'", parentKey, childKey);
                return;
            }
            relationProcessor.accept(parentKey, childKey);
            log.trace("建立关系: {} -> {}", parentKey, childKey);
        }

        /**
         * 获取特定键的相关项列表
         */
        public List<String> getRelations(String key) {
            return container.getOrDefault(key, Collections.emptyList());
        }
    }

    /**
     * 创建父->子关系收集器
     */
    public static MapRelationCollector createChildrenCollector() {
        Map<String, List<String>> childrenMap = new HashMap<>();
        return new MapRelationCollector(childrenMap, 
            (parent, child) -> childrenMap.computeIfAbsent(parent, k -> new ArrayList<>()).add(child));
    }

    /**
     * 创建子->父关系收集器
     */
    public static MapRelationCollector createParentCollector() {
        Map<String, List<String>> parentMap = new HashMap<>();
        return new MapRelationCollector(parentMap, 
            (parent, child) -> parentMap.computeIfAbsent(child, k -> new ArrayList<>()).add(parent));
    }

    /**
     *  键-节点对
     */
    @AllArgsConstructor
    private static class KeyNodePair {
        String key;
        IXDefNode node;
    }
} 