元模型定义需要用 ai 生成需求，大概分为: entity、columns、aliases、computes、relations、unique-keys、indexes, 由于现在大模型幻觉和上下文的限制，
分层实施策略

1.输入阶段
    首轮生成仅提供 entities + columns + relations，确保AI聚焦核心结构
    后续迭代逐步添加unique-keys/indexes约束条件

2.输出验证
    要求AI对computes生成公式时附带示例数据验证
    对indexes需结合假想查询场景解释必要性

3.防幻觉设计
    对非核心字段（如aliases）采用严格格式："alias": "xxx"
    对computes要求标记数据来源（如 depends_on: ["price", "quantity"]）

示例精简结构:
entities:
- name: User
  columns:
    - name: id
      type: integer
      unique: true  # 合并unique-key
    - name: email
      type: string
      alias?: 邮箱  # 可选别名
      relations:
    - target: Order
      type: 1:N


第一阶段：核心骨架（必须保留）
1.Entity（实体） 
   必要性：100%，模型的基础单元
   处理：强制要求AI明确每个实体的名称和用途

2.Columns（字段）
    必要性：100%，定义实体的属性
    优化：合并aliases到字段中，作为可选属性（如 column: {name: "price", alias: "售价"}）

3.Relations（关系）
    必要性：80%，描述实体间关联
    优化：初期仅保留简单关系（如 1:N），复杂关系后期补充


第二阶段：数据完整性（逐步添加）
4.Unique Keys（唯一约束）
    必要性：60%，依赖业务需求
    优化：合并到entity或columns中（如 column: {unique: true}）

5.Indexes（索引）
    必要性：50%，性能优化项
    建议：由AI根据查询模式推导生成，而非直接定义

第三阶段：增强特性（可后期扩展）
6.Computes（计算字段）
    必要性：30%，易引发幻觉
    优化：拆分逻辑与存储，初期仅声明计算目标（如 compute: "订单总价 = price * quantity"）

7.Aliases（别名）
    必要性：20%，国际化或语义化需求 
    优化：作为字段的附加属性而非独立模块

8.Filters（过滤器）
    必要性：20%，可选项
    优化：初期不强制要求，后期根据查询需求添加