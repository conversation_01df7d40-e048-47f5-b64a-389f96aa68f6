<?xml version="1.0" encoding="UTF-8"?>
<orm x:schema="/nop/schema/xdef.xdef" xmlns:x="/nop/schema/xdsl.xdef"
  xmlns:xdef="/nop/schema/xdef.xdef" xmlns:orm="orm">

    <entities xdef:body-type="list" xdef:key-attr="name">
        <entity name="!class-name" tableName="!string">
            <columns xdef:body-type="list" xdef:key-attr="name">
                <column name="!english" displayName="chinese" primary="boolean" mandatory="boolean"
                  stdDomain="std-domain" stdSqlType="!sql-type" precision="int" scale="int"/>
            </columns>

            <relations xdef:body-type="list" xdef:key-attr="name">
                <to-one name="!english" displayName="chinese" refEntityName="!class-name">
                    <join xdef:body-type="list">
                        <on leftProp="prop-name" leftValue="any" rightProp="prop-name" rightValue="any" xdef:allow-multiple="true"/>
                    </join>
                </to-one>

                <to-many name="!english" displayName="chinese" refEntityName="!class-name">
                    <join xdef:body-type="list">
                        <on leftProp="prop-name" leftValue="any" rightProp="prop-name" rightValue="any" xdef:allow-multiple="true"/>
                    </join>
                </to-many>
            </relations>

            <computes xdef:body-type="list" xdef:key-attr="name">
                <compute name="!prop-name" displayName="string" type="generic-type" notGenCode="!boolean=false" tagSet="tag-set" ui:control="string">
                    <args xdef:body-type="list" xdef:key-attr="name">
                        <arg name="!prop-name" displayName="string" type="!generic-type"/>
                    </args>
                    <getter xdef:value="xpl"/>
                    <setter xdef:value="xpl"/>
                </compute>
            </computes>
        </entity>
    </entities>
</orm>