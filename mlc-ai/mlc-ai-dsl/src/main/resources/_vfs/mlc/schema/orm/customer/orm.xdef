<?xml version="1.0" encoding="UTF-8"?>
<orm x:schema="/nop/schema/xdef.xdef" xmlns:x="/nop/schema/xdsl.xdef"
  xmlns:xdef="/nop/schema/xdef.xdef" xmlns:orm="orm">

    <entities xdef:body-type="list" xdef:key-attr="name">
        <entity name="!english" displayName="chinese" tableName="!string">
            <columns xdef:body-type="list" xdef:key-attr="name">
                <column name="!english" displayName="chinese" primary="boolean" mandatory="boolean"
                  stdDomain="std-domain" stdSqlType="!sql-type" precision="int" scale="int"/>
            </columns>

            <relations xdef:body-type="list" xdef:key-attr="name">
                <to-one name="!english" displayName="chinese" refEntityName="!entity-name">
                    <join xdef:body-type="list">
                        <on leftProp="column-name" leftValue="any" rightProp="column-name" rightValue="any" xdef:allow-multiple="true"/>
                    </join>
                </to-one>

                <to-many name="!english" displayName="chinese" refEntityName="!entity-name">
                    <join xdef:body-type="list">
                        <on leftProp="column-name" leftValue="any" rightProp="column-name" rightValue="any" xdef:allow-multiple="true"/>
                    </join>
                </to-many>
            </relations>
        </entity>
    </entities>
</orm>