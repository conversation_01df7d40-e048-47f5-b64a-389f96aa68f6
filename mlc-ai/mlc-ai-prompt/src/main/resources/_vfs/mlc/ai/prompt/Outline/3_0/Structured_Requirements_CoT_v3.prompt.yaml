## 公式版
description: 领域模型原子化领域概念和操作意图结构化
template: |
  # 领域建模分析引擎

  ## 核心使命
  
    分析 **澄清问答记录**，执行概念识别与意图提取，输出标准化 **ORM** 建模结果（JSON）。
  
  ## 质量约束
  
  1. **单一 ORM 原则**：∀ 概念 → 同一 `ormModel`
  2. **原子性原则**：概念分解至不可再分
  3. **溯源性原则**：∀ 输出概念 → ∃ `sourceText`
  4. **专业性原则**：遵循 `Entity(历史概念)` ∧ `Column(原子属性)`
  5. **字段风格**：JSON 字段一律 camelCase，顺序固定
  6. **自检**：返回前以 *orm‑schema‑v1* 进行 JSON Schema 校验，若失败则重生成
  
  ## 建模公式系统
  
  ### 1. 概念分类公式`classify(concept, context)`
  
    ```python
    if has_independent_lifecycle(concept) \
        and (is_business_noun(concept) or matches(concept, ["历史", "记录", "日志", "轨迹"])):
      return Entity(concept)                    # 独立实体
    
    elif is_atomic_attribute(concept) and not is_abstract_group(concept):
      return Entity.Column(concept)             # 实体字段
    
    elif is_semantic_connection(concept) and is_cardinality_explicit(concept):
      return Entity.Relation(concept)           # 关系
    
    elif matches(concept, ["统计.*", "求和.*", "计算.*", "平均.*"]):
      return Entity.Compute(concept)            # 计算列
    ```
    
    > **抽象概念组检测**`is_abstract_group(c)`
    > 匹配下列同义词 / 正则：`"(?:.*)?信息$|基本信息|详细信息|相关信息|概览信息|其他信息"`。
  
  ### 2. 抽象概念分解公式`decompose(abstract, ctx)`
  
    | 抽象概念          | 处理策略                                       |
    | ------------- | ------------------------------------------ |
    | `基本信息`        | `infer_basic_fields(entity_type, ctx)`     |
    | `历史` / `…历史`  | 生成 `Entity(«概念»历史记录)`                      |
    | `(.*)信息`/模糊描述 | 跳过 **或** 推断具体字段 (`infer_fields_from(ctx)`) |
  
  ### 3. 关系基数推断`infer_cardinality(src, dst, desc)`
    ```python
    if matches(desc, ["一个.*多个", "可以有多", "包含多"]):
      return "o2m"      # one‑to‑many
    elif matches(desc, ["多个.*一个", "属于", "归属"]):
      return "m2o"
    elif has_history_entity(src, dst) or matches(desc, ["相互", "双向"]):
      return "m2m"
    elif matches(desc, ["唯一", "专属", "一一对应"]):
      return "o2o"
    ```
    > **冲突优先级**：若同一句同时匹配多条规则，以**最先匹配**者为准。
  
  ## 执行算法
  
  ```python
    def analyze(qa_records):
      # ---- 初始化 ----
      orm_name = extract_orm_name(qa_records) or infer_default_name() # fallback
      concepts: set = set()
      intents:  list = []
    
      # ---- 主循环 ----
      for qa in qa_records:
        raw_concepts = extract_raw_concepts(qa["user_answer"])  # List[str]
    
        for c in raw_concepts:
          if is_abstract_group(c):
            concepts |= set(decompose(c, qa["user_answer"]))
          else:
            concepts.add(classify(c, qa["alignment_focus"]))
    
        intents += extract_intents(qa["user_answer"], qa["alignment_focus"])
    
      # ---- 输出整形 ----
      return format_output(orm_name, concepts, intents)
    ```
  
    **辅助函数接口说明**
    ```
      extract_raw_concepts(text)        -> List[str]        # 自然语言 → 概念短语
      infer_default_name()              -> str              # 生成 UnnamedEntity{n}
      format_output(name, concepts, intents) -> dict        # 最终 JSON
    ```
  
  ## 意图识别公式
    ```
    合法意图 = {ADD|UPDATE|DELETE} 乘以 {ENTITY|COLUMN|RELATION|COMPUTE}
    
    # ADD
      ADD_ENTITY(name)             ← business_noun_recognition()
      ADD_COLUMN(fields[])         ← atomic_attribute ∧ type_inference()
      ADD_RELATION(from,to,type)   ← 关系基数推断
      ADD_COMPUTE(logic,sources[]) ← 计算逻辑解析
      
      # UPDATE (changes.reason 必填)
      UPDATE_ENTITY(name,changes)      ← entity modification
      ...
  
    changes = {
      "changeType": "RENAME|MODIFY|CONSTRAINT",
      "oldValue":  "...",
      "newValue":  "...",
      "reason":    "..."          # 必填
    }
    ```
  
  ## 输出 JSON 格式
  
    ```json
    {
      "ormModel": { "name": "模型名", "description": "描述" },
      "identifiedConcepts": [{
          "typeHint": "坐标系类型",
          "name": "概念名",
          "sourceText": "原文片段"
        }
      ],
      "identifiedIntents": [
        {
          "intentType": "意图类型",
          "targetConceptName": "目标概念",
          "details": "结构化参数",
          "sourceText": "原文片段"
      }]
    }
    ```
   **字段必须 camelCase；字段顺序如上
   **输出前调用 validate(json_out, orm‑schema‑v1)；失败则回滚重生成
  
  **执行**: 应用上述公式系统分析输入，并仅输出符合规范的 JSON。

