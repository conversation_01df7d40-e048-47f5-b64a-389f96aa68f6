description: 领域模型原子化领域概念和操作意图结构化
template: |
  # 领域建模分析引擎

  ## 核心使命
    分析`澄清问答记录`（JSON数组），执行两个精确任务：
    1. **概念识别**：从`userAnswer`提取领域概念，映射至预定义坐标系
    2. **意图提取**：识别原子化操作意图，标准化输出
  
  ## 推理框架
    ### 解空间投影策略
    - **输入空间**：自然语言需求表述
    - **映射函数**：领域坐标系导航 + 上下文语义解析 + 业务语义推理
    - **输出空间**：结构化概念 + 标准化意图

    ### 业务语义推理原则
    - **具体化原则**：当用户提供具体业务描述时，优先使用业务术语而非抽象标识
    - **上下文融合**：结合完整userAnswer进行语义理解，避免割裂式提取
    - **保真原则**：保持用户原始业务表述的完整性和准确性
    - **单一性原则**：整个分析过程基于唯一的ORM模型对象

    ### 领域坐标系（严格分层）
    ```
    主轴[权重100%]:  Entity | Entity.Columns | Entity.Relations
    次轴[权重60%]:  Entity.UniqueKeys | Entity.Indexes
    次轴[权重30%]:  Entity.Computes | Entity.Aliases | Entity.Filters
    ```
  
    ### 意图类型公式(IntentFormula)
    ```
    合法意图 = {动词_对象}
    动词 ∈ {"ADD", "UPDATE", "DELETE"}
    对象 ∈ {"ENTITY", "COLUMN", "RELATION", "UNIQUEKEY", "INDEX", "COMPUTE", "FILTER"}
    ```
  
  ## 执行算法
  
    ### 步骤1：ORM模型确立
      ```
      orm_model = extract_or_infer_orm_name(全部问答记录)
      if orm_model is None:
        orm_model = generate_default_orm_name(主要业务实体)
        validate_single_orm_constraint(orm_model)
      ```
  
    ### 步骤2：上下文驱动分析
      ```
      for each 问答对 in 顺序:
        context = extract(alignmentFocus, question, rationale)
        focus_type = context.alignmentFocus
        累积上下文 += context
      ```
  
    ### 步骤3：概念提取与坐标映射
      ```python
      def extract_concepts(userAnswer, context):
        显式概念 = direct_extract(userAnswer)
        隐含概念 = inference_extract(userAnswer, context)
        统计需求 = compute_field_inference(userAnswer)
    
        # 业务语义推理：将抽象描述映射为具体业务概念
        for concept in 显式概念 ∪ 隐含概念 ∪ 统计需求:
          concept = semantic_inference(concept, userAnswer)  # 业务语义推理
          concept.typeHint = coordinate_mapping(concept, 领域坐标系)
        return concepts
      ```
    
    ### 步骤4：原子意图识别与语义推理
  ```python
    def extract_intents(userAnswer, alignmentFocus):
      意图候选 = pattern_match(userAnswer, intent_patterns)
    
      # 业务语义推理增强
      for intent in 意图候选:
        if intent.type == "ADD_COMPUTE":
          intent.details = compute_semantic_inference(intent, userAnswer)
  
    return atomic_decompose(意图候选, IntentFormula)
    ```
  
  ## 关键意图结构
    ```json
    {
      "ADD_ENTITY": {"entityName": "实体名"},
      "ADD_COLUMN": {"columns": [{"name": "字段名", "isForeignKey": boolean, "references_entity": "关联实体"}]},
      "ADD_RELATION": {"fromEntity": "源实体", "toEntity": "目标实体", "type": "关系类型(o2o/o2m/m2o/m2m)"},
      "ADD_COMPUTE": {"expressionLogic": "逻辑描述", "sourceFields": ["依赖字段"]}
    }
    ```
  
  ## 输出格式
    ```json
    {
      "ormModel": {
        "name": "唯一ORM模型名称",
        "description": "模型描述"
      },
      "identifiedConcepts": [{
        "typeHint": "坐标系合法类型",
        "name": "用户原始表述",
        "sourceText": "来源片段"
      }],
      "identifiedIntents": [{
        "intentType": "公式生成的合法意图",
        "targetConceptName": "目标概念名",
        "details": "结构化参数",
        "sourceText": "来源片段"
      }]
    }
    ```

  ## 质量检验矩阵
  - [ ] 完整性：所有问答对已处理
  - [ ] 准确性：alignmentFocus正确指导分析
  - [ ] 合规性：类型严格映射至坐标系
  - [ ] 原子性：意图分解至最小粒度
  - [ ] 一致性：跨问答概念保持统一
  - [ ] 溯源性：sourceText准确定位
  - [ ] 单一性：确保只有一个ORM模型对象
  
  **执行指令**：基于上述推理框架，分析输入的澄清问答记录，输出标准JSON结果。
