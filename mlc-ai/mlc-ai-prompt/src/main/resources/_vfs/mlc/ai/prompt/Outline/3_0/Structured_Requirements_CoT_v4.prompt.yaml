## 公式版
description: 领域模型原子化领域概念和操作意图结构化
template: |
  ## 领域建模分析引擎

  ### 1. 核心使命

  * **输入**：澄清问答记录（QA）。
  * **输出**：标准化 ORM 建模结果（实体、字段、关系、计算字段等）。

  ### 2. 领域坐标系（严格分层）

  | 轴向               | 说明                                                  |
  | ---------------- | --------------------------------------------------- |
  | **主轴（100% 优先级）** | `Entity`、`Entity.Columns`、`Entity.Relations`        |
  | **次轴（60%）**      | `Entity.UniqueKeys`、`Entity.Indexes`                |
  | **次轴（30%）**      | `Entity.Computes`、`Entity.Aliases`、`Entity.Filters` |

  ### 3. 概念分类规则

  1. **Entity（实体）**
  
  * 具有独立生命周期的业务名词，或
  * 名称含“历史 / 记录 / 日志 / 轨迹”等关键词，表示历史记录型实体。
  2. **Entity.Columns（字段）**
  
  * 原子级属性。
  * 若为“基本信息 / 详细信息 / 相关信息”等抽象集合，先按 §4 拆分。
  3. **Entity.Relations（关系）**
  
  * 描述实体间具有确定基数的语义连接。
  4. **Entity.Computes（计算字段）**
  
  * 名称出现“统计、求和、计算、平均”等字样。
  
  ### 4. 抽象概念拆分
  
  * **“基本信息”** → 推断出常见基础字段。
  * **带“历史”** → 拆出“××记录”实体。
  * **其他模糊描述** → 难以细分时可略过，或结合上下文推断具体字段。

  ### 5. 关系基数推断

  | 典型描述                 | 推断基数     |
  | -------------------- | -------- |
  | “一个…多个 / 可以有多 / 包含多” | 一对多（o2m） |
  | “多个…一个 / 属于 / 归属”    | 多对一（m2o） |
  | “相互 / 双向” 或出现历史实体    | 多对多（m2m） |
  | “唯一 / 专属 / 一一对应”     | 一对一（o2o） |

  ### 6. 意图识别与参数推断

  > **合法意图格式**：`QUERY | ADD | UPDATE | DELETE` + `_` + `ENTITY | COLUMN | RELATION | COMPUTE | UNIQUEKEY | INDEXE | FILTER`
  
  | 意图                   | 触发条件（示例）    | 关键参数 / 推断方式                            |
  | -------------------- | ----------- | -------------------------------------- |
  | **ADD\_ENTITY**      | 新出现的业务名词    | `name`：业务名词                            |
  | **ADD\_COLUMN**      | 新增字段描述      | `fields[]`：原子属性；自动推断类型                 |
  | **ADD\_RELATION**    | 描述实体间关联     | `from`、`to`：源/目标实体<br>`type`：§5 基数推断结果 |
  | **ADD\_COMPUTE**     | 描述计算指标      | `logic`：计算逻辑<br>`sources[]`：依赖字段/实体    |
  | **UPDATE\_ENTITY**   | 实体重命名 / 修改  | `changes`：见下方 *changes 对象*             |
  | **UPDATE\_COLUMN**   | 字段重命名 / 约束  | 同上                                     |
  | **UPDATE\_RELATION** | 关系方向 / 基数改动 | 同上                                     |
  | **UPDATE\_COMPUTE**  | 计算逻辑修改      | 同上                                     |
  | **DELETE\_* 系列*\*    | 删除描述        | `target`：待删除对象标识                       |
  
  **changes 对象格式**
  
  ```json
  {
    "changeType": "RENAME | MODIFY | CONSTRAINT",
    "oldValue": "原值",
    "newValue": "新值",
    "reason": "变更原因"
  }
  ```
  
  ### 7. 执行流程
  
  1. **确定模型名称**：显式获取；若缺失则生成默认名。
  2. **遍历每条问答**
  
  * 抽取原始概念 → 分类或先拆分抽象集合。
  * 提取与当前问答片段对应的意图。
  3. **输出 JSON**：整理概念与意图，见 §8。
  
  ### 8. 输出 JSON 结构（统一规范）
  
  ```json
  {
    "ormModel": {
      "name": "模型名",
      "description": "模型描述"
    },
    "identifiedConcepts": [
      {
        "typeHint": "坐标系类型",
        "name": "概念名",
        "sourceText": "原文片段"
      }
    ],
    "identifiedIntents": [
      {
        "intentType": "意图类型",
        "targetConceptName": "目标概念",
        "details": "ADD/DELETE 参数对象或 {\"changes\": {...}}",
        "sourceText": "原文片段"
      }
    ]
  }
  ```
  
  * **ADD\_* 意图*\*：`details` 直接给出创建参数（如字段数组、逻辑等）。
  * **UPDATE\_* 意图*\*：`details` 统一使用 `{"changes": {...}}`。
  * **DELETE\_* 意图*\*：`details` 使用 `{"target": "标识"}`。

  ### 9. 质量约束

  1. **单一模型**：所有概念归属同一 ORM。
  2. **原子性**：字段拆分到最小粒度。
  3. **溯源性**：每个输出项保留来源片段。
  4. **专业性**：严格区分“历史实体”与“原子字段”。
