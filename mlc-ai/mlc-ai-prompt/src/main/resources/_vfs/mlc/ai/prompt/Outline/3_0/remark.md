为什么输出不用树形 Json: 
    1.树形结构需额外转换坐标系权重 
    2.树形结构需跨层级验证原子性（如orm->entity->add->column需回溯到根节点校验）
    3.现有格式已通过完整验证（质量检验矩阵全达标），能清晰表达「概念识别+意图提取」的双轨输出，树形嵌套结构解析大幅度增加复杂度（需递归遍历多层节点）

为什么不推荐拆分原子意图：add、update、delete 拆分成多个提示词：意图关联识别、业务规则一致性、原子意图的本质耦合性、错误传播风险、领域坐标系对齐

Structured_Requirements_CoT_v3.prompt.yaml 意图公式 可能需要意图识别模式，后续看情况添加：
    意图识别模式:
    ADD_* ⟸ pattern_match(text, [需要, 添加, 新增, 创建, 建立])
    UPDATE_* ⟸ pattern_match(text, [修改, 更新, 改为, 调整, 变更])  
    DELETE_* ⟸ pattern_match(text, [删除, 移除, 去掉, 不要, 取消])
    RENAME ⟸ pattern_match(描述, ["改名", "重命名", "叫做", "改为"])
    MODIFY ⟸ pattern_match(描述, ["修改", "调整", "变更", "改变"])  
    CONSTRAINT ⟸ pattern_match(描述, ["约束", "限制", "规则", "验证"])

    
