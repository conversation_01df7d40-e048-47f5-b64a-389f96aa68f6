package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 冲突解决节点
 * 用于在顺序流向中处理冲突解决
 * 
 * 流向：ConflictDetectionNode -> ConflictResolutionNode -> 下一个处理节点
 */
@Getter
@Setter
public class ConflictResolutionNode extends AbstractDagNode {

    // 冲突描述
    private String conflictDescription;

    // 解决方案
    private String resolutionStrategy;
    
    // 解决结果
    private String resolutionResult;

    // 冲突的节点ID列表
    // 由于冲突节点是单独管理的，所以可以在 node 中直接存储冲突的节点ID列表
    private List<String> conflictedNodeIds;

    // 是否已解决
    private boolean isResolved;

    public ConflictResolutionNode() {
        super(NodeType.CONFLICT_RESOLUTION);
    }

    public ConflictResolutionNode(String conflictDescription, List<String> conflictedNodeIds) {
        super(NodeType.CONFLICT_RESOLUTION);
        this.conflictDescription = conflictDescription;
        this.conflictedNodeIds = conflictedNodeIds;
    }

    /**
     * 设置解决结果
     * @param strategy 解决策略
     * @param result 解决结果
     */
    public void setResolutionResult(String strategy, String result) {
        this.resolutionStrategy = strategy;
        this.resolutionResult = result;
        this.isResolved = true;
    }
} 