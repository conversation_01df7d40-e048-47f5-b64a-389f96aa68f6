package com.mlc.ai.prompt.store.dag.context;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.task.context.ExecutionContext;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * DAG数据上下文接口
 * 提供节点执行时的数据访问和传递能力
 */
public interface IDagDataContext {
    
    /**
     * 获取执行上下文
     */
    ExecutionContext getExecutionContext();
    
    /**
     * 获取DAG图实例
     */
    DagGraph getDagGraph();
    
    /**
     * 获取当前节点
     */
    IDagNode getCurrentNode();
    
    /**
     * 根据数据键获取输入数据
     * @param dataKey 数据键
     * @return 数据值
     */
    <T> Optional<T> getInputData(String dataKey);
    
    /**
     * 设置输出数据
     * @param dataKey 数据键
     * @param value 数据值
     */
    <T> void setOutputData(String dataKey, T value);
    
    /**
     * 获取所有输入数据
     */
    Map<String, Object> getAllInputData();
    
    /**
     * 获取所有输出数据
     */
    Map<String, Object> getAllOutputData();
    
    /**
     * 获取节点的提供者节点列表
     */
    List<IDagNode> getProviderNodes();
    
    /**
     * 获取节点的消费者节点列表
     */
    List<IDagNode> getConsumerNodes();
} 