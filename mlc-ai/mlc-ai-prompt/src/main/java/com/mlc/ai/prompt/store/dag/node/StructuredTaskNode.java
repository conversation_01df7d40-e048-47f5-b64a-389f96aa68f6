package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 结构化子任务节点，相当于导出概念。
 * 已经边界相对明确，包含了意图、目标对象和大部分（或全部）参数的子任务。这些是最终"Solve"阶段的输入。
 *
 *  直到所有相关的用户需求都被转化为 DAG 中一系列明确的、参数化的、无冲突（或已解决冲突）、已澄清的 StructuredSubTaskNode，或者所有路径都已探索完毕，生成操作计划。
 *  一旦 DAG 达到一个"可解决"的状态（例如，所有主要路径都导向了 StructuredSubTaskNode，并且没有未解决的关键歧义或冲突）。
 *
 * 意图节点和参数节点通过PROVIDES边指向子任务节点。
 */
@Getter
@Setter
public class StructuredTaskNode extends AbstractDagNode {

    // 属性字段
    private String taskDescription;
    private String executionTasks; // 执行任务列表

    public StructuredTaskNode(String taskDescription) {
        super(NodeType.STRUCTURED_TASK);
        this.taskDescription = taskDescription;
    }
}

