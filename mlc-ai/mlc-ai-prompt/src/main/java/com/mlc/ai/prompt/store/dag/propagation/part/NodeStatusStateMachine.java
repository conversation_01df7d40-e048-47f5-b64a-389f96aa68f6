package com.mlc.ai.prompt.store.dag.propagation.part;

import com.mlc.ai.prompt.store.dag.node.ParameterNode;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * DAG节点状态机
 * 负责管理节点状态转换规则和逻辑
 */
@Slf4j
public class NodeStatusStateMachine {
    
    // 定义各节点类型的有效状态转换规则
    private static final Map<NodeType, Map<NodeStatus, Set<NodeStatus>>> VALID_TRANSITIONS =
        Map.of(
            NodeType.PARAMETER, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED, NodeStatus.AWAITING_DEPENDENCIES, NodeStatus.REQUIRES_CLARIFY),
                NodeStatus.AWAITING_DEPENDENCIES, Set.of(NodeStatus.RESOLVED, NodeStatus.ASSUMPTION_MADE, NodeStatus.REQUIRES_CLARIFY),
                NodeStatus.ASSUMPTION_MADE, Set.of(NodeStatus.RESOLVED, NodeStatus.ASSUMPTION_INVALIDATED),
                NodeStatus.REQUIRES_CLARIFY, Set.of(NodeStatus.RESOLVED, NodeStatus.AWAITING_DEPENDENCIES)
            ),
            NodeType.STRUCTURED_TASK, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.AWAITING_DEPENDENCIES, NodeStatus.READY_FOR_EXECUTION),
                NodeStatus.AWAITING_DEPENDENCIES, Set.of(NodeStatus.READY_FOR_EXECUTION, NodeStatus.AWAITING_VALIDATION, NodeStatus.CONFLICTED),
                NodeStatus.READY_FOR_EXECUTION, Set.of(NodeStatus.EXECUTION_IN_PROGRESS, NodeStatus.CONFLICTED),
                NodeStatus.EXECUTION_IN_PROGRESS, Set.of(NodeStatus.COMPLETED, NodeStatus.FAILED),
                NodeStatus.AWAITING_VALIDATION, Set.of(NodeStatus.READY_FOR_EXECUTION, NodeStatus.CONFLICTED)
            ),
            NodeType.DOMAIN_COORDINATE, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED, NodeStatus.REQUIRES_CLARIFY, NodeStatus.CONFLICTED),
                NodeStatus.REQUIRES_CLARIFY, Set.of(NodeStatus.RESOLVED, NodeStatus.CONFLICTED)
            ),
            NodeType.IDENTIFIED_ATOMIC_INTENT, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED, NodeStatus.CONFLICTED),
                NodeStatus.RESOLVED, Set.of(NodeStatus.CONFLICTED)
            ),
            NodeType.ASSUMPTION, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.ASSUMPTION_MADE, NodeStatus.ASSUMPTION_INVALIDATED)
            ),
            NodeType.ASSUMPTION_VALIDATION, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED, NodeStatus.FAILED)
            ),
            NodeType.CLARIFICATION_QUESTION, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED)
            ),
            NodeType.CLARIFICATION_ANSWER, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED)
            ),
            NodeType.CONFLICT_RESOLUTION, Map.of(
                NodeStatus.PENDING_ANALYSIS, Set.of(NodeStatus.RESOLVED, NodeStatus.FAILED)
            )
        );
    
    /**
     * 确定节点的下一个状态
     */
    public static NodeStatus determineNextStatus(IDagNode node, List<IDagNode> providers) {
        NodeType nodeType = node.getType();
        NodeStatus currentStatus = node.getStatus();
        
        log.debug("确定节点 {} ({}) 的下一状态，当前: {}, 提供者数量: {}", 
                 node.getId(), nodeType, currentStatus, providers.size());
        
        // 检查是否有假设依赖
        boolean hasAssumptionDependency = providers.stream()
            .anyMatch(p -> p.getStatus() == NodeStatus.ASSUMPTION_MADE);
        
        // 检查所有提供者是否就绪
        boolean allProvidersReady = providers.stream()
            .allMatch(p -> NodeStatus.isNodeReady(p) || p.getStatus() == NodeStatus.ASSUMPTION_MADE);
        
        if (!allProvidersReady) {
            log.debug("节点 {} 的提供者尚未全部就绪，保持当前状态: {}", node.getId(), currentStatus);
            return currentStatus; // 保持当前状态
        }
        
        // 根据节点类型和依赖情况确定下一状态
        NodeStatus nextStatus = determineStatusByType(node, providers, hasAssumptionDependency);
        
        // 验证状态转换的合法性
        if (isValidTransition(nodeType, currentStatus, nextStatus)) {
            log.debug("节点 {} 状态转换: {} -> {}", node.getId(), currentStatus, nextStatus);
            return nextStatus;
        } else {
            log.warn("节点 {} 非法状态转换: {} -> {}，保持当前状态", node.getId(), currentStatus, nextStatus);
            return currentStatus;
        }
    }
    
    /**
     * 根据节点类型确定状态
     */
    private static NodeStatus determineStatusByType(IDagNode node, List<IDagNode> providers, boolean hasAssumptionDependency) {
        NodeType nodeType = node.getType();
        
        // 如果有假设依赖且是任务节点，需要等待验证
        if (hasAssumptionDependency && nodeType == NodeType.STRUCTURED_TASK) {
            return NodeStatus.AWAITING_VALIDATION;
        }
        
        return switch (nodeType) {
            case PARAMETER -> NodeStatus.RESOLVED;
            case STRUCTURED_TASK -> NodeStatus.READY_FOR_EXECUTION;
            case DOMAIN_COORDINATE, IDENTIFIED_ATOMIC_INTENT -> NodeStatus.RESOLVED;
            case ASSUMPTION -> NodeStatus.ASSUMPTION_MADE;
            case ASSUMPTION_VALIDATION, CLARIFICATION_QUESTION, CLARIFICATION_ANSWER, CONFLICT_RESOLUTION -> NodeStatus.RESOLVED;
            default -> NodeStatus.PENDING_ANALYSIS;
        };
    }
    
    /**
     * 确定独立节点（无依赖）的初始状态
     */
    public static NodeStatus determineInitialStatusForIndependentNode(IDagNode node) {
        return switch (node.getType()) {
            case PARAMETER -> {
                ParameterNode paramNode = (ParameterNode) node;
                yield paramNode.getValue() != null ? NodeStatus.RESOLVED : NodeStatus.PENDING_ANALYSIS;
            }
            case USER_RAW_REQUEST, OUTSIDE_BORDER, IDENTIFIED_ATOMIC_INTENT -> NodeStatus.RESOLVED;
            case STRUCTURED_TASK -> NodeStatus.READY_FOR_EXECUTION; // 没有依赖的任务直接可执行
            default -> NodeStatus.PENDING_ANALYSIS;
            // DOMAIN_COORDINATE, ASSUMPTION , ASSUMPTION_VALIDATION, CLARIFICATION_QUESTION, CLARIFICATION_ANSWER, CONFLICT_RESOLUTION 等类型的节点初始状态为 PENDING_ANALYSIS
        };
    }
    
    /**
     * 验证状态转换是否合法
     */
    public static boolean isValidTransition(NodeType nodeType, NodeStatus from, NodeStatus to) {
        if (from == to) {
            return true; // 保持相同状态总是合法的
        }
        
        Set<NodeStatus> allowedTransitions = VALID_TRANSITIONS
            .getOrDefault(nodeType, Collections.emptyMap())
            .getOrDefault(from, Collections.emptySet());
        
        boolean isValid = allowedTransitions.contains(to);
        
        if (!isValid) {
            log.debug("节点类型 {} 的状态转换 {} -> {} 不在预定义规则中", nodeType, from, to);
        }
        
        return isValid;
    }
    
    /**
     * 获取节点类型的所有可能状态
     */
    public static Set<NodeStatus> getPossibleStatuses(NodeType nodeType) {
        Map<NodeStatus, Set<NodeStatus>> transitions = VALID_TRANSITIONS.get(nodeType);
        if (transitions == null) {
            return Set.of(NodeStatus.PENDING_ANALYSIS, NodeStatus.RESOLVED, NodeStatus.FAILED);
        }
        
        Set<NodeStatus> allStatuses = new HashSet<>(transitions.keySet());
        transitions.values().forEach(allStatuses::addAll);
        return allStatuses;
    }
    
    /**
     * 检查节点是否可以从当前状态转换到目标状态
     */
    public static boolean canTransitionTo(IDagNode node, NodeStatus targetStatus) {
        return isValidTransition(node.getType(), node.getStatus(), targetStatus);
    }
} 