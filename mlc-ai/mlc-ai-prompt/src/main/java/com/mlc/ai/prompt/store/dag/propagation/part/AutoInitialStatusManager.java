package com.mlc.ai.prompt.store.dag.propagation.part;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自动初始状态管理器
 * 负责新添加节点的智能初始状态设置和依赖分析
 */
@Slf4j
public class AutoInitialStatusManager {
    
    private final DagGraph dagGraph;
    private final DependencyQueryService dependencyService;

    // 缓存节点初始化策略
    private final Map<NodeType, InitializationStrategy> strategyCache = new ConcurrentHashMap<>();
    
    public AutoInitialStatusManager(DagGraph dagGraph, 
                                  DependencyQueryService dependencyService) {
        this.dagGraph = dagGraph;
        this.dependencyService = dependencyService;
        initializeStrategies();
    }
    
    /**
     * 初始化各种节点类型的策略
     */
    private void initializeStrategies() {
        // 参数节点：有未就绪依赖时等待
        strategyCache.put(NodeType.PARAMETER, 
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        
        // 任务节点：有未就绪依赖时等待
        strategyCache.put(NodeType.STRUCTURED_TASK,
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        
        // 领域坐标节点：可能需要分析
        strategyCache.put(NodeType.DOMAIN_COORDINATE, 
            (node, providers) -> NodeStatus.PENDING_ANALYSIS);
        
        // 意图节点：等待依赖
        strategyCache.put(NodeType.IDENTIFIED_ATOMIC_INTENT, 
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        
        // 假设节点：需要分析
        strategyCache.put(NodeType.ASSUMPTION, 
            (node, providers) -> NodeStatus.PENDING_ANALYSIS);
        
        // 验证节点：等待依赖
        strategyCache.put(NodeType.ASSUMPTION_VALIDATION, 
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        
        // 澄清相关节点：等待依赖
        strategyCache.put(NodeType.CLARIFICATION_QUESTION, 
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        strategyCache.put(NodeType.CLARIFICATION_ANSWER, 
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        
        // 冲突解决节点：等待依赖
        strategyCache.put(NodeType.CONFLICT_RESOLUTION, 
            (node, providers) -> NodeStatus.AWAITING_DEPENDENCIES);
        
        // 用户请求节点：通常是起始节点，应该是已解析状态
        strategyCache.put(NodeType.USER_RAW_REQUEST, 
            (node, providers) -> NodeStatus.RESOLVED);
        
        // 边界节点：通常是已解析状态
        strategyCache.put(NodeType.OUTSIDE_BORDER, 
            (node, providers) -> NodeStatus.RESOLVED);
    }
    
    /**
     * 处理新添加的节点
     */
    public void handleNodeAdded(IDagNode node) {
        log.debug("处理新添加的节点: {} (类型: {})", node.getId(), node.getType());
        
        // 分析节点的依赖关系
        List<IDagNode> providers = dependencyService.findProvidersForConsumer(node);
        
        // 根据依赖情况确定初始状态
        NodeStatus initialStatus = determineInitialStatus(node, providers);
        
        // 设置初始状态
        if (node.getStatus() != initialStatus) {
            dagGraph.updateNodeStatus(node.getId(), initialStatus);
            log.info("自动设置节点 {} 初始状态为: {}", node.getId(), initialStatus);
        }
        
        // 如果节点已就绪，立即检查并传播到消费者
        if (NodeStatus.isNodeReady(node)) {
            propagateToConsumers(node);
        }
        
        // 如果节点有依赖但依赖已就绪，立即检查状态更新
        if (!providers.isEmpty() && dependencyService.areAllProvidersReady(providers)) {
            checkAndUpdateNodeIfReady(node);
        }
    }
    
    /**
     * 确定节点的初始状态
     */
    private NodeStatus determineInitialStatus(IDagNode node, List<IDagNode> providers) {
        if (providers.isEmpty()) {
            // 没有依赖的节点使用独立节点策略
            return NodeStatusStateMachine.determineInitialStatusForIndependentNode(node);
        } else {
            // 有依赖的节点使用依赖策略
            return determineInitialStatusWithDependencies(node, providers);
        }
    }
    
    /**
     * 确定有依赖节点的初始状态
     */
    private NodeStatus determineInitialStatusWithDependencies(IDagNode node, List<IDagNode> providers) {
        // 检查依赖是否已就绪
        boolean allProvidersReady = dependencyService.areAllProvidersReady(providers);
        
        if (allProvidersReady) {
            // 如果所有依赖已就绪，直接确定下一状态
            return NodeStatusStateMachine.determineNextStatus(node, providers);
        } else {
            // 如果依赖未就绪，使用类型特定的策略
            InitializationStrategy strategy = strategyCache.get(node.getType());
            if (strategy != null) {
                return strategy.determineStatusWithPendingDep(node, providers);
            } else {
                return NodeStatus.AWAITING_DEPENDENCIES;
            }
        }
    }
    
    /**
     * 传播状态到消费者节点
     */
    private void propagateToConsumers(IDagNode readyNode) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(readyNode);
        log.debug("立即传播：节点 {} 变为就绪，检查 {} 个消费者", readyNode.getId(), consumers.size());
        
        for (IDagNode consumer : consumers) {
            if (shouldCheckConsumer(consumer)) {
                checkAndUpdateNodeIfReady(consumer);
            }
        }
    }
    
    /**
     * 检查并更新节点状态
     */
    private void checkAndUpdateNodeIfReady(IDagNode node) {
        List<IDagNode> providers = dependencyService.findProvidersForConsumer(node);
        
        if (dependencyService.areAllProvidersReady(providers)) {
            NodeStatus nextStatus = NodeStatusStateMachine.determineNextStatus(node, providers);
            if (nextStatus != node.getStatus()) {
                NodeStatus oldStatus = node.getStatus();
                log.debug("立即更新节点 {} 状态从 {} 到 {}", node.getId(), oldStatus, nextStatus);
                dagGraph.updateNodeStatus(node.getId(), nextStatus);
                
                // 递归传播
                if (NodeStatus.isNodeReady(node)) {
                    propagateToConsumers(node);
                }
            }
        }
    }
    
    /**
     * 判断是否应该检查消费者节点
     */
    private boolean shouldCheckConsumer(IDagNode consumer) {
        return consumer.getStatus() == NodeStatus.AWAITING_DEPENDENCIES ||
               consumer.getStatus() == NodeStatus.PENDING_ANALYSIS ||
               consumer.getStatus() == NodeStatus.AWAITING_VALIDATION;
    }
    
    /**
     * 批量初始化多个节点
     */
    public void batchInitializeNodes(List<IDagNode> nodes) {
        log.info("批量初始化 {} 个节点", nodes.size());
        
        // 按依赖关系排序，先处理无依赖的节点
        nodes.sort((a, b) -> {
            int adeps = dependencyService.findProvidersForConsumer(a).size();
            int bdeps = dependencyService.findProvidersForConsumer(b).size();
            return Integer.compare(adeps, bdeps);
        });
        
        for (IDagNode node : nodes) {
            handleNodeAdded(node);
        }
        
        log.info("批量初始化完成");
    }
    
    /**
     * 初始化策略接口
     */
    @FunctionalInterface
    public interface InitializationStrategy {
        NodeStatus determineStatusWithPendingDep(IDagNode node, List<IDagNode> providers);
    }
}