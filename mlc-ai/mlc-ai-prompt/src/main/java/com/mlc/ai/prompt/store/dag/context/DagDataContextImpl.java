package com.mlc.ai.prompt.store.dag.context;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.task.context.ExecutionContext;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * DAG数据上下文实现类
 */
@Getter
public class DagDataContextImpl implements IDagDataContext {
    
    private final ExecutionContext executionContext;
    private final DagGraph dagGraph;
    private final IDagNode currentNode;
    private final Map<String, Object> inputData;
    private final Map<String, Object> outputData;
    
    public DagDataContextImpl(ExecutionContext executionContext, DagGraph dagGraph, IDagNode currentNode) {
        this.executionContext = executionContext;
        this.dagGraph = dagGraph;
        this.currentNode = currentNode;
        this.inputData = new HashMap<>();
        this.outputData = new HashMap<>();
        
        // 初始化输入数据（从提供者节点收集）
        initializeInputData();
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getInputData(String dataKey) {
        return Optional.ofNullable((T) inputData.get(dataKey));
    }
    
    @Override
    public <T> void setOutputData(String dataKey, T value) {
        outputData.put(dataKey, value);
    }
    
    @Override
    public Map<String, Object> getAllInputData() {
        return new HashMap<>(inputData);
    }
    
    @Override
    public Map<String, Object> getAllOutputData() {
        return new HashMap<>(outputData);
    }
    
    @Override
    public List<IDagNode> getProviderNodes() {
        return dagGraph.getIncomingEdges(currentNode).stream()
                .filter(edge -> edge.getType() == EdgeType.PROVIDES)
                .map(edge -> dagGraph.getGraph().getEdgeSource(edge))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IDagNode> getConsumerNodes() {
        return dagGraph.getOutgoingEdges(currentNode).stream()
                .filter(edge -> edge.getType() == EdgeType.PROVIDES)
                .map(edge -> dagGraph.getGraph().getEdgeTarget(edge))
                .collect(Collectors.toList());
    }
    
    /**
     * 初始化输入数据
     * 从提供者节点收集输出数据作为当前节点的输入
     */
    private void initializeInputData() {
        List<IDagNode> providers = this.getProviderNodes();
        for (IDagNode provider : providers) {
            // 从提供者节点的属性中获取输出数据
            inputData.putAll(provider.getOutputs());
        }
    }
}