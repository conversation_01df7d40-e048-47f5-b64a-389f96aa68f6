package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户原始请求节点
 * 代表用户的原始请求，通常是未处理的文本。
 *
 * 设计核心：围绕 UserRawRequestNode 和 DAG 的动态扩展进行任务处理：
 * 1.  **`UserRawRequestNode` 的角色与时机**:
 *     *   它标志着一个**围绕明确的核心用户意图或任务展开的对话的起点**。
 *     *   仅在用户发起一个**全新的、与当前任务无关的意图**时，才应创建新的 `UserRawRequestNode` 来启动新的 DAG 实例。
 *
 * 2.  **同一任务内的交互与 DAG 演进**:
 *     *   在**同一个核心任务**的持续交互中，用户的后续输入（如对澄清问题的回答、对中间结果的反馈/修正、对当前任务的进一步指令或补充信息）**不应创建新的 `UserRawRequestNode`**。
 *     *   这些后续输入应被视为**数据流**，用于**扩展 (extend)** 现有的 DAG 结构。
 *
 * 3.  **DAG "扩展"的具体含义**:
 *     *   用户的输入会触发 DAG 中**新节点的创建和执行**，或者作为参数传递给**因等待这些输入而暂停的已有节点**。
 *     *   这些新产生的或被激活的节点会连接到 DAG 中现有的相关节点，从而**延长和完善思考链与执行路径**。
 *
 * 4.  **用户输入的性质判断至关重要**:
 *     *   系统必须具备机制（规则或LLM判断）来区分用户输入：
 *         *   是**全新的意图** (触发新的 `UserRawRequestNode` 和 DAG)。
 *         *   还是对**当前正在进行的任务的响应或补充** (在现有 DAG 内处理)。
 *
 * 5.  **设计模式的目标与优势**:
 *     *   确保在单一任务的上下文得以保持，**CoT (Chain of Thought) 的连贯性**得以维持。
 *     *   使 DAG 成为一个**动态的、能反映思考和执行过程演进的“活地图”**，而不是一系列孤立的请求。
 *
 */
@Getter
@Setter
public class UserRawRequestNode extends AbstractDagNode {

    // 属性字段
    private String text;

    public UserRawRequestNode(String text) {
        super(NodeType.USER_RAW_REQUEST);
        this.text = text;
    }

}
