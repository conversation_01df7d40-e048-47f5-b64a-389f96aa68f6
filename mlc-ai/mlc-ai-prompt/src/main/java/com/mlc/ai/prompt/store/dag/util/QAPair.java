package com.mlc.ai.prompt.store.dag.util;

import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 问答对数据类
 * 用于存储澄清问题和对应的用户回答
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QAPair {
    
    /** 问题ID */
    private String questionId;
    
    /** 问题文本 */
    private String questionText;
    
    /** 对齐焦点 */
    private String alignmentFocus;
    
    /** 选择此问题的原因 */
    private String rationale;
    
    /** 推荐选项 */
    private List<String> potentialRecommendations;
    
    /** 用户回答 */
    private String userAnswer;
    
    /** 回答状态 */
    private AnswerStatus answerStatus;
    
    /** 回答时间戳 */
    private long answerTimestamp;
    
    /**
     * 简化构造函数
     */
    public QAPair(String questionId, String questionText, String userAnswer) {
        this.questionId = questionId;
        this.questionText = questionText;
        this.userAnswer = userAnswer;
        this.answerStatus = AnswerStatus.ANSWERED;
        this.answerTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 从澄清问题节点创建QAPair
     */
    public static QAPair fromClarificationQuestion(ClarificationQuestionNode questionNode) {
        QAPair qaPair = new QAPair();
        qaPair.setQuestionId(questionNode.getId());
        qaPair.setQuestionText(questionNode.getQuestion());
        qaPair.setAlignmentFocus(questionNode.getAlignmentFocus());
        qaPair.setRationale(questionNode.getRationale());
        qaPair.setPotentialRecommendations(questionNode.getPotentialRecommendations());
        qaPair.setAnswerStatus(AnswerStatus.PENDING);
        return qaPair;
    }
    
    /**
     * 设置用户回答
     */
    public void setAnswer(String answer) {
        this.userAnswer = answer;
        this.answerStatus = AnswerStatus.ANSWERED;
        this.answerTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 检查是否已回答
     */
    public boolean isAnswered() {
        return answerStatus == AnswerStatus.ANSWERED && userAnswer != null && !userAnswer.trim().isEmpty();
    }
    
    /**
     * 回答状态枚举
     */
    public enum AnswerStatus {
        PENDING,    // 等待回答
        ANSWERED,   // 已回答
        SKIPPED,    // 用户跳过
        TIMEOUT,    // 超时
        ERROR       // 错误
    }
    
    @Override
    public String toString() {
        return String.format("Q[%s]: %s | A: %s (%s)", questionId, questionText, userAnswer, answerStatus);
    }
} 