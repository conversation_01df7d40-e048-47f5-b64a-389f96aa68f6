package com.mlc.ai.prompt.store.dag.node.base;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
public abstract class AbstractDagNode implements IDagNode {
    protected final String id;

    protected final NodeType type;

    @Setter
    protected NodeStatus status;

    protected final Map<String, Object> attachProps;

    protected final Map<String, Object> outputs;

    // 创建时间戳，用于排序
    protected final long createTimestamp;

    protected AbstractDagNode(NodeType type) {
        this.id = UUID.randomUUID().toString();
        this.type = type;
        this.status = NodeStatus.PENDING_ANALYSIS; // 默认状态
        this.attachProps = new HashMap<>();
        this.outputs = new HashMap<>();
        this.createTimestamp = System.currentTimeMillis();
    }


    @Override
    public void setAttachProp(String key, Object value) {
        this.attachProps.put(key, value);
    }

    @Override
    public Object getAttachProp(String key) {
        return this.attachProps.get(key);
    }

    // 如果节点直接用作顶点，则 hashCode 和 equals 对于 JGraphT 很重要
    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        AbstractDagNode that = (AbstractDagNode) obj;
        return id.equals(that.id);
    }
}

