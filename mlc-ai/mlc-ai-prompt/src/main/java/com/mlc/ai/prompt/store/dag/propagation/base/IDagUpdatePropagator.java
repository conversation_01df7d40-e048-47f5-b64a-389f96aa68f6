package com.mlc.ai.prompt.store.dag.propagation.base;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.event.NodeStatusChangeEvent;

/**
 * 处理跨 DAG 传播更新（通常是状态更改）的组件接口
 */
public interface IDagUpdatePropagator {

    /**
     * 当节点状态发生变化时调用。
     * 实现应该定义此变化如何影响 DAG 中的其他节点。
     *
     * @param event 节点状态变化的事件详情。
     * @param graph DAG 图实例，允许传播器查询和修改该图。
     */
    void onNodeStatusChanged(NodeStatusChangeEvent event, DagGraph graph);

    /**
     * 允许传播器使用图进行初始化，例如，当它直接与图事件交互或执行初始设置时，将自身注册为监听器。
     * @param graph DagGraph 实例。
     */
    void initialize(DagGraph graph);
    
}

