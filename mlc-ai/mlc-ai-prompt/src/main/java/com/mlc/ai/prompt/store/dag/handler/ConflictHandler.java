package com.mlc.ai.prompt.store.dag.handler;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictConstraintManager;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictConstraint;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictType;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.ConflictResolutionNode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 冲突处理器
 * 冲突解决通过独立的流向处理：前置节点 -> ConflictResolutionNode -> 下一个处理节点
 * <p>
 * 设计思想：
 * 1. 使用 ConflictConstraintManager 管理冲突约束，而不是通过边类型
 * 2. 冲突作为独立的元数据约束存储
 * 3. 支持冲突检测、标记、解决和澄清问题生成
 * 4. 与任务编排执行器协作，在调度时检查冲突约束
 */
@Slf4j
public class ConflictHandler {

    private final DagGraph dagGraph;
    /**
     *  获取冲突约束管理器
     */
    @Getter
    private final ConflictConstraintManager conflictManager;

    public ConflictHandler(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
        this.conflictManager = new ConflictConstraintManager(dagGraph);
    }

    /**
     * 检测并标记两个节点之间的冲突
     * @param node1 节点1
     * @param node2 节点2
     * @param conflictDescription 冲突描述
     * @param conflictType 冲突类型
     * @return 如果成功标记冲突则返回true
     */
    public boolean detectAndMarkConflict(IDagNode node1, IDagNode node2,
        String conflictDescription, ConflictType conflictType) {

        if (node1 == null || node2 == null) {
            log.error("无法添加冲突约束，节点不存在: {} 或 {}", node1, node2);
            return false;
        }
        return conflictManager.addConflictConstraint(node1, node2, conflictDescription, conflictType);
    }

    /**
     * 在流程中创建冲突解决节点
     * @param previousNodeId 前置节点ID
     * @param conflictDescription 冲突描述
     * @param conflictedNodeIds 涉及冲突的节点ID列表
     * @return 创建的冲突解决节点
     */
    public ConflictResolutionNode createConflictResolutionInFlow(String previousNodeId,
        String conflictDescription, List<String> conflictedNodeIds) {
        IDagNode previousNode = dagGraph.getNode(previousNodeId);
        if (previousNode == null) {
            log.error("前置节点未找到: {}", previousNodeId);
            return null;
        }

        log.info("在流程中创建冲突解决节点，前置节点: {}, 冲突: {}", previousNodeId, conflictDescription);

        // 创建冲突解决节点
        ConflictResolutionNode resolutionNode = new ConflictResolutionNode(conflictDescription, conflictedNodeIds);
        resolutionNode.setStatus(NodeStatus.PENDING_ANALYSIS);

        // 添加到图中
        dagGraph.addNode(resolutionNode);

        // 建立顺序流向：前置节点 -> 冲突解决节点
        dagGraph.addEdge(previousNode, resolutionNode, EdgeType.PROVIDES);

        return resolutionNode;
    }

    /**
     * 处理冲突解决结果
     * @param resolutionNodeId 冲突解决节点ID
     * @param strategy 解决策略
     * @param result 解决结果
     */
    public void processResolutionResult(String resolutionNodeId, String strategy, String result) {
        IDagNode node = dagGraph.getNode(resolutionNodeId);
        if (!(node instanceof ConflictResolutionNode resolutionNode)) {
            log.error("冲突解决节点未找到或类型不正确: {}", resolutionNodeId);
            return;
        }

        // 设置解决结果
        resolutionNode.setResolutionResult(strategy, result);

        log.info("冲突解决完成: {} - 策略: {}", result, strategy);
        dagGraph.updateNodeStatus(resolutionNodeId, NodeStatus.RESOLVED);

        // 解决相关的冲突约束
        resolveConflictsForResolutionNode(resolutionNode);
    }

    /**
     * 连接冲突解决结果到下一个节点
     * @param resolutionNodeId 冲突解决节点ID
     * @param nextNodeId 下一个节点ID
     * @return 是否连接成功
     */
    public boolean connectResolutionToNextNode(String resolutionNodeId, String nextNodeId) {
        IDagNode resolutionNode = dagGraph.getNode(resolutionNodeId);
        IDagNode nextNode = dagGraph.getNode(nextNodeId);

        if (resolutionNode == null || nextNode == null) {
            log.error("节点未找到 - 解决节点: {}, 下一节点: {}", resolutionNodeId, nextNodeId);
            return false;
        }

        // 建立从解决节点到下一节点的流向
        dagGraph.addEdge(resolutionNode, nextNode, EdgeType.PROVIDES);
        log.info("冲突解决节点 {} 已连接到下一节点: {}", resolutionNodeId, nextNodeId);
        return true;
    }

    /**
     * 解决与冲突解决节点相关的所有冲突约束
     * @param resolutionNode 冲突解决节点
     */
    private void resolveConflictsForResolutionNode(ConflictResolutionNode resolutionNode) {
        List<String> conflictedNodeIds = resolutionNode.getConflictedNodeIds();

        // 解决涉及这些节点的所有冲突
        for (int i = 0; i < conflictedNodeIds.size(); i++) {
            for (int j = i + 1; j < conflictedNodeIds.size(); j++) {
                String nodeId1 = conflictedNodeIds.get(i);
                String nodeId2 = conflictedNodeIds.get(j);

                conflictManager.resolveConflict(nodeId1, nodeId2,
                                                "通过冲突解决节点解决: " + resolutionNode.getResolutionResult());
            }
        }

        // 更新涉及的节点状态
        for (String nodeId : conflictedNodeIds) {
            IDagNode node = dagGraph.getNode(nodeId);
            if (node != null && node.getStatus() == NodeStatus.CONFLICTED) {
                dagGraph.updateNodeStatus(nodeId, NodeStatus.RESOLVED);
            }
        }
    }


    /**
     * 查找所有未解决的冲突
     * @return 未解决的冲突列表
     */
    public List<String> findUnresolvedConflicts() {
        return conflictManager.getUnresolvedConflicts().stream()
                              .map(ConflictConstraint::getDescription)
                              .toList();
    }

    /**
     * 检查节点是否可以被调度执行
     * 供Orchestrator调用
     */
    public boolean canNodeBeScheduled(String nodeId) {
        return conflictManager.canNodeBeScheduled(nodeId);
    }
}