package com.mlc.ai.prompt.store.dag.event;

import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import lombok.Getter;

@Getter
public class NodeStatusChangeEvent {
    private final IDagNode node;
    private final NodeStatus oldStatus;
    private final NodeStatus newStatus;
    private final long timestamp;

    public NodeStatusChangeEvent(IDagNode node, NodeStatus oldStatus, NodeStatus newStatus) {
        this.node = node;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.timestamp = System.currentTimeMillis();
    }

    @Override
    public String toString() {
        return "NodeStatusChangeEvent{" +
                "nodeId=" + node.getId() +
                ", nodeType=" + node.getType() +
                ", oldStatus=" + oldStatus +
                ", newStatus=" + newStatus +
                ", timestamp=" + timestamp +
                '}';
    }
}

