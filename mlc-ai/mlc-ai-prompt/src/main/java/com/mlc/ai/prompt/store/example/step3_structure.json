{"ormModel": {"name": "车辆管理系统模型", "description": "用于公司内部车辆的管理，核心是实现车辆调度、维修记录和违章处理的数字化，旨在提高车辆使用效率并降低整体管理成本。"}, "modelStructure": [{"entityName": "车辆", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "车辆", "props": {"name": "车辆"}, "sourceText": "车辆"}]}, {"entityName": "维修记录", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "维修记录", "props": {"name": "维修记录"}, "sourceText": "维修记录"}]}, {"entityName": "违章处理", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "违章处理", "props": {"name": "违章处理"}, "sourceText": "违章处理"}]}, {"entityName": "客户", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "客户", "props": {"name": "客户"}, "sourceText": "客户"}, {"intentType": "ADD_COLUMN", "targetConceptName": "客户", "props": {"name": "联系方式"}, "sourceText": "主要职责是记录客户的基本信息、租赁历史和联系方式。"}, {"intentType": "ADD_COLUMN", "targetConceptName": "客户", "props": {"name": "客户类型"}, "sourceText": "个人或公司"}, {"intentType": "ADD_COLUMN", "targetConceptName": "客户", "props": {"name": "客户等级", "stdSqlType": "数字"}, "sourceText": "类型为数字"}, {"intentType": "ADD_RELATION", "targetConceptName": "客户", "props": {"from": "客户", "to": "订单", "type": "o2m"}, "sourceText": "一个客户可以有多个订单"}, {"intentType": "ADD_RELATION", "targetConceptName": "客户", "props": {"from": "客户", "to": "车辆", "type": "m2m"}, "sourceText": "和车辆关联，记录客户租赁过的车辆"}, {"intentType": "DELETE_RELATION", "targetConceptName": "客户", "props": {"from": "客户", "to": "订单"}, "sourceText": "并删除与订单表的关联"}]}, {"entityName": "订单", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "订单", "props": {"name": "订单"}, "sourceText": "订单"}, {"intentType": "UPDATE_ENTITY", "targetConceptName": "订单", "props": {"useLogicalDelete": "启用"}, "sourceText": "启用订单表逻辑删除"}, {"intentType": "UPDATE_ENTITY", "targetConceptName": "订单", "props": {"tableName": "Customer_O2M_Order"}, "sourceText": "更新表名为Customer_O2M_Order"}, {"intentType": "ADD_COLUMN", "targetConceptName": "订单", "props": {"name": "订单金额"}, "sourceText": "比如订单金额和租赁时长"}, {"intentType": "ADD_COLUMN", "targetConceptName": "订单", "props": {"name": "租赁时长"}, "sourceText": "比如订单金额和租赁时长"}, {"intentType": "ADD_COMPUTE", "targetConceptName": "订单", "props": {"name": "订单统计", "logic": "统计求和", "fields": ["订单金额", "租赁时长"]}, "sourceText": "这个统计应该由订单实体来管理...比如订单金额和租赁时长"}]}, {"entityName": "驾驶员", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "驾驶员", "props": {"name": "驾驶员"}, "sourceText": "驾驶员信息"}]}, {"entityName": "租赁历史", "intents": [{"intentType": "ADD_ENTITY", "targetConceptName": "租赁历史", "props": {"name": "租赁历史"}, "sourceText": "客户租赁历史"}]}]}