// 对应 Prompt_3_Structured_Requirements_CoT.yaml 提示词
{
  "ormModel": {
    "name": "车辆管理系统模型",
    "description": "用于公司内部车辆调度、维修、订单及客户等信息的综合管理，提升车辆使用效率并降低管理成本"
  },
  "identified_concepts": [
    {
      "type_hint": "Entity",
      "name": "客户",
      "source_text": "客户"
    },
    {
      "type_hint": "Entity",
      "name": "车辆",
      "source_text": "车辆"
    },
    {
      "type_hint": "Entity",
      "name": "维修记录",
      "source_text": "维修记录"
    },
    {
      "type_hint": "Entity",
      "name": "订单",
      "source_text": "订单"
    },
    {
      "type_hint": "Entity",
      "name": "驾驶员信息",
      "source_text": "驾驶员信息"
    },
    {
      "type_hint": "Entity",
      "name": "客户实体",
      "source_text": "客户实体"
    },
    {
      "type_hint": "Entity.Column",
      "name": "基本信息",
      "source_text": "基本信息"
    },
    {
      "type_hint": "Entity.Column",
      "name": "租赁历史",
      "source_text": "租赁历史"
    },
    {
      "type_hint": "Entity.Column",
      "name": "联系方式",
      "source_text": "联系方式"
    },
    {
      "type_hint": "Entity.Relation",
      "name": "客户与订单关联",
      "source_text": "客户应该和订单关联"
    },
    {
      "type_hint": "Entity.Relation",
      "name": "客户与车辆关联",
      "source_text": "和车辆关联"
    },
    {
      "type_hint": "Entity",
      "name": "订单实体",
      "source_text": "订单实体"
    },
    {
      "type_hint": "Entity.Column",
      "name": "订单金额",
      "source_text": "订单金额"
    },
    {
      "type_hint": "Entity.Column",
      "name": "租赁时长",
      "source_text": "租赁时长"
    },
    {
      "type_hint": "Entity.Compute",
      "name": "订单金额与租赁时长统计",
      "source_text": "这个统计"
    }
  ],
  "identified_intents": [
    {
      "intent_type": "ADD_ENTITY",
      "target_concept_name": "车辆",
      "details": {
        "entity_name": "车辆"
      },
      "source_text": "车辆"
    },
    {
      "intent_type": "ADD_ENTITY",
      "target_concept_name": "维修记录",
      "details": {
        "entity_name": "维修记录"
      },
      "source_text": "维修记录"
    },
    {
      "intent_type": "ADD_ENTITY",
      "target_concept_name": "订单实体",
      "details": {
        "entity_name": "订单实体"
      },
      "source_text": "订单"
    },
    {
      "intent_type": "ADD_ENTITY",
      "target_concept_name": "驾驶员信息",
      "details": {
        "entity_name": "驾驶员信息"
      },
      "source_text": "驾驶员信息"
    },
    {
      "intent_type": "ADD_ENTITY",
      "target_concept_name": "客户实体",
      "details": {
        "entity_name": "客户实体"
      },
      "source_text": "客户实体代表租赁或使用我们车辆的个人或公司。"
    },
    {
      "intent_type": "ADD_COLUMN",
      "target_concept_name": "客户实体",
      "details": {
        "columns": [
          {
            "name": "基本信息",
            "is_foreign_key": false
          },
          {
            "name": "租赁历史",
            "is_foreign_key": false
          },
          {
            "name": "联系方式",
            "is_foreign_key": false
          }
        ]
      },
      "source_text": "记录客户的基本信息、租赁历史和联系方式"
    },
    {
      "intent_type": "ADD_RELATION",
      "target_concept_name": "客户与订单关联",
      "details": {
        "from_entity": "客户实体",
        "to_entity": "订单实体",
        "type": "o2m"
      },
      "source_text": "客户应该和订单关联，一个客户可以有多个订单"
    },
    {
      "intent_type": "ADD_RELATION",
      "target_concept_name": "客户与车辆关联",
      "details": {
        "from_entity": "客户实体",
        "to_entity": "车辆",
        "type": "m2m"
      },
      "source_text": "可能还需要和车辆关联，记录客户租赁过的车辆"
    },
    {
      "intent_type": "ADD_COLUMN",
      "target_concept_name": "订单实体",
      "details": {
        "columns": [
          {
            "name": "订单金额",
            "is_foreign_key": false
          },
          {
            "name": "租赁时长",
            "is_foreign_key": false
          }
        ]
      },
      "source_text": "订单金额和租赁时长"
    },
    {
      "intent_type": "ADD_COMPUTE",
      "target_concept_name": "订单实体",
      "details": {
        "compute_field_name": "订单金额与租赁时长统计",
        "expression_logic": "对'订单金额'和'租赁时长'进行求和统计",
        "source_fields": [
          "订单金额",
          "租赁时长"
        ]
      },
      "source_text": "这个统计应该由订单实体来管理"
    }
  ]
}