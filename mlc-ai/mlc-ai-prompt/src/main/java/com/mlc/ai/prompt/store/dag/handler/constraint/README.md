# 冲突约束管理重构

## 概述

本次重构将冲突处理从边类型系统中分离出来，创建了独立的冲突约束管理机制。这样做的目的是避免边类型语义的混淆，并提供更清晰的执行策略。

## 主要变化

### 1. 移除 EdgeType.CONFLICTS

**之前的问题：**
- `CONFLICTS` 边类型与其他边类型（如 `PROVIDES`）在语义上混淆
- 如果 `A --PROVIDES--> B` 且 `A --CONFLICTS--> B` 同时存在，Orchestrator 无法明确执行策略
- 冲突关系被错误地表示为图的拓扑结构的一部分

**解决方案：**
- 从 `EdgeType` 枚举中完全移除 `CONFLICTS`
- 冲突不再通过边来表示

### 2. 新增冲突约束管理器

**新增类：**
- `ConflictConstraintManager`: 核心冲突约束管理器
- `ConflictConstraint`: 冲突约束实体类
- `ConflictType`: 冲突类型枚举

**设计理念：**
- 冲突作为独立的元数据约束存储，不影响图的拓扑结构
- 支持多种冲突类型：语义冲突、资源冲突、逻辑冲突等
- 提供冲突检测、标记、解决和查询功能

### 3. Orchestrator 调度逻辑

**新的调度流程：**
1. **检查边类型条件**：根据边类型（如 `PROVIDES`）检查依赖条件是否满足
2. **检查冲突约束**：检查是否有冲突约束阻止节点执行
3. **最终调度决策**：只有当两个条件都满足时才能调度节点

**示例场景：**
```
图结构：A --PROVIDES--> B
冲突约束：Conflicts: { (A, C) }

Orchestrator 调度 B 时：
1. 检查 A 是否完成（边类型条件）
2. 检查 A 是否与其他活跃节点冲突（冲突约束）
3. 如果都满足，则可以调度 B
```

## 代码结构

```
dag/constraint/
├── ConflictConstraintManager.java    # 冲突约束管理器
├── ConflictConstraint.java           # 冲突约束实体
├── ConflictType.java                 # 冲突类型枚举
├── ConflictConstraintExample.java    # 使用示例
└── README.md                         # 本文档
```

## 使用示例

### 基本用法

```java
// 创建DAG图（自动包含冲突约束管理器）
DagGraph dagGraph = new DagGraph();
ConflictConstraintManager conflictManager = dagGraph.getConflictConstraintManager();

// 添加冲突约束
conflictManager.addConflictConstraint(
    nodeA, nodeB, 
    "描述冲突原因", 
    ConflictType.SEMANTIC_CONFLICT
);

// 检查节点是否可以调度
boolean canSchedule = conflictManager.canNodeBeScheduled(nodeA.getId());

// 解决冲突
conflictManager.resolveConflict(nodeA.getId(), nodeB.getId(), "解决方案描述");
```

### 在 ConflictHandler 中使用

```java
ConflictHandler conflictHandler = new ConflictHandler(dagGraph);

// 检测并标记冲突（必须指定冲突类型）
conflictHandler.detectAndMarkConflict(
    node1, node2, 
    "冲突描述", 
    ConflictType.SEMANTIC_CONFLICT
);
```

## 优势

1. **语义清晰**：边类型专注于执行流程，冲突约束专注于约束检查
2. **架构分离**：图结构与约束管理分离，职责更明确
3. **扩展性强**：支持多种冲突类型，易于扩展新的冲突检测逻辑
4. **调度明确**：Orchestrator 的调度逻辑更加清晰和可预测
5. **核心简洁**：移除了向后兼容代码，保持核心功能的简洁性

## 核心原则

- **明确性**：所有冲突约束都必须明确指定类型
- **分离性**：冲突约束与图拓扑结构完全分离
- **一致性**：Orchestrator 调度逻辑统一且可预测 