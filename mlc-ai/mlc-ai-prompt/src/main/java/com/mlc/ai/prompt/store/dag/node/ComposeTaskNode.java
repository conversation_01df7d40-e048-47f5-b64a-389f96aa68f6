package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.cache.DagGraphCacheManager;
import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import com.mlc.ai.prompt.store.dag.node.logic.IDagNodeLogic;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 组合任务节点 - 基于生命周期设计
 *
 * 核心优势：
 * - 避免类型爆炸: 只需要一个通用的 ComposeTaskNode 类
 * - 逻辑与框架分离: 业务逻辑存在于独立的接口实现中
 * - 可配置性和重用性: 通过注入不同逻辑实现不同任务
 */
@Getter
@Setter
@Slf4j
public class ComposeTaskNode extends AbstractDagNode {

    /**
     * 节点名称（用于标识和日志）
     */
    private String nodeName;

    /**
     * 节点逻辑处理接口
     * 负责节点的初始化、执行和完成阶段
     */
    private IDagNodeLogic nodeLogic;

    /**
     * 声明的输入数据键列表
     * 定义此节点需要从上游节点获取哪些数据
     */
    private List<String> inputDataKeys;

    /**
     * 声明的输出数据键
     * 定义此节点将产生的主要输出数据键
     */
    private String outputDataKey;

    /**
     * 节点是否已初始化
     */
    private boolean initialized = false;

    /**
     * 基础构造函数
     */
    public ComposeTaskNode(String nodeName, IDagNodeLogic nodeLogic,
                           List<String> inputDataKeys, String outputDataKey) {
        super(NodeType.COMPOSE_TASK);
        this.nodeName = nodeName;
        this.nodeLogic = nodeLogic;
        this.inputDataKeys = new ArrayList<>(inputDataKeys);
        this.outputDataKey = outputDataKey;
    }

    /**
     * 初始化节点
     * 在节点首次添加到DAG或准备执行前调用
     *
     * @param context 数据上下文
     * @return 初始化结果
     */
    public Map<String, Object> initialize(IDagDataContext context) {
        if (nodeLogic == null) {
            throw new IllegalStateException("节点逻辑未设置，节点: " + nodeName);
        }

        if (initialized) {
            log.debug("节点已初始化，跳过: {} (ID: {})", nodeName, getId());
            return Collections.emptyMap();
        }

        log.info("初始化节点: {} (ID: {})", nodeName, getId());

        try {
            // 通过上下文传递节点信息，避免直接设置到逻辑中
            Map<String, Object> initResult = nodeLogic.initialize(context);
            initialized = true;

            // 将初始化结果存储到节点的输出中
            if (initResult != null) {
                this.getOutputs().putAll(initResult);

                String executionId = context.getExecutionContext().getExecutionId();
                if (executionId != null) {
                    // 缓存初始化结果
                    DagGraphCacheManager.INSTANCE.putNodeOutput(executionId, this.getId(), initResult);
                    log.debug("节点 {} (ID: {}) 初始化结果已缓存 for executionId: {}", nodeName, getId(), executionId);
                }
            }

            return initResult;
        } catch (Exception e) {
            log.error("[{}] 节点初始化失败: {}", nodeName, e.getMessage(), e);
            throw new RuntimeException("节点初始化失败: " + nodeName, e);
        }
    }

    /**
     * 执行业务逻辑
     *
     * @param context 数据上下文
     * @return 执行结果
     */
    public Map<String, Object> execute(IDagDataContext context){
        if (nodeLogic == null) {
            throw new IllegalStateException("节点逻辑未设置，节点: " + nodeName);
        }

        String executionId = context.getExecutionContext().getExecutionId();
        try {
            // 1. 确保节点已初始化
            if (!initialized) {
                initialize(context);
            }

            // 2. 输入验证
            this.validateInput(context);

            // 3. 执行核心业务逻辑
            log.info("[{}] 开始执行节点逻辑: {}", getId(), nodeName);
            Map<String, Object> executeResult = nodeLogic.execute(context);

            // 4. 完成处理
            log.info("[{}] 节点执行完成，开始后续处理: {}", getId(), nodeName);
            Map<String, Object> finalResult = nodeLogic.finish(context, executeResult);

            // 5. 输出验证
            this.validateOutput(finalResult);

            if (executionId != null && finalResult != null) {
                DagGraphCacheManager.INSTANCE.putNodeOutput(executionId, this.getId(), finalResult);
                log.debug("节点 {} (ID: {}) 执行成功，输出已缓存 for executionId: {}", nodeName, this.getId(), executionId);
            } else if (finalResult == null) {
                log.warn("节点 {} (ID: {}) 执行结果为null，将不会缓存 for executionId: {}", nodeName, this.getId(), executionId);
            }

            return finalResult;
        } catch (Exception e) {
            log.error("[{}] 节点执行失败: {}", nodeName, e.getMessage(), e);
            throw new RuntimeException("节点执行失败: " + nodeName, e);
        }
    }

    // ==================== 验证方法 ====================

    /**
     * 验证输入数据
     *
     * @param dagContext 数据上下文
     */
    private void validateInput(IDagDataContext dagContext) {
        // 检查必需的输入键是否存在
        if (!inputDataKeys.isEmpty()) {
            for (String key : inputDataKeys) {
                if (dagContext.getInputData(key).isEmpty()) {
                    throw new RuntimeException(
                        String.format("节点 %s 缺少必需的输入数据: %s", nodeName, key));
                }
            }
        }
    }

    /**
     * 验证输出数据
     *
     * @param result 执行结果
     */
    private void validateOutput(Map<String, Object> result) {
        // 检查主要输出键是否存在
        if (outputDataKey != null && (result == null || !result.containsKey(outputDataKey))) {
            throw new IllegalStateException(
                String.format("节点 %s 未产生预期的输出数据: %s", nodeName, outputDataKey));
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取节点显示名称
     */
    public String getDisplayName() {
        return nodeName;
    }
}

