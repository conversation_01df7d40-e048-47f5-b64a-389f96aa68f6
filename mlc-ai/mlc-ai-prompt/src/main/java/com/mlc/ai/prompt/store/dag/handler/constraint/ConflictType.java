package com.mlc.ai.prompt.store.dag.handler.constraint;

/**
 * 冲突类型枚举
 * 定义不同类型的节点冲突
 */
public enum ConflictType {
    
    /**
     * 语义冲突：两个节点在语义上互相矛盾
     * 例如：同一个概念被定义为不同的类型（实体 vs 视图）
     */
    SEMANTIC_CONFLICT,
    
    /**
     * 资源冲突：两个节点竞争同一个资源
     * 例如：两个节点都要创建同名的表
     */
    RESOURCE_CONFLICT,
    
    /**
     * 逻辑冲突：两个节点的逻辑条件互斥
     * 例如：A要求字段为必填，B要求同一字段为可选
     */
    LOGICAL_CONFLICT,
    
    /**
     * 时序冲突：两个节点在时间上不能同时存在
     * 例如：删除操作和使用操作不能同时进行
     */
    TEMPORAL_CONFLICT,
    
    /**
     * 依赖冲突：两个节点形成循环依赖
     * 例如：A依赖B，B又依赖A
     */
    DEPENDENCY_CONFLICT
} 