package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 参数节点
 * 与意图相关的具体值，如实体名称、字段类型、计算公式等。这些参数节点可能有"已确定"、"待澄清"、"推荐值"等状态。
 * 专门用于表示一个意图或操作所需要的参数,"是什么参数"和"它的值是什么"。
 *
 * CoT 核心: 尝试根据【领域坐标系】的权重、上下文、通用知识来推断参数值 (例如，"ID" 字段通常是 NUMERIC 或 INTEGER)。如果成功推断，更新参数状态为"推荐值"或"已确定(基于推断)"。
 * 如果无法自信推断但必须前进，则生成一个明确的 Assumption 节点并与之关联，同时更新参数状态为"已确定(基于假设)"。
 */
@Getter
@Setter
public class ParameterNode extends AbstractDagNode {
    
    // 枚举标记值
    public enum ValueSource {
        DEFAULTED, // 默认
        USER_INPUT, // 用户输入值
        RECOMMENDED, // 推荐值
        DERIVATION, // 推导值
        ASSUMED, // 假设值
        CLARIFIED // 澄清值
    }

    // 属性字段
    private String parameterName;

    private String expectedType;

    private Object value;

    private ValueSource valueSource;

    private boolean isRequired;

    public ParameterNode(String parameterName, String expectedType, boolean isRequired) {
        super(NodeType.PARAMETER);
        this.parameterName = parameterName;
        this.expectedType = expectedType;
        this.isRequired = isRequired;
    }

    public void setValue(Object value, ValueSource source) {
        this.value = value;
        this.valueSource = source;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}

