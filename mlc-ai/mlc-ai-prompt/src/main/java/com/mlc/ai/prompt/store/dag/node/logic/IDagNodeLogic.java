package com.mlc.ai.prompt.store.dag.node.logic;

import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import java.util.Map;

/**
 * DAG节点逻辑接口，定义节点生命周期
 * 生命周期：initialize -> execute -> finish
 */
public interface IDagNodeLogic {
    
    /**
     * 初始化节点逻辑
     * 在节点首次创建或添加到DAG图时调用，用于准备执行环境
     * 
     * @param context 数据上下文
     * @return 初始化结果
     */
    Map<String, Object> initialize(IDagDataContext context);
    
    /**
     * 执行节点的核心业务逻辑
     * 
     * @param context 数据上下文
     * @return 执行结果
     */
    Map<String, Object> execute(IDagDataContext context);
    
    /**
     * 完成节点执行
     * 在节点执行完成后调用，用于清理资源或执行收尾工作
     * 
     * @param context 数据上下文
     * @param executeResult 执行结果
     * @return 最终处理结果
     */
    Map<String, Object> finish(IDagDataContext context, Map<String, Object> executeResult);
} 