package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 假设节点
 * 常和 ParameterNode 一起使用。
 * Agent在信息不足时做出的临时假设，这些假设可能需要后续验证或用户澄清。
 * 可能存在验证或者撤销的状态。
 * 
 * 基础节点通过PROVIDES边指向假设节点。
 */
@Getter
@Setter
public class AssumptionNode extends AbstractDagNode {
    
    // 属性字段
    private String assumptionDescription;

    public AssumptionNode(String assumptionDescription) {
        super(NodeType.ASSUMPTION);

        // 验证状态已经是 NodeStatus 枚举的一部分（ASSUMPTION_MADE、ASSUMPTION_VALIDATED、ASSUMPTION_INVALIDATED）
        setStatus(NodeStatus.ASSUMPTION_MADE);

        this.assumptionDescription = assumptionDescription;
    }
}

