package com.mlc.ai.prompt.store.dag.node.base;

public enum NodeType {
    OUTSIDE_BORDER, // 边界外节点
    USER_RAW_REQUEST, // 用户原始请求节点
    USER_UTTERANCE_SEGMENT, // 用户话语片段节点
    DOMAIN_COORDINATE, // 领域坐标节点
    IDENTIFIED_ATOMIC_INTENT, // 识别的原子意图节点
    PARAMETER, // 参数节点
    STRUCTURED_TASK, // 结构化任务节点
    COMPOSE_TASK, // 组合任务节点
    CLARIFICATION_QUESTION, // 澄清问题节点
    CLARIFICATION_ANSWER, // 澄清回答节点
    CLARIFICATION_CYCLE, // 澄清循环节点 - 自包含的多轮问答交互节点
    CONSTRAINT, // 约束节点
    ASSUMPTION, // 假设节点
    ASSUMPTION_VALIDATION, // 假设验证节点
    CONFLICT_RESOLUTION // 冲突解决节点
}

