package com.mlc.ai.prompt.store.dag.edge;

/**
 * DAG图中边的类型定义
 */
public enum EdgeType {
    
    /** 
     * 提供关系：
     * 语义：A --PROVIDES--> B 表示 A 为 B 提供必要条件，源节点向目标节点提供信息、数据或服务
     * 执行：A 完成后，B 才能开始执行
     * 传播：A 的状态变化会影响 B 的状态
     * 方向：信息流动方向，从提供者到消费者
     * <p>
     * 典型：LLM A 的CoT输出是 LLM B 的必要输入。
     */
    PROVIDES,

    /**
     * 信息通知:
     * 语义：源节点向目标节点传递信息/数据，但目标节点的执行不一定严格阻塞等待源节点。
     *      可能是可选数据、状态更新、或允许更灵活调度的信号。
     * 执行：
     *      1. 传递可选数据：若源完成，数据传递给目标（目标可能已开始或可因此开始）。
     *      2. 状态通知：源状态变化通知目标，目标可能调整行为。
     *      3. 允许并行：任务调度器可能基于此优化，允许目标与源部分并行。
     * <p>
     * 典型：LLM A 的中间结论可以提示 LLM B；LLM A 的完成状态通知一个监控节点；弱依赖。
     */
     INFORMS,

    /**
     * 抑制关系:
     * 语义：如果源节点成功执行，则目标节点不应被执行。
     * 执行：若源节点成功，任务调度器将不会调度目标节点。
     * <p>
     * 典型：如果方案A的LLM推理成功，则备用方案B的LLM节点无需执行。
     */
     INHIBITS
}

