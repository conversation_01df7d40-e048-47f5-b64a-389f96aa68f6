package com.mlc.ai.prompt.store.dag.node.base;

/**
 * 节点状态枚举
 * 定义了 DAG 中节点的不同状态。
 * 这些状态用于跟踪节点的处理和执行进度。
 * 
 * 状态按照节点生命周期分类：
 * 1. 初始/分析状态: PENDING_ANALYSIS, ANALYSIS_IN_PROGRESS
 * 2. 依赖处理状态: AWAITING_DEPENDENCIES, AWAITING_VALIDATION
 * 3. 澄清处理状态: REQUIRES_CLARIFY, CLARIFY_PENDING, CLARIFY_REPROCESSING
 * 4. 假设处理状态: ASSUMPTION_MADE, ASSUMPTION_VALIDATED, ASSUMPTION_INVALIDATED
 * 5. 执行相关状态: READY_FOR_EXECUTION, EXECUTION_IN_PROGRESS
 * 6. 终止状态: RESOLVED, COMPLETED, FAILED, CONFLICTED
 */
public enum NodeStatus {

    // === 初始/分析状态 ===
    
    /** 新添加的节点，尚未进行处理 */
    PENDING_ANALYSIS, 
    
    /** 节点正在被分析中 */
    ANALYSIS_IN_PROGRESS,

    // === 终止状态 - 成功 ===
    
    /** 节点已成功处理，信息已提取或验证 */
    RESOLVED, 
    
    /** 任务或分析已成功完成，通常用于子任务节点 */
    COMPLETED,

    // === 澄清处理状态 ===
    
    /** 节点信息不完整或模糊，需要用户澄清 */
    REQUIRES_CLARIFY, 
    
    /** 已生成澄清问题，等待用户回答 */
    CLARIFY_PENDING, 
    
    /** 已收到用户澄清，准备重新处理节点 */
    CLARIFY_REPROCESSING,

    // === 依赖处理状态 ===
    
    /** 节点正在等待其依赖节点(提供者)就绪 */
    AWAITING_DEPENDENCIES,

    // === 终止状态 - 失败 ===
    
    /** 节点处理或执行失败 */
    FAILED, 
    
    /** 节点被标记为冲突的一部分 */
    CONFLICTED,

    // === 假设处理状态 ===
    
    /** 已针对此节点做出假设 */
    ASSUMPTION_MADE,

    /** 节点依赖于假设，等待假设验证 */
    AWAITING_VALIDATION,

    /** 假设已被验证为有效 */
    ASSUMPTION_VALIDATED, 
    
    /** 假设已被验证为无效 */
    ASSUMPTION_INVALIDATED,

    // === 执行相关状态 ===
    
    /** 子任务所有参数均已准备就绪，可以执行 */
    READY_FOR_EXECUTION, 
    
    /** 子任务当前正在执行中 */
    EXECUTION_IN_PROGRESS,
    ;

    /**
     * 检查节点是否处于就绪状态
     * 就绪状态意味着节点可以作为提供者向其消费者节点提供信息
     */
    public static boolean isNodeReady(IDagNode node) {
        NodeStatus status = node.getStatus();
        return status == RESOLVED ||
            status == COMPLETED ||
            status == CLARIFY_REPROCESSING ||
            status == ASSUMPTION_VALIDATED;
    }
    
    /**
     * 检查节点是否处于终态
     * 终态表示节点已完成生命周期，不会再发生状态变化
     */
    public static boolean isTerminalStatus(NodeStatus status) {
        return status == RESOLVED ||
               status == COMPLETED ||
               status == FAILED ||
               status == CONFLICTED;
    }
    
    /**
     * 检查节点是否需要人工干预
     * 需要人工干预的状态通常需要用户输入或决策
     */
    public static boolean requiresHumanIntervention(NodeStatus status) {
        return status == REQUIRES_CLARIFY ||
               status == CLARIFY_PENDING ||
               status == CONFLICTED;
    }
}

