package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

/**
 * 澄清问题节点
 * 当某个参数缺失或某个概念模糊时生成的特定问题节点。
 *
 * 触发条件: 存在无法通过推断或假设解决的"待澄清"Parameter，或者存在明显的 Conflict。
 *
 * <p>
 * 澄清问题节点通过PROVIDES边指向需要澄清的目标节点。
 */
@Getter
@Setter
public class ClarificationQuestionNode extends AbstractDagNode {

    // 问题文本
    private String question;

    // 对齐焦点 orm、entity、relation等
    private String alignmentFocus;

    // 选择此问题的原因
    private String rationale;

    // 推荐选项
    private List<String> potentialRecommendations;

    public ClarificationQuestionNode() {
        super(NodeType.CLARIFICATION_QUESTION);
    }

    /**
     * 基础构造函数
     */
    public ClarificationQuestionNode(String question) {
        super(NodeType.CLARIFICATION_QUESTION);
        this.question = question;
    }

    public ClarificationQuestionNode(String question, String alignmentFocus, List<String> potentialRecommendations, String rationale) {
        super(NodeType.CLARIFICATION_QUESTION);
        this.question = question;
        this.alignmentFocus = alignmentFocus;
        this.potentialRecommendations = potentialRecommendations;
        this.rationale = rationale;
    }
}

