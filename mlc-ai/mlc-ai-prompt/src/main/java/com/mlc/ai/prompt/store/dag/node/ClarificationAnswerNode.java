package com.mlc.ai.prompt.store.dag.node;


import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 处理澄清回答的节点类
 * 该类用于在DAG中处理用户对澄清问题的回答
 *
 * 只能与澄清问题节点关联，是澄清节点的消费者
 * 
 * 澄清问题节点通过PROVIDES边指向回答节点。
 */
@Getter
@Setter
public class ClarificationAnswerNode extends AbstractDagNode {

    // 回答内容
    private String answerContent;

    protected ClarificationAnswerNode() {
        super(NodeType.CLARIFICATION_ANSWER);
    }

    public ClarificationAnswerNode(String answerContent) {
        super(NodeType.CLARIFICATION_ANSWER);
        this.answerContent = answerContent;
    }
}
