package com.mlc.ai.prompt.store.dag.handler;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.AssumptionNode;
import com.mlc.ai.prompt.store.dag.node.AssumptionValidationNode;
import com.mlc.ai.prompt.store.dag.util.DagRelationshipQueryUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 假设处理器
 * <p>
 * 流向语义：
 * 前置节点 -> AssumptionNode -> AssumptionValidationNode -> 下一个处理节点
 * <p>
 * 设计原则：
 * 1. 假设是处理流程中的一个步骤
 * 2. 假设验证是独立的流程节点
 * 3. 通过边关系实现状态自动传播
 */
@Slf4j
public class AssumptionHandler {
    
    private final DagGraph dagGraph;
    private final DagRelationshipQueryUtil relationshipQueryUtil;
    
    public AssumptionHandler(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
        this.relationshipQueryUtil = new DagRelationshipQueryUtil(dagGraph);
    }
    
    /**
     * 在流程中创建假设节点
     * @param previousNodeId 前置节点ID
     * @param assumptionDescription 假设描述
     * @param assumedValue 假定值
     * @return 创建的假设节点
     */
    public AssumptionNode createAssumptionInFlow(String previousNodeId, 
                                                String assumptionDescription, 
                                                Object assumedValue) {
        IDagNode previousNode = dagGraph.getNode(previousNodeId);
        if (previousNode == null) {
            log.error("前置节点未找到: {}", previousNodeId);
            return null;
        }
        
        log.info("在流程中创建假设节点，前置节点: {}, 假设: {}", previousNodeId, assumptionDescription);
        
        // 创建假设节点
        AssumptionNode assumptionNode = new AssumptionNode(assumptionDescription);
        assumptionNode.setAttachProp("assumedValue", assumedValue);
        assumptionNode.setStatus(NodeStatus.PENDING_ANALYSIS);
        
        // 添加到图中
        dagGraph.addNode(assumptionNode);
        
        // 建立顺序流向：前置节点 -> 假设节点
        dagGraph.addEdge(previousNode, assumptionNode, EdgeType.PROVIDES);
        
        return assumptionNode;
    }
    
    /**
     * 创建假设验证节点
     * @param assumptionNodeId 假设节点ID
     * @param validationDescription 验证描述
     * @return 创建的验证节点
     */
    public AssumptionValidationNode createValidationNode(String assumptionNodeId, 
                                                        String validationDescription) {
        IDagNode assumptionNode = dagGraph.getNode(assumptionNodeId);
        if (!(assumptionNode instanceof AssumptionNode)) {
            log.error("假设节点未找到或类型不正确: {}", assumptionNodeId);
            return null;
        }
        
        log.info("为假设节点 {} 创建验证节点", assumptionNodeId);
        
        // 创建验证节点
        AssumptionValidationNode validationNode = new AssumptionValidationNode(validationDescription);
        validationNode.setStatus(NodeStatus.PENDING_ANALYSIS);
        
        // 添加到图中
        dagGraph.addNode(validationNode);
        
        // 建立顺序流向：假设节点 -> 验证节点
        dagGraph.addEdge(assumptionNode, validationNode, EdgeType.PROVIDES);
        
        return validationNode;
    }
    
    /**
     * 处理假设验证结果
     * @param validationNodeId 验证节点ID
     * @param isValid 验证结果
     * @param rationale 验证依据
     */
    public void processValidationResult(String validationNodeId, boolean isValid, String rationale) {
        IDagNode node = dagGraph.getNode(validationNodeId);
        if (!(node instanceof AssumptionValidationNode validationNode)) {
            log.error("验证节点未找到或类型不正确: {}", validationNodeId);
            return;
        }
        
        // 设置验证结果
        validationNode.setValidationResult(isValid, rationale);
        
        if (isValid) {
            log.info("假设验证通过: {}", rationale);
            dagGraph.updateNodeStatus(validationNodeId, NodeStatus.RESOLVED);
        } else {
            log.info("假设验证失败: {}", rationale);
            dagGraph.updateNodeStatus(validationNodeId, NodeStatus.FAILED);
        }
    }
    
    /**
     * 连接验证结果到下一个节点
     * @param validationNodeId 验证节点ID
     * @param nextNodeId 下一个节点ID
     * @return 是否连接成功
     */
    public boolean connectValidationToNextNode(String validationNodeId, String nextNodeId) {
        IDagNode validationNode = dagGraph.getNode(validationNodeId);
        IDagNode nextNode = dagGraph.getNode(nextNodeId);
        
        if (validationNode == null || nextNode == null) {
            log.error("节点未找到 - 验证节点: {}, 下一节点: {}", validationNodeId, nextNodeId);
            return false;
        }
        
        // 建立从验证节点到下一节点的流向
        dagGraph.addEdge(validationNode, nextNode, EdgeType.PROVIDES);
        log.info("假设验证节点 {} 已连接到下一节点: {}", validationNodeId, nextNodeId);
        return true;
    }
    

    /**
     * 获取验证节点对应的假设节点
     * @param validationNode 验证节点
     * @return 假设节点
     */
    public AssumptionNode getAssumptionFromValidation(AssumptionValidationNode validationNode) {
        IDagNode assumptionNode = relationshipQueryUtil.getAssumptionForValidation(validationNode);
        return assumptionNode instanceof AssumptionNode ? (AssumptionNode) assumptionNode : null;
    }
    
    /**
     * 获取假设节点的基础节点列表
     * @param assumptionNode 假设节点
     * @return 基础节点列表
     */
    public List<IDagNode> getBasisNodesForAssumption(AssumptionNode assumptionNode) {
        return relationshipQueryUtil.getBasisNodesForAssumption(assumptionNode);
    }
    
    /**
     * 获取验证节点的下一个节点
     * @param validationNode 验证节点
     * @return 下一个节点
     */
    public IDagNode getNextNodeFromValidation(AssumptionValidationNode validationNode) {
        return dagGraph.getOutgoingEdges(validationNode).stream()
            .filter(edge -> edge.getType() == EdgeType.PROVIDES)
            .map(edge -> dagGraph.getGraph().getEdgeTarget(edge))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 查找所有待验证的假设节点
     * @return 待验证的假设节点列表
     */
    public List<AssumptionNode> findAllPendingAssumptions() {
        return dagGraph.getAllNodes().stream()
            .filter(node -> node instanceof AssumptionNode)
            .map(node -> (AssumptionNode) node)
            .filter(assumptionNode -> assumptionNode.getStatus() == NodeStatus.PENDING_ANALYSIS)
            .collect(Collectors.toList());
    }
    
    /**
     * 查找所有已验证的假设验证节点
     * @return 已验证的验证节点列表
     */
    public List<AssumptionValidationNode> findAllResolvedValidations() {
        return dagGraph.getAllNodes().stream()
            .filter(node -> node instanceof AssumptionValidationNode)
            .map(node -> (AssumptionValidationNode) node)
            .filter(validationNode -> validationNode.getStatus() == NodeStatus.RESOLVED)
            .collect(Collectors.toList());
    }
}