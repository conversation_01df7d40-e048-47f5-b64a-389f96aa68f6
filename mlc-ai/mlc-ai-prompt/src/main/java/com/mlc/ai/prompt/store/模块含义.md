1.全局上下文 (Global Context Store): 需要一个地方存储和传递各个步骤的输出，作为后续步骤的输入。这可以是一个结构化的数据对象 (例如，一个不断被充实的JSON对象)。

CoT Prompting Execution Plan (元计划 - DAG驱动)：

核心理念：Orchestrator 维护一个动态的“需求理解与规划图 (Requirement Understanding and Planning Graph - RUPG)”（即你描述的 DAG）。
            LLM 被用作一个“节点处理器”，根据 Orchestrator 传递的上下文和任务类型，对图中的一个或多个节点进行推理，并输出新的节点、边或更新现有节点的状态。

它将整个过程从一个线性的“管道”模型升级为一个更动态、更灵活的“图”模型。LLM 负责在图的特定节点上进行“原子级”的 CoT 推理，而外部代码（我们称之为“Orchestrator”或“DAG Manager”）则负责构建、维护、遍历这个图，并根据图的状态驱动整个流程。

将 LLM 视为一个强大的、可编程的“推理组件”，而不是一个端到端的黑箱。这本质上是在构建一个小型、专用的“基于LLM的推理系统”。

LLM 的强项: LLM 更擅长处理模糊性、进行语义理解、从非结构化文本中提取信息、生成创意性内容。一旦信息被结构化到 DAG 中，LLM 在“格式化输出”这一步的优势就不那么明显了。