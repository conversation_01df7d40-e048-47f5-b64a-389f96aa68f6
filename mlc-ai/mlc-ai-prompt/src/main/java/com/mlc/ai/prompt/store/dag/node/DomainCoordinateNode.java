package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * todo: 加个泛型：如Orm, Entity, Entity.Column, Entity.Computes 等
 *      是否创建具体的类型:更具体的如 EntityNode、ColumnNode等
 *
 * 提取领域坐标化的节点
 * 如"模型"、"客户表"、"字段"、"计算"。这些节点可以附带其在【领域坐标系】中初步对齐的类型（如Orm, Entity, Entity.Column, Entity.Computes）。
 */
@Getter
@Setter
public class DomainCoordinateNode extends AbstractDagNode {
    
    // 属性字段
    private String conceptName;

    private String originalText;

    private Map<String, String> domainCoordinates;

    private Double confidenceScore;

    public DomainCoordinateNode(String conceptName, String originalText) {
        super(NodeType.DOMAIN_COORDINATE);
        this.conceptName = conceptName;
        this.originalText = originalText;
    }
}

