//对应 Prompt_2_Clarifying Questions_CoT.yaml 提示词
[
  {
    "question": "您计划设计的这个'车辆管理系统模型'，其核心业务目标或主要用户场景是什么？它期望解决哪些关键问题或满足哪些核心需求？",
    "alignmentFocus": "ORM",
    "potentialRecommendations": ["例如：车辆调度、维修保养记录、客户租赁管理、违章处理等"],
    "rationale": "用户提及进行'车辆管理系统模型'的实体设计，首先需要明确该系统的整体业务目标和范围，这是ORM建模的基础。"
  },
  {
    "question": "基于这些核心目标，除了您已提到的'客户'实体，这个'车辆管理系统模型'还需要管理和追踪哪些最核心的业务对象或概念？（这些通常会成为系统的核心实体/表，例如 '车辆' 本身，或者与车辆相关的 '订单'、'维修记录' 等）",
    "alignmentFocus": "Entity",
    "potentialRecommendations": ["车辆", "订单", "维修记录", "驾驶员", "租赁合同"],
    "rationale": "在明确系统核心目标后，需要识别出系统的主要业务对象，这些将构成模型的核心实体。用户已提及'客户'，此问题旨在引导用户思考其他必要的实体。"
  },
  {
    "question": "对于您计划添加的'客户'实体，它在车辆管理系统中具体代表什么核心概念或业务对象，并预计承担哪些核心职责？",
    "alignmentFocus": "Entity",
    "potentialRecommendations": [],
    "rationale": "用户明确要添加'客户'实体，需要澄清其在业务中的核心概念和主要职责，这是定义该实体的基础。",
  },
  {
    "question": "这个'客户'实体，为了完整地发挥其作用，最需要和哪些其他核心实体（例如您可能考虑的 '车辆'、'订单' 等）建立业务关联？您初步设想这些关联是什么类型的（比如一个客户可以有多辆车，或者一个客户可以有多个订单），以及这些关联背后的业务逻辑是什么？",
    "alignmentFocus": "Relation",
    "potentialRecommendations": ["车辆", "订单", "租赁记录"],
    "rationale": "明确'客户'实体与其他核心实体的主要关系及其业务含义，这对构建模型结构至关重要。"
  },
  {
    "question": "关于您提到的对'字段A'和'字段B'进行统计求和，这个统计结果（以及可能的字段A和字段B本身）您认为应该由哪个核心业务实体来承载或管理？是'客户'实体，还是与'车辆'、'订单'或其他业务概念相关的实体？",
    "alignmentFocus": "Entity",
    "potentialRecommendations": ["客户", "车辆", "订单", "特定的统计/报表实体"],
    "rationale": "用户提及'字段A和字段B的统计和'，需要明确这些数据或统计结果归属于哪个实体，以确保数据模型的完整性和准确性。"
  }
]