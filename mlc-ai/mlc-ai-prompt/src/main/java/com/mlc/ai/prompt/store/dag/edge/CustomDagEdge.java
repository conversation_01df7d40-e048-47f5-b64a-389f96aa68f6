package com.mlc.ai.prompt.store.dag.edge;

import lombok.Getter;
import org.jgrapht.graph.DefaultEdge;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Getter
public class CustomDagEdge extends DefaultEdge {
    private final String id;
    private final EdgeType type;
    private final Map<String, Object> attributes;
    private final long creationTimestamp;

    public CustomDagEdge(EdgeType type) {
        this.id = UUID.randomUUID().toString();
        this.type = type;
        this.attributes = new HashMap<>();
        this.creationTimestamp = System.currentTimeMillis();
    }

    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }

    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }

    @Override
    public String toString() {
        return "(" + getSource() + " : " + getTarget() + " : " + type + " : " + id + ")";
    }
}

