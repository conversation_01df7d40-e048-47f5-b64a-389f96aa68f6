package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.List;
import lombok.Getter;
import lombok.Setter;


/**
 * 边界外节点
 */
@Getter
@Setter
public class OutsideBorderNode extends AbstractDagNode {

    private String remainingStatement;

    // 边界外概念
    private List<Cross> cross;

    @Getter
    @Setter
    public static class Cross {
        private String edge;
        private String description;
    }

    public OutsideBorderNode(){
        super(NodeType.OUTSIDE_BORDER);
    }

    public OutsideBorderNode(String remainingStatement, List<Cross> cross) {
        super(NodeType.OUTSIDE_BORDER);
        this.remainingStatement = remainingStatement;
        this.cross = cross;
    }
}
