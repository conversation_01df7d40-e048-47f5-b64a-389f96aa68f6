package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.ArrayList;

/**
 * 表示 DAG 中用于标识概念列表和意图列表的节点
 */
@Getter
@Setter
public class IdentifyAtomicIntentNode extends AbstractDagNode {

    /**
     * 识别的概念列表
     */
    private List<IdentifiedConcept> identifiedConcepts;
    
    /**
     * 识别的意图列表
     */
    private List<IdentifiedIntent> identifiedIntents;

    public IdentifyAtomicIntentNode() {
        super(NodeType.IDENTIFIED_ATOMIC_INTENT);
        this.identifiedConcepts = new ArrayList<>();
        this.identifiedIntents = new ArrayList<>();
    }

    // ==================== 结构化需求分析数据结构 ====================

    /**
     * 识别的概念
     */
    @Getter
    @Setter
    public static class IdentifiedConcept {
        private String typeHint;
        private String name;
        private String sourceText;
    }

    /**
     * 识别的意图
     */
    @Getter
    @Setter
    public static class IdentifiedIntent {
        private String intentType;
        private String targetConceptName;
        private Object details;
        private String sourceText;
    }

    public enum IntentType {
        DESCRIBE_CONTEXT, // 描述上下文

        CREATE_ENTITY,
        UPDATE_ENTITY,
        DELETE_ENTITY,

        ADD_COLUMNS,
        DELETE_COLUMNS,
        UPDATE_COLUMNS,

        ADD_RELATIONS,
        DELETE_RELATIONS,
        UPDATE_RELATIONS,

        ADD_UNIQUEKEYS,
        DELETE_UNIQUEKEYS,
        UPDATE_UNIQUEKEYS,

        ADD_INDEXES,
        DELETE_INDEXES,
        UPDATE_INDEXES,

        CREATE_COMPUTES,
        DELETE_COMPUTES,
        UPDATE_COMPUTES,

        CREATE_FILTERS,
        DELETE_FILTERS,
        UPDATE_FILTERS,
    }
}

