package com.mlc.ai.prompt.store.dag.propagation;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.event.NodeStatusChangeEvent;
import com.mlc.ai.prompt.store.dag.propagation.base.IDagUpdatePropagator;
import com.mlc.ai.prompt.store.dag.propagation.part.AutoInitialStatusManager;
import com.mlc.ai.prompt.store.dag.propagation.part.BatchStatusPropagator;
import com.mlc.ai.prompt.store.dag.propagation.part.DependencyQueryService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 此传播器将对节点状态变化做出反应，并尝试相应地更新依赖节点。
 * <p>
 * 依赖方向说明：采用提供者->消费者(Provider->Consumer)模式
 * Provider --PROVIDES_FOR--> Consumer
 * 这符合信息流动和推理链的自然方向
 * <p>
 * A 产生 B
 * A 推导出 B
 * A 的完成是 B 开始的前提
 * 信息从 A 流向 B
 */
@Slf4j
public class DagProviderSpreadPropagator implements IDagUpdatePropagator {

    private DagGraph dagGraph;

    /**
     *  获取依赖查询服务
     */
    @Getter
    private DependencyQueryService dependencyService;
    /**
     *  获取批量传播器
     */
    @Getter
    private BatchStatusPropagator batchPropagator;
    /**
     *  获取初始状态管理器
     */
    @Getter
    private AutoInitialStatusManager initialStatusManager;
    
    // 组件初始化状态
    private volatile boolean initialized = false;

    @Override
    public void initialize(DagGraph graph) {
        this.dagGraph = graph;
        
        // 初始化核心组件
        initializeComponents();
        
        // 注册事件监听器
        registerEventListeners();
        
        this.initialized = true;
        log.info("DagProviderSpreadPropagator 已初始化完成");
    }
    
    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        // 依赖查询服务
        this.dependencyService = new DependencyQueryService(dagGraph);
        
        // 批量状态传播器
        this.batchPropagator = new BatchStatusPropagator(dagGraph, dependencyService);
        
        // 自动初始状态管理器
        this.initialStatusManager = new AutoInitialStatusManager(dagGraph, dependencyService);
        
        log.debug("核心组件初始化完成");
    }
    
    /**
     * 注册事件监听器
     */
    private void registerEventListeners() {
        // 注册状态变化监听器
        this.dagGraph.setNodeStatusChangeListener(this::handleNodeStatusChange);
        
        // 注册节点添加监听器
        this.dagGraph.setNodeAddListener(this::handleNodeAdded);
        
        log.debug("事件监听器注册完成");
    }
    
    /**
     * 处理节点状态变化事件
     */
    private void handleNodeStatusChange(NodeStatusChangeEvent event) {
        if (!initialized) {
            log.warn("传播器尚未初始化，忽略状态变化事件: {}", event.getNode().getId());
            return;
        }
        
        log.debug("处理节点状态变化: {} {} -> {}", 
                 event.getNode().getId(), event.getOldStatus(), event.getNewStatus());
        
        // 委托给批量传播器处理
        batchPropagator.processNodeStatusChange(event);
    }
    
    /**
     * 处理节点添加事件
     */
    private void handleNodeAdded(IDagNode node) {
        if (!initialized) {
            log.warn("传播器尚未初始化，忽略节点添加事件: {}", node.getId());
            return;
        }
        
        log.debug("处理节点添加: {} (类型: {})", node.getId(), node.getType());
        
        // 委托给初始状态管理器处理
        initialStatusManager.handleNodeAdded(node);
    }

    @Override
    public void onNodeStatusChanged(NodeStatusChangeEvent event, DagGraph graph) {
        if (this.dagGraph == null) {
            this.dagGraph = graph;
            initialize(graph);
        }
        handleNodeStatusChange(event);
    }
    
    /**
     * 强制检查所有节点状态
     * 用于在参数值更新后强制触发状态传播
     */
    public void forceCheckAllNodes() {
        if (!initialized) {
            log.warn("传播器尚未初始化，无法执行强制检查");
            return;
        }
        
        log.info("开始强制检查所有节点状态...");
        batchPropagator.forceCheckAllNodes();
        log.info("强制状态检查完成");
    }


    /**
     * 关闭传播器，清理资源
     */
    public void shutdown() {
        if (initialized) {
            log.info("正在关闭 DagProviderSpreadPropagator...");
            
            // 清理事件监听器
            if (dagGraph != null) {
                dagGraph.setNodeStatusChangeListener(null);
                dagGraph.setNodeAddListener(null);
            }
            
            // 重置组件
            this.dependencyService = null;
            this.batchPropagator = null;
            this.initialStatusManager = null;
            this.initialized = false;
            
            log.info("DagProviderSpreadPropagator 已关闭");
        }
    }

    /**
     * 批量初始化节点
     */
    public void batchInitializeNodes(List<IDagNode> nodes) {
        if (!initialized) {
            log.warn("传播器尚未初始化，无法批量初始化节点");
            return;
        }
        
        log.info("批量初始化 {} 个节点", nodes.size());
        initialStatusManager.batchInitializeNodes(nodes);
    }
}