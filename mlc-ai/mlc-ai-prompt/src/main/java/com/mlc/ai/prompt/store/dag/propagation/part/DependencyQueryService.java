package com.mlc.ai.prompt.store.dag.propagation.part;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.edge.CustomDagEdge;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;

/**
 * 依赖关系查询服务
 * 负责查询和分析DAG中节点之间的依赖关系
 */
@Slf4j
public class DependencyQueryService {
    
    private final DagGraph dagGraph;
    
    public DependencyQueryService(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
    }
    
    /**
     * 获取消费者节点的所有依赖提供者
     * 提供者(Provider) --PROVIDES_FOR--> 消费者(Consumer)
     */
    public List<IDagNode> findProvidersForConsumer(IDagNode consumerNode) {
        List<IDagNode> providers = new ArrayList<>();
        
        // 检查入边 - 当提供者指向消费者 (Provider --PROVIDES_FOR--> Consumer)
        for (CustomDagEdge edge : dagGraph.getIncomingEdges(consumerNode)) {
            if (isProviderToConsumerEdge(edge)) {
                IDagNode sourceProvider = dagGraph.getGraph().getEdgeSource(edge);
                log.debug("消费者 {} 的依赖提供者 {}: {} --PROVIDES_FOR--> {}",
                        consumerNode.getId(), sourceProvider.getId(), sourceProvider.getId(), consumerNode.getId());
                providers.add(sourceProvider);
            }
        }
        return providers;
    }
    
    /**
     * 获取所有依赖于指定提供者节点的消费者节点
     * 提供者(Provider) --PROVIDES_FOR--> 消费者(Consumer)
     */
    public List<IDagNode> findConsumersOfProvider(IDagNode providerNode) {
        List<IDagNode> consumerNodes = new ArrayList<>();
        // 检查出边 - 当提供者指向消费者时 (Provider --PROVIDES_FOR--> Consumer)
        for (CustomDagEdge edge : dagGraph.getOutgoingEdges(providerNode)) {
            if (isProviderToConsumerEdge(edge)) {
                IDagNode consumer = dagGraph.getGraph().getEdgeTarget(edge);
                log.debug("找到依赖于提供者 {} 的消费者 {}: {} --PROVIDES_FOR--> {}",
                         providerNode.getId(), consumer.getId(), providerNode.getId(), consumer.getId());
                consumerNodes.add(consumer);
            }
        }
        return consumerNodes;
    }
    
    /**
     * 检查所有依赖提供者是否就绪
     */
    public boolean areAllProvidersReady(List<IDagNode> providers) {
        if (providers.isEmpty()) {
            return true;
        }
        
        return providers.stream().allMatch(provider -> 
            NodeStatus.isNodeReady(provider) || provider.getStatus() == NodeStatus.ASSUMPTION_MADE);
    }
    
    /**
     * 检查消费者节点的所有其他提供者（排除指定提供者）是否已就绪
     */
    public boolean areOtherProvidersReady(IDagNode consumer, IDagNode excludedProvider) {
        List<IDagNode> allProviders = findProvidersForConsumer(consumer);
        if (allProviders.isEmpty()) {
            log.debug("消费者节点 {} (排除 {}) 没有其他提供者", consumer.getId(), excludedProvider.getId());
            return true;
        }

        for (IDagNode provider : allProviders) {
            if (provider.equals(excludedProvider)) {
                continue; // 跳过被排除的提供者
            }

            // 检查这个"其他"的提供者是否处于某种形式的"就绪"状态
            boolean isProviderEffectivelyReady = NodeStatus.isNodeReady(provider) || 
                                               provider.getStatus() == NodeStatus.ASSUMPTION_MADE;

            if (!isProviderEffectivelyReady) {
                log.debug("消费者节点 {} 仍在等待其他提供者 {} (状态: {}) 就绪",
                        consumer.getId(), provider.getId(), provider.getStatus());
                return false;
            }
        }
        log.debug("消费者节点 {} 的所有其他提供者 (排除 {}) 均已就绪", consumer.getId(), excludedProvider.getId());
        return true;
    }
    
    /**
     * 检查从提供者到消费者是否是强依赖关系
     */
    public boolean isStrongDependency(IDagNode provider, IDagNode consumer) {
        Set<CustomDagEdge> edges = dagGraph.getOutgoingEdges(provider).stream()
                                           .filter(edge -> dagGraph.getGraph().getEdgeTarget(edge).equals(consumer))
                                           .collect(Collectors.toSet());

        return edges.stream().anyMatch(this::isProviderToConsumerEdge);
    }
    
    /**
     * 检查从提供者到消费者的依赖关系是否关键
     */
    public boolean isDependencyCritical(IDagNode provider, IDagNode consumer) {
        // 如果消费者是结构化子任务，认为依赖是关键的
        if (consumer.getType() == NodeType.STRUCTURED_TASK) {
            return true;
        }
        
        // 如果提供者是参数节点且消费者依赖它，也认为是关键的
        if (provider.getType() == NodeType.PARAMETER) {
            return true;
        }

        return false;
    }
    
    /**
     * 获取节点的所有传递性依赖（递归查找所有上游依赖）
     */
    public Set<IDagNode> getTransitiveDependencies(IDagNode node) {
        Set<IDagNode> visited = new HashSet<>();
        Set<IDagNode> dependencies = new HashSet<>();
        collectTransitiveDependencies(node, visited, dependencies);
        return dependencies;
    }
    
    private void collectTransitiveDependencies(IDagNode node, Set<IDagNode> visited, Set<IDagNode> dependencies) {
        if (visited.contains(node)) {
            return; // 避免循环依赖
        }
        visited.add(node);
        
        List<IDagNode> directProviders = findProvidersForConsumer(node);
        for (IDagNode provider : directProviders) {
            dependencies.add(provider);
            collectTransitiveDependencies(provider, visited, dependencies);
        }
    }
    
    /**
     * 获取节点的所有传递性消费者（递归查找所有下游消费者）
     */
    public Set<IDagNode> getTransitiveConsumers(IDagNode node) {
        Set<IDagNode> visited = new HashSet<>();
        Set<IDagNode> consumers = new HashSet<>();
        collectTransitiveConsumers(node, visited, consumers);
        return consumers;
    }
    
    private void collectTransitiveConsumers(IDagNode node, Set<IDagNode> visited, Set<IDagNode> consumers) {
        if (visited.contains(node)) {
            return; // 避免循环依赖
        }
        visited.add(node);
        
        List<IDagNode> directConsumers = findConsumersOfProvider(node);
        for (IDagNode consumer : directConsumers) {
            consumers.add(consumer);
            collectTransitiveConsumers(consumer, visited, consumers);
        }
    }
    
    /**
     * 检查两个节点之间是否存在依赖路径
     */
    public boolean hasPathBetween(IDagNode from, IDagNode to) {
        Set<IDagNode> transitiveConsumers = getTransitiveConsumers(from);
        return transitiveConsumers.contains(to);
    }
    
    /**
     * 获取指定状态的所有提供者
     */
    public List<IDagNode> getProvidersByStatus(IDagNode consumer, NodeStatus status) {
        return findProvidersForConsumer(consumer).stream()
                .filter(provider -> provider.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定类型的所有提供者
     */
    public List<IDagNode> getProvidersByType(IDagNode consumer, NodeType type) {
        return findProvidersForConsumer(consumer).stream()
                .filter(provider -> provider.getType() == type)
                .collect(Collectors.toList());
    }
    

    /**
     * 判断边是否为提供者->消费者类型
     */
    private boolean isProviderToConsumerEdge(CustomDagEdge edge) {
        return edge.getType() == EdgeType.PROVIDES;
    }
}