package com.mlc.ai.prompt.store.dag;

import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.edge.CustomDagEdge;
import com.mlc.ai.prompt.store.dag.event.NodeStatusChangeEvent;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import java.util.function.Supplier;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jgrapht.Graph;
import org.jgrapht.graph.DirectedAcyclicGraph;
import org.jgrapht.traverse.TopologicalOrderIterator;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * DAG图实现类
 * 专注于图结构管理，包括节点和边的添加、删除、查询等基本操作
 */
@Slf4j
public class DagGraph {

    @Getter
    private final Graph<IDagNode, CustomDagEdge> graph;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    // 节点状态变化的监听器，供更新传播机制使用
    @Setter
    private Consumer<NodeStatusChangeEvent> nodeStatusChangeListener;
    
    // 节点添加监听器，用于自动化状态设置
    @Setter
    private Consumer<IDagNode> nodeAddListener;

    public DagGraph() {
        this.graph = new DirectedAcyclicGraph<>(CustomDagEdge.class);
    }

    // 同步执行图形操作，确保写锁
    private <T> T executeGraphOperation(Supplier<T> operation) {
        lock.writeLock().lock();
        try {
            return operation.get();
        } finally {
            lock.writeLock().unlock();
        }
    }

    public void addNode(IDagNode node) {
        executeGraphOperation(() -> {
            // 检查节点是否已存在
            if (graph.containsVertex(node)) {
                log.warn("节点已存在，跳过添加: ID={}, Type={}", node.getId(), node.getType());
                return null;
            }
            
            boolean added = graph.addVertex(node);
            if (added) {
                log.debug("成功添加节点: ID={}, Type={}", node.getId(), node.getType());
                // 触发节点添加监听器
                if (nodeAddListener != null) {
                    nodeAddListener.accept(node);
                }
            } else {
                log.error("节点添加失败，可能存在问题: ID={}, Type={}", node.getId(), node.getType());
            }
            return null;
        });
    }

    // --- 节点状态管理 ---
    public void updateNodeStatus(String nodeId, NodeStatus newStatus) {
        executeGraphOperation(() -> {
            IDagNode node = getNodeInternal(nodeId);
            if (node != null) {
                NodeStatus oldStatus = node.getStatus();
                if (oldStatus != newStatus) {
                    node.setStatus(newStatus);
                    log.info("节点 {} 状态从 {} 更新为 {}", nodeId, oldStatus, newStatus);
                    if (nodeStatusChangeListener != null) {
                        // 触发状态变化监听器
                        nodeStatusChangeListener.accept(new NodeStatusChangeEvent(node, oldStatus, newStatus));
                    }
                }
            } else {
                log.error("节点 ID 无效或不存在: {}", nodeId);
                throw new IllegalArgumentException("节点 ID 无效或不存在: " + nodeId);
            }
            return null;
        });
    }

    public boolean removeNode(IDagNode node) {
        return executeGraphOperation(() -> graph.removeVertex(node));
    }

    public void addEdge(IDagNode sourceNode, IDagNode targetNode, EdgeType edgeType) {
        executeGraphOperation(() -> {
            if (!graph.containsVertex(sourceNode)) {
                graph.addVertex(sourceNode);
            }
            if (!graph.containsVertex(targetNode)) {
                graph.addVertex(targetNode);
            }
            CustomDagEdge edge = new CustomDagEdge(edgeType);
            graph.addEdge(sourceNode, targetNode, edge);
            return null;
        });
    }

    public boolean removeEdge(CustomDagEdge edge) {
        return executeGraphOperation(() -> graph.removeEdge(edge));
    }

    public IDagNode getNode(String nodeId) {
        lock.readLock().lock();
        try {
            return graph.vertexSet().stream()
                    .filter(node -> node.getId().equals(nodeId))
                    .findFirst()
                    .orElse(null);
        } finally {
            lock.readLock().unlock();
        }
    }

    public Set<IDagNode> getAllNodes() {
        lock.readLock().lock();
        try {
            return graph.vertexSet();
        } finally {
            lock.readLock().unlock();
        }
    }

    public Set<CustomDagEdge> getAllEdges() {
        lock.readLock().lock();
        try {
            return graph.edgeSet();
        } finally {
            lock.readLock().unlock();
        }
    }

    public Set<CustomDagEdge> getIncomingEdges(IDagNode node) {
        lock.readLock().lock();
        try {
            return graph.incomingEdgesOf(node);
        } finally {
            lock.readLock().unlock();
        }
    }

    public Set<CustomDagEdge> getOutgoingEdges(IDagNode node) {
        lock.readLock().lock();
        try {
            return graph.outgoingEdgesOf(node);
        } finally {
            lock.readLock().unlock();
        }
    }

    public List<IDagNode> getParents(IDagNode node) {
        lock.readLock().lock();
        try {
            return graph.incomingEdgesOf(node).stream()
                    .map(graph::getEdgeSource)
                    .collect(Collectors.toList());
        } finally {
            lock.readLock().unlock();
        }
    }

    public List<IDagNode> getChildren(IDagNode node) {
        lock.readLock().lock();
        try {
            return graph.outgoingEdgesOf(node).stream()
                    .map(graph::getEdgeTarget)
                    .collect(Collectors.toList());
        } finally {
            lock.readLock().unlock();
        }
    }

    public List<IDagNode> getTopologicalOrder() {
        lock.readLock().lock();
        try {
            TopologicalOrderIterator<IDagNode, CustomDagEdge> topologicalOrderIterator =
                    new TopologicalOrderIterator<>(graph);
            List<IDagNode> orderedNodes = new ArrayList<>();
            topologicalOrderIterator.forEachRemaining(orderedNodes::add);
            return orderedNodes;
        } finally {
            lock.readLock().unlock();
        }
    }

    // 内部方法，用于在写锁内获取节点
    private IDagNode getNodeInternal(String nodeId) {
        return graph.vertexSet().stream()
                .filter(n -> n.getId().equals(nodeId))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        lock.readLock().lock();
        try {
            return graph.toString();
        } finally {
            lock.readLock().unlock();
        }
    }
}

