package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 假设验证节点
 * 用于在顺序流向中验证假设的有效性
 * 
 * 流向：AssumptionNode -> AssumptionValidationNode -> 下一个处理节点
 * 
 * 假设节点通过PROVIDES边指向验证节点。
 */
@Getter
@Setter
public class AssumptionValidationNode extends AbstractDagNode {
    
    // 验证结果
    private boolean isValid;
    
    // 验证描述
    private String validationDescription;
    
    // 验证依据
    private String validationRationale;

    public AssumptionValidationNode() {
        super(NodeType.ASSUMPTION_VALIDATION);
    }

    public AssumptionValidationNode(String validationDescription) {
        super(NodeType.ASSUMPTION_VALIDATION);
        this.validationDescription = validationDescription;
    }

    /**
     * 设置验证结果
     * @param isValid 是否有效
     * @param rationale 验证依据
     */
    public void setValidationResult(boolean isValid, String rationale) {
        this.isValid = isValid;
        this.validationRationale = rationale;
    }
} 