package com.mlc.ai.prompt.store.dag.util;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import com.mlc.ai.prompt.store.dag.edge.CustomDagEdge;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * DAG关系查询工具类
 * 提供通过edge关系查询节点间关联的工具方法，替代原有的ID字段查询方式
 *
 * 提供了所有节点间关系的查询方法
 * 支持一对一、一对多、多对多关系
 */
@Slf4j
public class DagRelationshipQueryUtil {

    private final DagGraph dagGraph;

    public DagRelationshipQueryUtil(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
    }

    /**
     * 获取假设验证节点对应的假设节点
     * 通过入边查找PROVIDES类型的假设节点
     */
    public IDagNode getAssumptionForValidation(IDagNode validationNode) {
        return getIncomingNodesByType(validationNode, EdgeType.PROVIDES, NodeType.ASSUMPTION)
                .stream()
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取澄清回答节点对应的澄清问题节点
     * 通过入边查找PROVIDES类型的澄清问题节点
     */
    public IDagNode getQuestionForAnswer(IDagNode answerNode) {
        return getIncomingNodesByType(answerNode, EdgeType.PROVIDES, NodeType.CLARIFICATION_QUESTION)
                .stream()
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取假设节点的基础节点列表
     * 通过入边查找所有PROVIDES类型的前置节点
     */
    public List<IDagNode> getBasisNodesForAssumption(IDagNode assumptionNode) {
        return getIncomingNodesByEdgeType(assumptionNode, EdgeType.PROVIDES);
    }


    /**
     * 获取结构化子任务的参数节点列表
     * 通过入边查找所有PROVIDES类型的参数节点
     */
    public List<IDagNode> getParametersForSubTask(IDagNode subTaskNode) {
        return getIncomingNodesByType(subTaskNode, EdgeType.PROVIDES, NodeType.PARAMETER);
    }


    /**
     * 通过入边和边类型查找节点
     */
    public List<IDagNode> getIncomingNodesByEdgeType(IDagNode targetNode, EdgeType edgeType) {
        Set<CustomDagEdge> incomingEdges = dagGraph.getIncomingEdges(targetNode);
        return incomingEdges.stream()
                .filter(edge -> edge.getType() == edgeType)
                .map(edge -> dagGraph.getGraph().getEdgeSource(edge))
                .collect(Collectors.toList());
    }

    /**
     * 通过入边、边类型和节点类型查找节点
     */
    public List<IDagNode> getIncomingNodesByType(IDagNode targetNode, EdgeType edgeType, NodeType nodeType) {
        return getIncomingNodesByEdgeType(targetNode, edgeType).stream()
                .filter(node -> node.getType() == nodeType)
                .collect(Collectors.toList());
    }

    /**
     * 通过出边和边类型查找节点
     */
    public List<IDagNode> getOutgoingNodesByEdgeType(IDagNode sourceNode, EdgeType edgeType) {
        Set<CustomDagEdge> outgoingEdges = dagGraph.getOutgoingEdges(sourceNode);
        return outgoingEdges.stream()
                .filter(edge -> edge.getType() == edgeType)
                .map(edge -> dagGraph.getGraph().getEdgeTarget(edge))
                .collect(Collectors.toList());
    }

    /**
     * 通过出边、边类型和节点类型查找节点
     */
    public List<IDagNode> getOutgoingNodesByType(IDagNode sourceNode, EdgeType edgeType, NodeType nodeType) {
        return getOutgoingNodesByEdgeType(sourceNode, edgeType).stream()
                .filter(node -> node.getType() == nodeType)
                .collect(Collectors.toList());
    }

    /**
     * 检查两个节点之间是否存在指定类型的边
     */
    public boolean hasEdgeBetween(IDagNode sourceNode, IDagNode targetNode, EdgeType edgeType) {
        Set<CustomDagEdge> outgoingEdges = dagGraph.getOutgoingEdges(sourceNode);
        return outgoingEdges.stream()
                .anyMatch(edge -> edge.getType() == edgeType && 
                         dagGraph.getGraph().getEdgeTarget(edge).equals(targetNode));
    }

    /**
     * 获取两个节点之间的边
     */
    public CustomDagEdge getEdgeBetween(IDagNode sourceNode, IDagNode targetNode, EdgeType edgeType) {
        Set<CustomDagEdge> outgoingEdges = dagGraph.getOutgoingEdges(sourceNode);
        return outgoingEdges.stream()
                .filter(edge -> edge.getType() == edgeType && 
                               dagGraph.getGraph().getEdgeTarget(edge).equals(targetNode))
                .findFirst()
                .orElse(null);
    }
}