package com.mlc.ai.prompt.store.dag.handler;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.ClarificationAnswerNode;
import com.mlc.ai.prompt.store.dag.util.DagRelationshipQueryUtil;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 澄清处理器
 * 用于处理DAG中与澄清相关的功能，支持顺序流向：
 * OutsideBorderNode -> ClarificationQuestionNode -> ClarificationAnswerNode -> IdentifyAtomicIntentNode
 */
@Slf4j
public class ClarificationHandler {
    
    private final DagGraph dagGraph;
    private final DagRelationshipQueryUtil relationshipQueryUtil;

    /**
     * 获取ClarificationHandler实例
     * @param dagGraph DAG图实例
     */
    public ClarificationHandler(DagGraph dagGraph) {
         this.dagGraph = dagGraph;
         this.relationshipQueryUtil = new DagRelationshipQueryUtil(dagGraph);
    }

    /**
     * 在流程中创建澄清问题节点
     * @param previousNodeId 前置节点ID
     * @param questionText 澄清问题文本
     * @param recommendations 可选的回答选项
     * @return 创建的澄清问题节点
     */
    public ClarificationQuestionNode createClarificationQuestion(String previousNodeId, String questionText, List<String> recommendations) {
        IDagNode previousNode = dagGraph.getNode(previousNodeId);
        if (previousNode == null) {
            log.error("前置节点未找到: {}", previousNodeId);
            return null;
        }
        
        // 创建澄清问题节点
        ClarificationQuestionNode questionNode = new ClarificationQuestionNode(questionText);
        if (recommendations != null) {
            questionNode.setPotentialRecommendations(recommendations);
        }
        questionNode.setStatus(NodeStatus.CLARIFY_PENDING); // 问题等待用户回答
        
        // 将问题节点添加到图中
        dagGraph.addNode(questionNode);
        
        // 建立从前置节点到问题节点的顺序流向
        dagGraph.addEdge(previousNode, questionNode, EdgeType.PROVIDES);
        
        log.info("从节点 {} 创建澄清问题: {}", previousNodeId, questionText);
        return questionNode;
    }
    
    /**
     * 处理澄清问题的回答，创建ClarificationAnswerNode并建立流向
     * @param questionNodeId 澄清问题节点ID
     * @param response 用户回答
     * @return 创建的澄清回答节点
     */
    public ClarificationAnswerNode resolveClarification(String questionNodeId, String response) {
        if (!(dagGraph.getNode(questionNodeId) instanceof ClarificationQuestionNode questionNode)) {
            log.error("未找到澄清问题节点或类型不正确: {}", questionNodeId);
            return null;
        }
        
        // 创建澄清回答节点
        ClarificationAnswerNode answerNode = new ClarificationAnswerNode(response);
        answerNode.setStatus(NodeStatus.RESOLVED);
        
        // 将回答节点添加到图中
        dagGraph.addNode(answerNode);
        
        // 建立从问题节点到回答节点的顺序流向
        dagGraph.addEdge(questionNode, answerNode, EdgeType.PROVIDES);
        
        // 更新问题节点状态为已解决
        dagGraph.updateNodeStatus(questionNodeId, NodeStatus.RESOLVED);
        
        log.info("澄清问题 {} 已回答，创建回答节点: {}", questionNodeId, answerNode.getId());
        return answerNode;
    }

    /**
     * 将澄清回答节点连接到下一个处理节点
     * @param answerNodeId 澄清回答节点ID
     * @param nextNodeId 下一个节点ID
     * @return 是否连接成功
     */
    public boolean connectAnswerToNextNode(String answerNodeId, String nextNodeId) {
        IDagNode answerNode = dagGraph.getNode(answerNodeId);
        IDagNode nextNode = dagGraph.getNode(nextNodeId);
        
        if (answerNode == null || nextNode == null) {
            log.error("节点未找到 - 回答节点: {}, 下一节点: {}", answerNodeId, nextNodeId);
            return false;
        }
        
        // 建立从回答节点到下一节点的流向
        dagGraph.addEdge(answerNode, nextNode, EdgeType.PROVIDES);
        log.info("澄清回答节点 {} 已连接到下一节点: {}", answerNodeId, nextNodeId);
        return true;
    }

    /**
     * 获取澄清问题的前置节点
     * @param questionNode 澄清问题节点
     * @return 前置节点，如果没有找到返回null
     */
    public IDagNode getPreviousNode(ClarificationQuestionNode questionNode) {
        return dagGraph.getIncomingEdges(questionNode).stream()
            .filter(edge -> edge.getType() == EdgeType.PROVIDES)
            .map(edge -> dagGraph.getGraph().getEdgeSource(edge))
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取澄清回答的问题节点
     * @param answerNode 澄清回答节点
     * @return 对应的问题节点
     */
    public ClarificationQuestionNode getQuestionFromAnswer(ClarificationAnswerNode answerNode) {
        IDagNode questionNode = relationshipQueryUtil.getQuestionForAnswer(answerNode);
        return questionNode instanceof ClarificationQuestionNode ? (ClarificationQuestionNode) questionNode : null;
    }

    /**
     * 获取澄清回答节点的下一个节点
     * @param answerNode 澄清回答节点
     * @return 下一个节点，如果没有找到返回null
     */
    public IDagNode getNextNodeFromAnswer(ClarificationAnswerNode answerNode) {
        return dagGraph.getOutgoingEdges(answerNode).stream()
            .filter(edge -> edge.getType() == EdgeType.PROVIDES)
            .map(edge -> dagGraph.getGraph().getEdgeTarget(edge))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 查找澄清问题对应的回答节点
     * @param questionNodeId 澄清问题节点ID
     * @return 对应的回答节点，如果没有找到返回null
     */
    public ClarificationAnswerNode findAnswerForQuestion(String questionNodeId) {
        IDagNode questionNode = dagGraph.getNode(questionNodeId);
        if (questionNode == null) {
            return null;
        }
        
        // 通过出边查找回答节点
        return dagGraph.getOutgoingEdges(questionNode).stream()
            .filter(edge -> edge.getType() == EdgeType.PROVIDES)
            .map(edge -> dagGraph.getGraph().getEdgeTarget(edge))
            .filter(node -> node instanceof ClarificationAnswerNode)
            .map(node -> (ClarificationAnswerNode) node)
            .findFirst()
            .orElse(null);
    }

    /**
     * 查找所有待处理的澄清问题
     * @return 待处理的澄清问题列表
     */
    public List<ClarificationQuestionNode> findAllPendingClarifications() {
        return dagGraph.getAllNodes().stream()
            .filter(node -> node instanceof ClarificationQuestionNode)
            .map(node -> (ClarificationQuestionNode) node)
            .filter(qNode -> qNode.getStatus() == NodeStatus.CLARIFY_PENDING)
            .collect(Collectors.toList());
    }

    /**
     * 查找所有已解决的澄清回答节点
     * @return 已解决的澄清回答节点列表
     */
    public List<ClarificationAnswerNode> findAllResolvedAnswers() {
        return dagGraph.getAllNodes().stream()
            .filter(node -> node instanceof ClarificationAnswerNode)
            .map(node -> (ClarificationAnswerNode) node)
            .filter(answerNode -> answerNode.getStatus() == NodeStatus.RESOLVED)
            .collect(Collectors.toList());
    }

    /**
     * 检查澄清问题是否已被回答
     * @param questionNodeId 澄清问题节点ID
     * @return 如果已被回答则返回true
     */
    public boolean isQuestionAnswered(String questionNodeId) {
        return findAnswerForQuestion(questionNodeId) != null;
    }

    /**
     * 获取完整的澄清上下文（问题+回答）用于后续的意图识别
     * @param answerNode 澄清回答节点
     * @return 包含问题和回答的上下文信息
     */
    public ClarificationContext getClarificationContext(ClarificationAnswerNode answerNode) {
        ClarificationQuestionNode questionNode = getQuestionFromAnswer(answerNode);
        if (questionNode == null) {
            return null;
        }
        
        return new ClarificationContext(
            questionNode.getQuestion(),
            questionNode.getAlignmentFocus(),
            questionNode.getRationale(),
            questionNode.getPotentialRecommendations(),
            answerNode.getAnswerContent()
        );
    }

    /**
     * 获取流程中所有的澄清上下文
     * @return 所有澄清上下文的列表
     */
    public List<ClarificationContext> getAllClarificationContexts() {
        return findAllResolvedAnswers().stream()
            .map(this::getClarificationContext)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 澄清上下文数据类 用于传递给意图识别节点的完整澄清信息
     */
    public record ClarificationContext(String question, String alignmentFocus, String rationale,
                                       List<String> potentialRecommendations, String answer) {
        @Override
        public String toString() {
            return String.format("Q: %s | A: %s", question, answer);
        }
    }
} 