package com.mlc.ai.prompt.store.dag.execution;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.edge.CustomDagEdge;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.ParameterNode;
import com.mlc.ai.prompt.store.dag.node.StructuredTaskNode;
import com.mlc.ai.prompt.store.dag.util.DagRelationshipQueryUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务执行管理器
 * 管理DAG中任务的执行，包括查找可执行任务、执行任务等
 */
@Slf4j
public class TaskExecutionManager {
    private final DagGraph dagGraph;
    private final DagRelationshipQueryUtil relationshipQueryUtil;
    
    public TaskExecutionManager(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
        this.relationshipQueryUtil = new DagRelationshipQueryUtil(dagGraph);
    }
    
    /**
     * 执行所有准备就绪的任务
     * 
     * @return 执行的任务列表
     */
    public List<StructuredTaskNode> executeAllReadyTasks() {
        log.info("查找并执行所有准备就绪的任务...");
        
        // 获取所有状态为READY_FOR_EXECUTION的任务节点
        List<StructuredTaskNode> readyTasks = dagGraph.getAllNodes().stream()
                                                      .filter(node -> node instanceof StructuredTaskNode)
                                                      .map(node -> (StructuredTaskNode) node)
                                                      .filter(task -> task.getStatus() == NodeStatus.READY_FOR_EXECUTION) // 只查找准备就绪的任务，不包括等待验证的
                                                      .collect(Collectors.toList());
        
        log.info("找到 {} 个准备就绪的任务", readyTasks.size());
        
        // 执行每个准备就绪的任务
        List<StructuredTaskNode> executedTasks = new ArrayList<>();
        for (StructuredTaskNode task : readyTasks) {
            log.info("执行任务: {} (ID: {})", task.getTaskDescription(), task.getId());
            
            // 如果依赖于假设的参数，则需等待验证
            boolean dependsOnAssumption = checkIfDependsOnAssumption(task);
            if (dependsOnAssumption) {
                log.warn("任务 {} 依赖于未验证的假设，需等待验证后再执行", task.getId());
                continue;
            }
            
            // 正常执行流程
            try {
                boolean success = executeTask(task);
                if (success) {
                    executedTasks.add(task);
                }
            } catch (Exception e) {
                log.error("执行任务 {} 时发生错误: {}", task.getId(), e.getMessage(), e);
                dagGraph.updateNodeStatus(task.getId(), NodeStatus.FAILED);
            }
        }
        
        return executedTasks;
    }

    /**
     * 检查任务是否依赖于假设状态的参数
     * 
     * @param task 要检查的任务
     * @return 如果依赖于任何假设，返回true
     */
    private boolean checkIfDependsOnAssumption(StructuredTaskNode task) {
        // 获取任务的所有参数依赖
        List<IDagNode> dependencyNodes = new ArrayList<>();
        for (CustomDagEdge edge : dagGraph.getIncomingEdges(task)) {
            if (edge.getType() == EdgeType.PROVIDES) {
                IDagNode provider = dagGraph.getGraph().getEdgeSource(edge);
                dependencyNodes.add(provider);
            }
        }
        
        // 检查是否有任何依赖处于假设状态
        return dependencyNodes.stream()
                .anyMatch(node -> node.getStatus() == NodeStatus.ASSUMPTION_MADE);
    }

    /**
     * 执行单个任务
     */
    private boolean executeTask(StructuredTaskNode taskNode) {
        try {
            dagGraph.updateNodeStatus(taskNode.getId(), NodeStatus.EXECUTION_IN_PROGRESS);
            
            // 获取最新的参数值（实时获取，确保包含传播后的值）
            Map<String, Object> parameters = getLatestParameterValues(taskNode);
            
            // 执行任务
            String result = simulateTaskExecution(taskNode, parameters);
            taskNode.setExecutionTasks(result);
            
            // 更新任务状态
            dagGraph.updateNodeStatus(taskNode.getId(), NodeStatus.COMPLETED);
            log.info("任务 {} 执行成功", taskNode.getId());
            return true;
        } catch (Exception e) {
            log.error("任务 {} 执行失败: {}", taskNode.getId(), e.getMessage(), e);
            dagGraph.updateNodeStatus(taskNode.getId(), NodeStatus.FAILED);
            return false;
        }
    }
    
    /**
     * 获取任务的最新参数值
     * 确保获取到传播后的最新值，而不是初始的null值
     */
    private Map<String, Object> getLatestParameterValues(StructuredTaskNode taskNode) {
        Map<String, Object> parameters = new HashMap<>();
        
        // 通过关系查询工具获取参数节点
        List<IDagNode> parameterNodes = relationshipQueryUtil.getParametersForSubTask(taskNode);
        
        for (IDagNode paramNode : parameterNodes) {
            if (paramNode instanceof ParameterNode parameter) {
                // 获取最新值，包括通过传播更新的值
                Object latestValue = getParameterLatestValue(parameter);
                parameters.put(parameter.getParameterName(), latestValue);
                
                log.debug("参数 {} 的最新值: {} (来源: {})", 
                    parameter.getParameterName(), latestValue, parameter.getValueSource());
            }
        }
        
        return parameters;
    }
    
    /**
     * 获取参数的最新值
     * 优先级：直接值 > 属性中的resolvedValue > null
     */
    private Object getParameterLatestValue(ParameterNode parameter) {
        // 1. 首先检查参数的直接值
        Object directValue = parameter.getValue();
        if (directValue != null) {
            return directValue;
        }
        
        // 2. 检查属性中的resolvedValue（通过澄清或假设设置的值）
        Object resolvedValue = parameter.getAttachProp("resolvedValue");
        if (resolvedValue != null) {
            return resolvedValue;
        }
        
        // 3. 检查属性中的assumedValue（假设值）
        Object assumedValue = parameter.getAttachProp("assumedValue");
        if (assumedValue != null) {
            return assumedValue;
        }
        
        // 4. 返回null（表示参数值尚未确定）
        return null;
    }
    
    /**
     * 模拟任务执行
     * 实际项目中，这里会调用真正的业务逻辑
     */
    private String simulateTaskExecution(StructuredTaskNode taskNode, Map<String, Object> parameters) {
        StringBuilder result = new StringBuilder();
        result.append("执行任务: ").append(taskNode.getTaskDescription()).append("\n");
        result.append("使用参数: ").append(parameters).append("\n");
        result.append("完成时间: ").append(System.currentTimeMillis());
        
        log.info("模拟执行任务 {}: {}", taskNode.getId(), taskNode.getTaskDescription());
        return result.toString();
    }
} 