package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户话语片段节点
 * 代表用户原始请求中的一个特定片段，通常是经过分割和预处理的文本片段。
 * 
 * 原始请求节点通过PROVIDES边指向话语片段节点。
 */
@Getter
@Setter
public class UserUtteranceSegmentNode extends AbstractDagNode {

    // 属性字段
    private String segmentText; // 片段文本内容
    private int segmentIndex; // 片段在原始请求中的索引
    private String segmentType; // 片段类型，如 "ENTITY_MENTION", "ACTION_VERB", "CONSTRAINT" 等

    public UserUtteranceSegmentNode(String segmentText, int segmentIndex) {
        super(NodeType.USER_UTTERANCE_SEGMENT);
        this.segmentText = segmentText;
        this.segmentIndex = segmentIndex;
    }

    public UserUtteranceSegmentNode(String segmentText, int segmentIndex, String segmentType) {
        super(NodeType.USER_UTTERANCE_SEGMENT);
        this.segmentText = segmentText;
        this.segmentIndex = segmentIndex;
        this.segmentType = segmentType;
    }
}