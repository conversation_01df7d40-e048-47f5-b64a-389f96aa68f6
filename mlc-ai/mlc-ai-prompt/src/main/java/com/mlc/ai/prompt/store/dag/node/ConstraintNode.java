package com.mlc.ai.prompt.store.dag.node;

import com.mlc.ai.prompt.store.dag.node.base.AbstractDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import lombok.Getter;
import lombok.Setter;

/**
 * 约束节点
 * 从领域坐标系或用户输入中提取的约束，例如"唯一键"、"字段A必须是数字类型"。
 * 
 * 约束节点通过PROVIDES边指向应用的目标节点。
 */
@Getter
@Setter
public class ConstraintNode extends AbstractDagNode {
    
    // 属性字段
    private String constraintDescription;
    private String constraintType; // e.g., UNIQUE_KEY, DATA_TYPE

    public ConstraintNode(String constraintDescription, String constraintType) {
        super(NodeType.CONSTRAINT);
        this.constraintDescription = constraintDescription;
        this.constraintType = constraintType;
    }
}

