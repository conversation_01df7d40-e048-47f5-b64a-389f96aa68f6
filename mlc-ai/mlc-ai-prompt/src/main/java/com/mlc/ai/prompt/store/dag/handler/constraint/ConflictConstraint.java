package com.mlc.ai.prompt.store.dag.handler.constraint;

import lombok.Getter;
import lombok.ToString;

/**
 * 冲突约束
 * 表示两个节点之间的冲突关系和约束信息
 */
@Getter
@ToString
public class ConflictConstraint {
    
    private final String node1Id;
    private final String node2Id;
    private final String description;
    private final ConflictType conflictType;
    private final long createdTimestamp;
    
    private boolean resolved;
    private String resolution;
    private long resolvedTimestamp;
    
    public ConflictConstraint(String node1Id, String node2Id, String description, 
                             ConflictType conflictType, long createdTimestamp) {
        this.node1Id = node1Id;
        this.node2Id = node2Id;
        this.description = description;
        this.conflictType = conflictType;
        this.createdTimestamp = createdTimestamp;
        this.resolved = false;
    }
    
    /**
     * 解决冲突
     * @param resolution 解决方案描述
     */
    public void resolve(String resolution) {
        this.resolved = true;
        this.resolution = resolution;
        this.resolvedTimestamp = System.currentTimeMillis();
    }
    
    /**
     * 检查是否涉及指定节点
     */
    public boolean involvesNode(String nodeId) {
        return node1Id.equals(nodeId) || node2Id.equals(nodeId);
    }
    
    /**
     * 获取冲突的另一个节点ID
     */
    public String getOtherNodeId(String nodeId) {
        if (node1Id.equals(nodeId)) {
            return node2Id;
        } else if (node2Id.equals(nodeId)) {
            return node1Id;
        } else {
            throw new IllegalArgumentException("节点 " + nodeId + " 不在此冲突约束中");
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConflictConstraint that = (ConflictConstraint) o;
        return (node1Id.equals(that.node1Id) && node2Id.equals(that.node2Id)) ||
               (node1Id.equals(that.node2Id) && node2Id.equals(that.node1Id));
    }
    
    @Override
    public int hashCode() {
        // 确保相同的两个节点总是产生相同的哈希码，无论顺序如何
        return node1Id.hashCode() + node2Id.hashCode();
    }
} 