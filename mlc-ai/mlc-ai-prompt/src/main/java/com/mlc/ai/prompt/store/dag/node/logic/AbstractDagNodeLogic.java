package com.mlc.ai.prompt.store.dag.node.logic;

import com.mlc.ai.prompt.store.dag.context.IDagDataContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * DAG节点逻辑抽象实现
 */
@Slf4j
public abstract class AbstractDagNodeLogic implements IDagNodeLogic {

    /**
     * 节点名称
     */
    protected final String nodeName;
    
    /**
     * 构造函数
     * 
     * @param nodeName 节点名称
     */
    public AbstractDagNodeLogic(String nodeName) {
        this.nodeName = nodeName;
    }

    /**
     * 获取当前节点ID
     * 从上下文中获取节点信息，避免直接依赖
     */
    protected String getCurrentNodeId(IDagDataContext context) {
        return context.getCurrentNode().getId();
    }

    /**
     * 初始化实现 - 默认返回空Map
     */
    @Override
    public Map<String, Object> initialize(IDagDataContext context) {
        String nodeId = getCurrentNodeId(context);
        log.debug("[{}] 节点逻辑初始化: {}", nodeId, nodeName);
        return new HashMap<>();
    }

    /**
     * 默认完成处理 - 直接返回执行结果
     */
    @Override
    public Map<String, Object> finish(IDagDataContext context, Map<String, Object> executeResult) {
        String nodeId = getCurrentNodeId(context);
        log.debug("[{}] 节点逻辑完成: {}", nodeId, nodeName);
        return executeResult;
    }
} 