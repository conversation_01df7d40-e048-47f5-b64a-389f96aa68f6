package com.mlc.ai.prompt.store.dag.handler;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.ClarificationQuestionNode;
import com.mlc.ai.prompt.store.dag.node.ClarificationAnswerNode;
import com.mlc.ai.prompt.store.dag.node.OutsideBorderNode;
import com.mlc.ai.prompt.store.dag.node.IdentifyAtomicIntentNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 澄清处理器测试类
 * 测试新的顺序流向语义：OutsideBorderNode -> ClarificationQuestionNode -> ClarificationAnswerNode -> IdentifyAtomicIntentNode
 */
public class ClarificationHandlerTest {

    private DagGraph dagGraph;
    private ClarificationHandler clarificationHandler;

    @BeforeEach
    void setUp() {
        dagGraph = new DagGraph();
        clarificationHandler = new ClarificationHandler(dagGraph);
    }

    @Test
    @DisplayName("测试完整的澄清流向：OutsideBorderNode -> ClarificationQuestionNode -> ClarificationAnswerNode -> IdentifyAtomicIntentNode")
    void testCompleteClarificationFlow() {
        // 1. 创建OutsideBorderNode作为前置节点
        OutsideBorderNode borderNode = new OutsideBorderNode("用户请求边界", null);
        borderNode.setStatus(NodeStatus.RESOLVED);
        dagGraph.addNode(borderNode);

        // 2. 从边界节点创建澄清问题
        List<String> recommendations = Arrays.asList("MySQL", "PostgreSQL", "MongoDB");
        ClarificationQuestionNode questionNode = clarificationHandler.createClarificationQuestion(
            borderNode.getId(),
            "请选择您希望使用的数据库类型？",
            recommendations
        );

        assertNotNull(questionNode);
        assertEquals(NodeStatus.CLARIFY_PENDING, questionNode.getStatus());

        // 3. 验证前置节点关系
        IDagNode previousNode = clarificationHandler.getPreviousNode(questionNode);
        assertEquals(borderNode.getId(), previousNode.getId());

        // 4. 用户提供回答，创建回答节点
        String userResponse = "MySQL";
        ClarificationAnswerNode answerNode = clarificationHandler.resolveClarification(
            questionNode.getId(),
            userResponse
        );

        assertNotNull(answerNode);
        assertEquals(userResponse, answerNode.getAnswerContent());
        assertEquals(NodeStatus.RESOLVED, answerNode.getStatus());

        // 5. 验证问题节点状态已更新
        assertEquals(NodeStatus.RESOLVED, questionNode.getStatus());

        // 6. 验证问题-回答关系
        ClarificationQuestionNode foundQuestion = clarificationHandler.getQuestionFromAnswer(answerNode);
        assertEquals(questionNode.getId(), foundQuestion.getId());

        // 7. 创建IdentifyAtomicIntentNode并连接
        IdentifyAtomicIntentNode intentNode = new IdentifyAtomicIntentNode();
        dagGraph.addNode(intentNode);

        boolean connected = clarificationHandler.connectAnswerToNextNode(answerNode.getId(), intentNode.getId());
        assertTrue(connected);

        // 8. 验证完整流向
        IDagNode nextNode = clarificationHandler.getNextNodeFromAnswer(answerNode);
        assertEquals(intentNode.getId(), nextNode.getId());

        // 9. 验证澄清上下文
        ClarificationHandler.ClarificationContext context = clarificationHandler.getClarificationContext(answerNode);
        assertNotNull(context);
        assertEquals("请选择您希望使用的数据库类型？", context.question());
        assertEquals("MySQL", context.answer());
    }

    @Test
    @DisplayName("测试多轮澄清的顺序流向")
    void testMultipleSequentialClarifications() {
        // 创建初始边界节点
        OutsideBorderNode borderNode = new OutsideBorderNode("复杂请求边界", null);
        dagGraph.addNode(borderNode);

        // 第一轮澄清
        ClarificationQuestionNode question1 = clarificationHandler.createClarificationQuestion(
            borderNode.getId(),
            "请选择部署环境？",
            Arrays.asList("开发环境", "测试环境", "生产环境")
        );

        ClarificationAnswerNode answer1 = clarificationHandler.resolveClarification(
            question1.getId(),
            "生产环境"
        );

        // 第二轮澄清（从第一个回答节点继续）
        ClarificationQuestionNode question2 = clarificationHandler.createClarificationQuestion(
            answer1.getId(),
            "请选择服务器规格？",
            Arrays.asList("小型", "中型", "大型")
        );

        ClarificationAnswerNode answer2 = clarificationHandler.resolveClarification(
            question2.getId(),
            "大型"
        );

        // 验证流向链
        assertEquals(borderNode.getId(), clarificationHandler.getPreviousNode(question1).getId());
        assertEquals(answer1.getId(), clarificationHandler.getPreviousNode(question2).getId());

        // 验证所有澄清上下文
        List<ClarificationHandler.ClarificationContext> allContexts = clarificationHandler.getAllClarificationContexts();
        assertEquals(2, allContexts.size());
    }

    @Test
    @DisplayName("测试澄清上下文的获取")
    void testClarificationContextRetrieval() {
        // 创建边界节点
        OutsideBorderNode borderNode = new OutsideBorderNode("测试边界", null);
        dagGraph.addNode(borderNode);

        // 创建澄清问题和回答
        ClarificationQuestionNode questionNode = clarificationHandler.createClarificationQuestion(
            borderNode.getId(),
            "请确认操作类型？",
            Arrays.asList("创建", "更新", "删除")
        );

        // 设置对齐焦点
        questionNode.setAlignmentFocus("operation_type");

        ClarificationAnswerNode answerNode = clarificationHandler.resolveClarification(
            questionNode.getId(),
            "创建"
        );

        // 获取澄清上下文
        ClarificationHandler.ClarificationContext context = clarificationHandler.getClarificationContext(answerNode);
        
        assertNotNull(context);
        assertEquals("请确认操作类型？", context.question());
        assertEquals("创建", context.answer());
        assertEquals("operation_type", context.alignmentFocus());
        assertEquals(3, context.potentialRecommendations().size());
        assertTrue(context.potentialRecommendations().contains("创建"));
    }

    @Test
    @DisplayName("测试连接到意图识别节点")
    void testConnectionToIntentNode() {
        // 创建完整流程
        OutsideBorderNode borderNode = new OutsideBorderNode("边界节点", null);
        dagGraph.addNode(borderNode);

        ClarificationQuestionNode questionNode = clarificationHandler.createClarificationQuestion(
            borderNode.getId(),
            "请选择操作对象？",
            null
        );

        ClarificationAnswerNode answerNode = clarificationHandler.resolveClarification(
            questionNode.getId(),
            "用户表"
        );

        // 创建意图识别节点
        IdentifyAtomicIntentNode intentNode = new IdentifyAtomicIntentNode();
        dagGraph.addNode(intentNode);

        // 连接回答节点到意图节点
        boolean connected = clarificationHandler.connectAnswerToNextNode(answerNode.getId(), intentNode.getId());
        assertTrue(connected);

        // 验证连接
        IDagNode nextNode = clarificationHandler.getNextNodeFromAnswer(answerNode);
        assertEquals(intentNode.getId(), nextNode.getId());

        // 验证意图节点可以获取澄清上下文
        ClarificationHandler.ClarificationContext context = clarificationHandler.getClarificationContext(answerNode);
        assertNotNull(context);
        // 意图识别节点可以使用这个上下文进行更准确的意图识别
    }

    @Test
    @DisplayName("测试查询功能")
    void testQueryFunctions() {
        // 创建多个澄清场景
        OutsideBorderNode border1 = new OutsideBorderNode("边界1", null);
        OutsideBorderNode border2 = new OutsideBorderNode("边界2", null);
        dagGraph.addNode(border1);
        dagGraph.addNode(border2);

        // 创建问题和回答
        ClarificationQuestionNode q1 = clarificationHandler.createClarificationQuestion(border1.getId(), "问题1", null);
        ClarificationQuestionNode q2 = clarificationHandler.createClarificationQuestion(border2.getId(), "问题2", null);

        ClarificationAnswerNode a1 = clarificationHandler.resolveClarification(q1.getId(), "回答1");
        ClarificationAnswerNode a2 = clarificationHandler.resolveClarification(q2.getId(), "回答2");

        // 测试查询功能
        assertTrue(clarificationHandler.isQuestionAnswered(q1.getId()));
        assertTrue(clarificationHandler.isQuestionAnswered(q2.getId()));

        ClarificationAnswerNode foundAnswer = clarificationHandler.findAnswerForQuestion(q1.getId());
        assertEquals(a1.getId(), foundAnswer.getId());

        List<ClarificationAnswerNode> allAnswers = clarificationHandler.findAllResolvedAnswers();
        assertEquals(2, allAnswers.size());

        List<ClarificationHandler.ClarificationContext> allContexts = clarificationHandler.getAllClarificationContexts();
        assertEquals(2, allContexts.size());
    }
} 