package com.mlc.ai.prompt.store.run;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.propagation.DagProviderSpreadPropagator;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.handler.AssumptionHandler;
import com.mlc.ai.prompt.store.dag.handler.ClarificationHandler;
import com.mlc.ai.prompt.store.dag.handler.ConflictHandler;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.*;
import com.mlc.ai.prompt.store.dag.execution.TaskExecutionManager;
import lombok.extern.slf4j.Slf4j;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictType;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DAG示例运行器
 * 本类展示了Plan-and-Solve Agent的DAG实现流程示例
 * 通过有向无环图处理用户请求、任务分解、依赖管理、冲突处理等
 * 
 * 注意：本示例采用提供者->消费者(Provider->Consumer)模式定义边关系
 * Parameter/Provider --PROVIDES_FOR--> Task/Consumer
 */
@Slf4j
public class DagExampleRunner {

    /**
     *
     * 关键节点需要手动设置状态,必须手动设置的节点类型：
     *  1.用户请求节点 (USER_RAW_REQUEST)
     *      原因：作为DAG的起始节点，需要明确标记为已解析状态
     *      示例：dagGraph.updateNodeStatus(userRequest.getId(), NodeStatus.RESOLVED)
     * 2.任务执行状态变更
     *      原因：任务的执行进度需要外部执行器手动更新
     *      示例：从 READY_FOR_EXECUTION → EXECUTION_IN_PROGRESS → COMPLETED
     * 3.澄清回答节点
     *      原因：用户的澄清回答需要手动输入和确认
     *      示例：用户回答"字段A的类型是数字"后，需要手动设置回答节点状态
     * 4.假设验证结果
     *      原因：假设的验证结果需要外部逻辑判断
     *      示例：验证字段名称是否符合命名规范
     * 5.外部边界节点
     *       原因：代表外部系统输入，需要手动确认其状态
     * 为什么必须手动设置：
     *      外部输入依赖：依赖于外部系统或用户输入
     *      业务逻辑判断：需要复杂的业务逻辑来确定状态
     *      时序控制：需要精确控制状态变更的时机
     *      数据验证：需要验证外部数据的有效性
     */
    public static void main(String[] args) {
        System.out.println("=============== Plan-and-Solve Agent DAG 示例运行器 ===============");
        System.out.println("开始运行...");

        // ==================== 1. DAG系统初始化 ====================
        System.out.println("\n>>> 1. 系统初始化");
        // 创建核心组件
        DagGraph dagGraph = new DagGraph();
        // 状态传播器 - 使用优化版本
        DagProviderSpreadPropagator propagator = new DagProviderSpreadPropagator();
        propagator.initialize(dagGraph);

        // 创建辅助处理组件
        AssumptionHandler assumptionHandler = new AssumptionHandler(dagGraph);  // 假设处理器
        ConflictHandler conflictResolver = new ConflictHandler(dagGraph);     // 冲突解决器
        ClarificationHandler clarificationHandler = new ClarificationHandler(dagGraph); // 澄清处理器
        TaskExecutionManager taskExecutionManager = new TaskExecutionManager(dagGraph); // 任务执行管理器

        // ==================== 2. 用户请求解析与核心概念提取 ====================
        System.out.println("\n>>> 2. 用户请求解析与核心概念提取");
        
        // 用户原始请求
        String userInput = "我正在处理'车辆管理系统模型'的实体设计，添加客户表，增加字段A和字段B的统计和，请帮我设计相关模型。";
        System.out.println("用户输入: " + userInput);
        
        // 创建原始请求节点
        UserRawRequestNode rawRequestNode = new UserRawRequestNode(userInput);
        dagGraph.addNode(rawRequestNode);
        dagGraph.updateNodeStatus(rawRequestNode.getId(), NodeStatus.RESOLVED);
        
        // 提取核心领域概念: 车辆管理系统模型
        DomainCoordinateNode coordinateModel = new DomainCoordinateNode("车辆管理系统模型", "'车辆管理系统模型'");
        coordinateModel.setDomainCoordinates(Collections.singletonMap("coreElement", "ORM"));
        dagGraph.addNode(coordinateModel);
        dagGraph.updateNodeStatus(coordinateModel.getId(), NodeStatus.RESOLVED);
        dagGraph.addEdge(rawRequestNode, coordinateModel, EdgeType.PROVIDES);
        System.out.println("核心领域: 车辆管理系统模型 (ORM)");

        // 提取概念: 客户表(实体)
        Map<String, String> customerTableDomainCoords = new HashMap<>();
        customerTableDomainCoords.put("coreElement", "Entity");
        customerTableDomainCoords.put("weight", "100%");
        DomainCoordinateNode conceptCustomerTable = new DomainCoordinateNode("客户表", "客户表");
        conceptCustomerTable.setDomainCoordinates(customerTableDomainCoords);
        dagGraph.addNode(conceptCustomerTable);
        dagGraph.updateNodeStatus(conceptCustomerTable.getId(), NodeStatus.RESOLVED);
        dagGraph.addEdge(coordinateModel, conceptCustomerTable, EdgeType.PROVIDES);
        System.out.println("识别实体: 客户表");

        // 提取概念: 字段A和字段B (需要澄清类型的字段)
        Map<String, String> fieldDomainCoords = new HashMap<>();
        fieldDomainCoords.put("coreElement", "Entity.Columns");
        fieldDomainCoords.put("weight", "100%");
        
        // 字段A
        DomainCoordinateNode conceptFieldA = new DomainCoordinateNode("字段A", "字段A");
        conceptFieldA.setDomainCoordinates(fieldDomainCoords);
        dagGraph.addNode(conceptFieldA);
        dagGraph.updateNodeStatus(conceptFieldA.getId(), NodeStatus.REQUIRES_CLARIFY); // 标记为需要澄清
        dagGraph.addEdge(rawRequestNode, conceptFieldA, EdgeType.PROVIDES);
        
        // 字段B
        DomainCoordinateNode conceptFieldB = new DomainCoordinateNode("字段B", "字段B");
        conceptFieldB.setDomainCoordinates(fieldDomainCoords); 
        dagGraph.addNode(conceptFieldB);
        dagGraph.updateNodeStatus(conceptFieldB.getId(), NodeStatus.REQUIRES_CLARIFY); // 标记为需要澄清
        dagGraph.addEdge(rawRequestNode, conceptFieldB, EdgeType.PROVIDES);
        System.out.println("识别字段: 字段A, 字段B (均需澄清类型)");

        // 提取概念: 统计和(功能)
        Map<String, String> sumDomainCoords = new HashMap<>();
        sumDomainCoords.put("coreElement", "Entity.Computes");
        sumDomainCoords.put("secondaryAxis", "功能增强");
        sumDomainCoords.put("weight", "30%");
        DomainCoordinateNode conceptSum = new DomainCoordinateNode("统计和", "统计和");
        conceptSum.setDomainCoordinates(sumDomainCoords);
        dagGraph.addNode(conceptSum);
        dagGraph.updateNodeStatus(conceptSum.getId(), NodeStatus.RESOLVED);
        dagGraph.addEdge(rawRequestNode, conceptSum, EdgeType.PROVIDES);
        System.out.println("识别功能: 统计和(Entity.Computes)");

        // ==================== 3. 意图识别与参数构建 ====================
        System.out.println("\n>>> 3. 意图识别与参数构建");
        
        // 创建意图节点
        IdentifyAtomicIntentNode intentCreateEntity = new IdentifyAtomicIntentNode();
        dagGraph.addNode(intentCreateEntity);
        
        // 创建计算字段意图
        IdentifyAtomicIntentNode intentCreateComputedField = new IdentifyAtomicIntentNode();
        dagGraph.addNode(intentCreateComputedField);
        
        // 参数构建: 实体名称(已解析)
        ParameterNode paramEntityName = new ParameterNode("entityName", "String", true);
        paramEntityName.setValue("客户表", ParameterNode.ValueSource.DERIVATION);
        dagGraph.addNode(paramEntityName);
        System.out.println("参数: 实体名称 = '客户表' (已解析，状态: " + paramEntityName.getStatus() + ")");

        // 参数构建: 计算字段名称(通过假设流程)
        ParameterNode paramComputedFieldName = new ParameterNode("computedFieldName", "String", true);
        dagGraph.addNode(paramComputedFieldName); 

        // 使用新的假设流程：创建假设节点
        AssumptionNode computedFieldNameAssumption = assumptionHandler.createAssumptionInFlow(
                intentCreateComputedField.getId(), 
                "假设计算字段名称为'sum_A_B'，基于计算公式和字段名", 
                "sum_A_B"
        );

        AssumptionValidationNode validationNode = null;
        if (computedFieldNameAssumption != null) {
            // 创建假设验证节点
            validationNode = assumptionHandler.createValidationNode(
                computedFieldNameAssumption.getId(),
                "验证计算字段名称假设的合理性"
            );
            
            if (validationNode != null) {
                // 连接验证节点到参数节点
                assumptionHandler.connectValidationToNextNode(validationNode.getId(), paramComputedFieldName.getId());
            }
        }

        System.out.println("参数: 计算字段名称 = 'sum_A_B' (通过假设流程)");

        // 参数构建: 计算源字段1(依赖字段A的澄清)
        ParameterNode paramFieldAForSum = new ParameterNode("sourceField1", "Entity.Column", true);
        dagGraph.addNode(paramFieldAForSum);
        dagGraph.addEdge(conceptFieldA, paramFieldAForSum, EdgeType.PROVIDES);
        System.out.println("参数: 计算源字段1 (等待字段A澄清，状态: " + paramFieldAForSum.getStatus() + ")");

        // 参数构建: 计算源字段2(依赖字段B的澄清)
        ParameterNode paramFieldBForSum = new ParameterNode("sourceField2", "Entity.Column", true);
        dagGraph.addNode(paramFieldBForSum);
        dagGraph.addEdge(conceptFieldB, paramFieldBForSum, EdgeType.PROVIDES);
        System.out.println("参数: 计算源字段2 (等待字段B澄清，状态: " + paramFieldBForSum.getStatus() + ")");
        
        // 参数构建: 计算公式(已解析)
        ParameterNode paramFormula = new ParameterNode("formula", "String", true);
        paramFormula.setValue("字段A + 字段B", ParameterNode.ValueSource.DERIVATION);
        dagGraph.addNode(paramFormula);
        System.out.println("参数: 计算公式 = '字段A + 字段B' (已解析，状态: " + paramFormula.getStatus() + ")");

        // ==================== 4. 任务定义与依赖设置 ====================
        System.out.println("\n>>> 4. 任务定义与依赖设置");
        
        // 创建结构化子任务
        StructuredTaskNode taskCreateCustomerTable = new StructuredTaskNode(
            "创建客户表，包含ID、姓名、邮箱字段"
        );
        dagGraph.addNode(taskCreateCustomerTable);
        // 添加参数依赖边
        dagGraph.addEdge(paramEntityName, taskCreateCustomerTable, EdgeType.PROVIDES);
        System.out.println("任务1: 创建客户表 (依赖: 实体名称参数，状态: " + taskCreateCustomerTable.getStatus() + ")");
        
        StructuredTaskNode taskCreateSumField = new StructuredTaskNode(
            "创建计算字段：总和"
        );
        dagGraph.addNode(taskCreateSumField);
        // 添加所有参数依赖边
        dagGraph.addEdge(paramComputedFieldName, taskCreateSumField, EdgeType.PROVIDES);
        dagGraph.addEdge(paramFieldAForSum, taskCreateSumField, EdgeType.PROVIDES);
        dagGraph.addEdge(paramFieldBForSum, taskCreateSumField, EdgeType.PROVIDES);
        dagGraph.addEdge(paramFormula, taskCreateSumField, EdgeType.PROVIDES);
        System.out.println("任务2: 创建计算字段 (依赖: 字段名、字段A、字段B、公式参数，状态: " + taskCreateSumField.getStatus() + ")");

        // ==================== 5. 澄清处理阶段 ====================
        System.out.println("\n>>> 5. 澄清处理阶段");

        // 重要：澄清应该是流程的一部分，而不是为特定节点提供补充信息
        // 正确的流向：边界节点 -> 澄清问题 -> 澄清回答 -> 意图识别

        System.out.println("\n5.1 创建澄清流程处理字段类型");

        // 创建一个专门的边界节点来处理字段类型澄清
        OutsideBorderNode fieldTypeBorderNode = new OutsideBorderNode("字段类型澄清边界", null);
        dagGraph.addNode(fieldTypeBorderNode);
        dagGraph.updateNodeStatus(fieldTypeBorderNode.getId(), NodeStatus.RESOLVED);
        
        // 第一个澄清：字段A的类型
        ClarificationQuestionNode qNodeForFieldA = clarificationHandler.createClarificationQuestion(
            fieldTypeBorderNode.getId(), 
            "字段A的数据类型是什么 (例如: 数字, 文本)?", 
            Arrays.asList("数字", "文本")
        );

        if (qNodeForFieldA != null) {
            System.out.println("系统生成澄清问题: " + qNodeForFieldA.getQuestion());
            System.out.println("可选回答: " + qNodeForFieldA.getPotentialRecommendations());
            
            // 模拟用户回答"数字"
            String userAnswerA = "数字";
            System.out.println("用户回答: " + userAnswerA);
            
            // 处理回答
            ClarificationAnswerNode answerNodeA = clarificationHandler.resolveClarification(qNodeForFieldA.getId(), userAnswerA);
            
            if (answerNodeA != null) {
                // 第二个澄清：字段B的类型（从第一个回答继续）
                ClarificationQuestionNode qNodeForFieldB = clarificationHandler.createClarificationQuestion(
                    answerNodeA.getId(), 
                    "字段B的数据类型是什么 (例如: 数字, 文本)?", 
                    Arrays.asList("数字", "文本")
                );
                
                if (qNodeForFieldB != null) {
                    System.out.println("系统生成第二个澄清问题: " + qNodeForFieldB.getQuestion());
                    
                    // 模拟用户回答"文本"
                    String userAnswerB = "文本";
                    System.out.println("用户回答: " + userAnswerB);
                    
                    ClarificationAnswerNode answerNodeB = clarificationHandler.resolveClarification(qNodeForFieldB.getId(), userAnswerB);
                    
                    if (answerNodeB != null) {
                        // 创建字段类型处理节点来接收澄清结果
                        IdentifyAtomicIntentNode fieldTypeIntentNode = new IdentifyAtomicIntentNode();
                        dagGraph.addNode(fieldTypeIntentNode);
                        
                        // 连接最后的澄清回答到字段类型处理节点
                        clarificationHandler.connectAnswerToNextNode(answerNodeB.getId(), fieldTypeIntentNode.getId());
                        
                        // 获取所有澄清上下文用于后续处理
                        List<ClarificationHandler.ClarificationContext> clarificationContexts = 
                            clarificationHandler.getAllClarificationContexts();
                        
                        System.out.println("✅ 澄清流程完成，获得 " + clarificationContexts.size() + " 个澄清上下文:");
                        for (ClarificationHandler.ClarificationContext context : clarificationContexts) {
                            System.out.println("- " + context.toString());
                        }
                        
                        // 基于澄清上下文更新相关参数（这应该由意图处理节点自动完成）
                        // 注意：这里仍然需要手动更新是因为我们还没有实现完整的意图处理逻辑
                        updateParametersBasedOnClarification(clarificationContexts, paramFieldAForSum, paramFieldBForSum);
                        
                        dagGraph.updateNodeStatus(fieldTypeIntentNode.getId(), NodeStatus.RESOLVED);
                    }
                }
            }
        } else {
            System.out.println("❌ 创建字段A的澄清问题失败");
        }

        // ==================== 6. 假设验证阶段 ====================
        System.out.println("\n>>> 6. 假设验证阶段");

        if (validationNode != null) {
            System.out.println("验证计算字段名称假设...");
            // 使用新的假设验证方法
            assumptionHandler.processValidationResult(validationNode.getId(), true, "字段名称符合命名规范");
            
            // 基于验证结果更新参数
            paramComputedFieldName.setValue("sum_A_B", ParameterNode.ValueSource.ASSUMED);
            dagGraph.updateNodeStatus(paramComputedFieldName.getId(), NodeStatus.RESOLVED);
            
            System.out.println("✅ 假设已验证为有效: " + computedFieldNameAssumption.getAssumptionDescription());
            System.out.println("计算字段名称参数已更新为: " + paramComputedFieldName.getValue());
        } else {
            System.out.println("❌ 假设验证节点未创建");
        }

        // ==================== 7. 参数值自动传播 ====================
        propagateParameterValues(dagGraph, assumptionHandler, clarificationHandler);

        // ==================== 8. 主要任务执行阶段 ====================
        System.out.println("\n>>> 8. 主要任务执行阶段");
        
        List<StructuredTaskNode> executedTasksMain = taskExecutionManager.executeAllReadyTasks();
        if (!executedTasksMain.isEmpty()) {
            System.out.println("✅ 成功执行 " + executedTasksMain.size() + " 个任务:");
            for (StructuredTaskNode task : executedTasksMain) {
                System.out.println("- " + task.getTaskDescription() + " (ID: " + task.getId() + ")");
                System.out.println("  状态: " + task.getStatus());
                System.out.println("  执行结果: " + task.getExecutionTasks());
            }
        } else {
            System.out.println("❌ 当前没有准备好执行的任务");
        }

        // ==================== 9. 冲突处理阶段 ====================
        System.out.println("\n>>> 9. 冲突模拟与解决");
        
        // 模拟冲突: 创建与现有客户表概念冲突的新概念(定义为视图而非实体)
        System.out.println("模拟冲突: 添加冲突的客户表定义(视图而非实体)");
        DomainCoordinateNode conflictingConcept = new DomainCoordinateNode("客户表_alt_definition", "客户表");
        dagGraph.addNode(conflictingConcept);
        dagGraph.updateNodeStatus(conflictingConcept.getId(), NodeStatus.RESOLVED);

        IdentifyAtomicIntentNode conflictingIntent = new IdentifyAtomicIntentNode();
        dagGraph.addNode(conflictingIntent);
        dagGraph.updateNodeStatus(conflictingIntent.getId(), NodeStatus.RESOLVED);
        dagGraph.addEdge(conflictingConcept, conflictingIntent, EdgeType.PROVIDES);

        // 检测和处理冲突
        if (conceptCustomerTable.getOriginalText().equals(conflictingConcept.getOriginalText())
            ) { // && !intentCreateEntity.getIntentName().equals(conflictingIntent.getIntentName()

            String conflictDescription = "意图冲突：CREATE_ENTITY vs DEFINE_VIEW";
            // 检测冲突 - 使用新的冲突约束管理器
            boolean conflictDetected = conflictResolver.detectAndMarkConflict(
                intentCreateEntity,
                conflictingIntent,
                conflictDescription,
                ConflictType.SEMANTIC_CONFLICT
            );

            if (conflictDetected) {
                System.out.println("✅ 检测到冲突: " + conflictDescription);

                // 创建冲突解决流程
                ConflictResolutionNode conflictResolution = conflictResolver.createConflictResolutionInFlow(
                    intentCreateEntity.getId(),
                    conflictDescription,
                    Arrays.asList(intentCreateEntity.getId(), conflictingIntent.getId())
                );

                if (conflictResolution != null) {

                    // 模拟冲突解决过程
                    System.out.println("用户选择: 实体");
                    conflictResolver.processResolutionResult(conflictResolution.getId(), "用户选择", "选择实体定义");

                    // 不要连接回原来的意图节点，这会形成循环
                    // 冲突解决节点本身就是流程的终点，表示冲突已解决
                    System.out.println("✅ 冲突已解决，选择了'实体'定义");
                } else {
                    System.out.println("❌ 未能创建冲突解决节点");
                }
            }
        }

        // ==================== 10. 最终状态检查 ====================
        System.out.println("\n>>> 10. 最终状态检查");
        
        // 检查所有任务完成状态
        boolean allTasksCompleted = true;
        System.out.println("检查所有任务状态:");
        for (IDagNode node : dagGraph.getAllNodes()) {
            if (node instanceof StructuredTaskNode taskNode) {
                if (taskNode.getStatus() != NodeStatus.COMPLETED && taskNode.getStatus() != NodeStatus.FAILED) {
                    allTasksCompleted = false;
                    System.out.println("❌ 任务「" + taskNode.getTaskDescription() + "」未完成，当前状态: " + taskNode.getStatus());
                } else {
                    System.out.println("✅ 任务「" + taskNode.getTaskDescription() + "」已完成，状态: " + taskNode.getStatus());
                }
            }
        }
        
        if (allTasksCompleted) {
            System.out.println("\n 所有任务已完成!");
        } else {
            System.out.println("\n 部分任务未完成");
        }

        // 检查未解决的冲突
        List<String> unresolvedConflicts = conflictResolver.findUnresolvedConflicts();
        if (unresolvedConflicts.isEmpty()) {
            System.out.println("没有未解决的冲突");
        } else {
            System.out.println("存在 " + unresolvedConflicts.size() + " 个未解决的冲突:");
            for (String conflict : unresolvedConflicts) {
                System.out.println("- " + conflict);
            }
        }
        
        System.out.println("\n=============== 示例运行完成 ===============");
    }


    /**
     * 基于澄清上下文更新参数
     * 注意：这是临时方法，理想情况下应该由意图处理节点自动完成
     */
    private static void updateParametersBasedOnClarification(
            List<ClarificationHandler.ClarificationContext> clarificationContexts,
            ParameterNode paramFieldAForSum, 
            ParameterNode paramFieldBForSum) {
        
        for (ClarificationHandler.ClarificationContext context : clarificationContexts) {
            if (context.question().contains("字段A")) {
                paramFieldAForSum.setAttachProp("resolvedValue", "field_A_column_id");
                paramFieldAForSum.setAttachProp("dataType", context.answer());
                paramFieldAForSum.setValue("field_A_column_id", ParameterNode.ValueSource.CLARIFIED);
                System.out.println("✅ 字段A类型已澄清为'" + context.answer() + "'，参数已更新");
            } else if (context.question().contains("字段B")) {
                paramFieldBForSum.setAttachProp("resolvedValue", "field_B_column_id");
                paramFieldBForSum.setAttachProp("dataType", context.answer());
                paramFieldBForSum.setValue("field_B_column_id", ParameterNode.ValueSource.CLARIFIED);
                System.out.println("✅ 字段B类型已澄清为'" + context.answer() + "'，参数已更新");
            }
        }
    }
    
    /**
     * 参数值自动传播器
     * 根据假设验证和澄清结果自动更新相关参数的值
     */
    private static void propagateParameterValues(DagGraph dagGraph, 
        AssumptionHandler assumptionHandler, ClarificationHandler clarificationHandler) {
        System.out.println("\n>>> 参数值自动传播");
        
        // 1. 传播假设验证结果到参数
        List<AssumptionValidationNode> validatedAssumptions = assumptionHandler.findAllResolvedValidations();
        for (AssumptionValidationNode validationNode : validatedAssumptions) {
            if (validationNode.isValid()) {
                AssumptionNode assumptionNode = assumptionHandler.getAssumptionFromValidation(validationNode);
                if (assumptionNode != null) {
                    Object assumedValue = assumptionNode.getAttachProp("assumedValue");
                    
                    // 查找连接到此验证节点的参数节点
                    IDagNode nextNode = assumptionHandler.getNextNodeFromValidation(validationNode);
                    if (nextNode instanceof ParameterNode paramNode) {
                        paramNode.setValue(assumedValue, ParameterNode.ValueSource.ASSUMED);
                        dagGraph.updateNodeStatus(paramNode.getId(), NodeStatus.RESOLVED);
                        System.out.println("✅ 参数 " + paramNode.getParameterName() + " 通过假设验证获得值: " + assumedValue);
                    }
                }
            }
        }
        
        // 2. 传播澄清结果到参数
        List<ClarificationHandler.ClarificationContext> clarificationContexts = 
            clarificationHandler.getAllClarificationContexts();
        
        for (ClarificationHandler.ClarificationContext context : clarificationContexts) {
            // 根据澄清内容更新相关参数
            for (IDagNode node : dagGraph.getAllNodes()) {
                if (node instanceof ParameterNode paramNode) {
                    // 根据参数名称和澄清内容匹配
                    if (shouldUpdateParameterFromClarification(paramNode, context)) {
                        updateParameterFromClarification(paramNode, context, dagGraph);
                    }
                }
            }
        }
        
        // 3. 强制检查所有节点状态，确保状态传播完整
        System.out.println("强制检查所有节点状态，确保任务节点正确更新...");
        // 获取传播器实例并强制检查
//        propagator.forceCheckAllNodes();
    }
    
    /**
     * 判断参数是否应该根据澄清结果更新
     */
    private static boolean shouldUpdateParameterFromClarification(ParameterNode paramNode, 
                                                                ClarificationHandler.ClarificationContext context) {
        String paramName = paramNode.getParameterName();
        String question = context.question();
        
        // 字段A相关的参数
        if (paramName.contains("sourceField1") && question.contains("字段A")) {
            return true;
        }
        // 字段B相关的参数
        if (paramName.contains("sourceField2") && question.contains("字段B")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 根据澄清结果更新参数
     */
    private static void updateParameterFromClarification(ParameterNode paramNode, 
                                                       ClarificationHandler.ClarificationContext context,
                                                       DagGraph dagGraph) {
        String answer = context.answer();
        String fieldId = null;
        
        if (context.question().contains("字段A")) {
            fieldId = "field_A_column_id";
        } else if (context.question().contains("字段B")) {
            fieldId = "field_B_column_id";
        }
        
        if (fieldId != null) {
            paramNode.setAttachProp("dataType", answer);
            paramNode.setValue(fieldId, ParameterNode.ValueSource.CLARIFIED);
            dagGraph.updateNodeStatus(paramNode.getId(), NodeStatus.RESOLVED);
            System.out.println("✅ 参数 " + paramNode.getParameterName() + " 通过澄清获得值: " + fieldId + " (类型: " + answer + ")");
        }
    }
}

