package com.mlc.ai.prompt.store.run;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.propagation.DagProviderSpreadPropagator;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.*;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;

/**
 * 特殊状态传播测试
 * 演示如何触发handleSpecialStatusPropagation中的各种情况
 */
public class SpecialStatusPropagationTest {

    public static void main(String[] args) {
        System.out.println("=============== 特殊状态传播测试 ===============");
        
        // 初始化DAG系统
        DagGraph dagGraph = new DagGraph();
        DagProviderSpreadPropagator propagator = new DagProviderSpreadPropagator();
        propagator.initialize(dagGraph);
        
        System.out.println(">>> 测试1: ASSUMPTION_MADE状态传播");
        testAssumptionMadePropagation(dagGraph);
        
        System.out.println("\n>>> 测试2: FAILED状态传播");
        testFailedPropagation(dagGraph);
        
        System.out.println("\n>>> 测试3: CONFLICTED状态传播");
        testConflictedPropagation(dagGraph);
        
        System.out.println("\n>>> 测试4: REQUIRES_CLARIFY状态传播");
        testRequiresClarifyPropagation(dagGraph);
        
        System.out.println("\n>>> 测试5: ASSUMPTION_INVALIDATED状态传播");
        testAssumptionInvalidatedPropagation(dagGraph);
        
        // 清理资源
        propagator.shutdown();
        
        System.out.println("\n=============== 测试完成 ===============");
    }
    
    private static void testAssumptionMadePropagation(DagGraph dagGraph) {
        // 创建假设节点
        AssumptionNode assumption = new AssumptionNode("测试假设");
        dagGraph.addNode(assumption);
        
        // 创建依赖参数节点
        ParameterNode param = new ParameterNode("testParam", "String", true);
        dagGraph.addNode(param);
        dagGraph.addEdge(assumption, param, EdgeType.PROVIDES);
        
        System.out.println("参数节点初始状态: " + param.getStatus());
        
        // 触发ASSUMPTION_MADE状态
        dagGraph.updateNodeStatus(assumption.getId(), NodeStatus.ASSUMPTION_MADE);
        
        System.out.println("假设节点状态变为ASSUMPTION_MADE后，参数节点状态: " + param.getStatus());
    }
    
    private static void testFailedPropagation(DagGraph dagGraph) {
        // 创建参数节点
        ParameterNode failedParam = new ParameterNode("failedParam", "String", true);
        dagGraph.addNode(failedParam);
        
        // 创建依赖任务节点
        StructuredTaskNode task = new StructuredTaskNode("测试任务");
        dagGraph.addNode(task);
        dagGraph.addEdge(failedParam, task, EdgeType.PROVIDES);
        
        System.out.println("任务节点初始状态: " + task.getStatus());
        
        // 触发FAILED状态
        dagGraph.updateNodeStatus(failedParam.getId(), NodeStatus.FAILED);
        
        System.out.println("参数节点状态变为FAILED后，任务节点状态: " + task.getStatus());
    }
    
    private static void testConflictedPropagation(DagGraph dagGraph) {
        // 创建概念节点
        DomainCoordinateNode concept = new DomainCoordinateNode("冲突概念", "冲突概念");
        dagGraph.addNode(concept);
        
        // 创建依赖任务节点
        StructuredTaskNode task = new StructuredTaskNode("测试任务");
        dagGraph.addNode(task);
        dagGraph.addEdge(concept, task, EdgeType.PROVIDES);
        
        System.out.println("任务节点初始状态: " + task.getStatus());
        
        // 触发CONFLICTED状态
        dagGraph.updateNodeStatus(concept.getId(), NodeStatus.CONFLICTED);
        
        System.out.println("概念节点状态变为CONFLICTED后，任务节点状态: " + task.getStatus());
    }
    
    private static void testRequiresClarifyPropagation(DagGraph dagGraph) {
        // 创建概念节点
        DomainCoordinateNode concept = new DomainCoordinateNode("需澄清概念", "需澄清概念");
        dagGraph.addNode(concept);
        
        // 创建依赖任务节点
        StructuredTaskNode task = new StructuredTaskNode("测试任务");
        dagGraph.addNode(task);
        dagGraph.addEdge(concept, task, EdgeType.PROVIDES);
        
        System.out.println("任务节点初始状态: " + task.getStatus());
        
        // 触发REQUIRES_CLARIFY状态
        dagGraph.updateNodeStatus(concept.getId(), NodeStatus.REQUIRES_CLARIFY);
        
        System.out.println("概念节点状态变为REQUIRES_CLARIFY后，任务节点状态: " + task.getStatus());
    }
    
    private static void testAssumptionInvalidatedPropagation(DagGraph dagGraph) {
        // 创建假设节点
        AssumptionNode assumption = new AssumptionNode("无效假设");
        dagGraph.addNode(assumption);
        
        // 创建依赖参数节点
        ParameterNode param = new ParameterNode("assumptionParam", "String", true);
        dagGraph.addNode(param);
        dagGraph.addEdge(assumption, param, EdgeType.PROVIDES);
        
        // 先设置参数为已解析状态
        dagGraph.updateNodeStatus(param.getId(), NodeStatus.RESOLVED);
        System.out.println("参数节点初始状态: " + param.getStatus());
        
        // 触发ASSUMPTION_INVALIDATED状态
        dagGraph.updateNodeStatus(assumption.getId(), NodeStatus.ASSUMPTION_INVALIDATED);
        
        System.out.println("假设节点状态变为ASSUMPTION_INVALIDATED后，参数节点状态: " + param.getStatus());
    }
} 