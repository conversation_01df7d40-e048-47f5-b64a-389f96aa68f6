package com.mlc.ai.prompt.store.dag.handler;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictConstraint;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictConstraintManager;
import com.mlc.ai.prompt.store.dag.handler.constraint.ConflictType;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.node.DomainCoordinateNode;
import com.mlc.ai.prompt.store.dag.node.IdentifyAtomicIntentNode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 冲突约束管理示例
 * 
 * 演示如何使用新的冲突约束管理机制：
 * 1. 图中有正常的边关系（如 A --PROVIDES--> B）
 * 2. 图的元数据中有冲突约束（如 Conflicts: { (A, C) }）
 * 3. Orchestrator 在调度时先检查边类型条件，再检查冲突约束
 */
@Slf4j
public class ConflictConstraintTest {

    public static void main(String[] args) {
        demonstrateConflictConstraints();
    }

    public static void demonstrateConflictConstraints() {
        log.info("=== 冲突约束管理示例 ===");
        
        // 创建DAG图
        DagGraph dagGraph = new DagGraph();
        ConflictConstraintManager conflictManager = new ConflictConstraintManager(dagGraph);
        
        // 创建节点
        DomainCoordinateNode customerTable = new DomainCoordinateNode("客户表", "客户表");
        dagGraph.addNode(customerTable);
        dagGraph.updateNodeStatus(customerTable.getId(), NodeStatus.RESOLVED);
        
        IdentifyAtomicIntentNode createEntityIntent = new IdentifyAtomicIntentNode();
        dagGraph.addNode(createEntityIntent);
        dagGraph.updateNodeStatus(createEntityIntent.getId(), NodeStatus.RESOLVED);
        
        IdentifyAtomicIntentNode createViewIntent = new IdentifyAtomicIntentNode();
        dagGraph.addNode(createViewIntent);
        dagGraph.updateNodeStatus(createViewIntent.getId(), NodeStatus.RESOLVED);
        
        // 添加正常的边关系
        dagGraph.addEdge(customerTable, createEntityIntent, EdgeType.PROVIDES);
        dagGraph.addEdge(customerTable, createViewIntent, EdgeType.PROVIDES);
        
        log.info("创建了图结构:");
        log.info("  {} --PROVIDES--> {}", customerTable.getId(), createEntityIntent.getId());
        log.info("  {} --PROVIDES--> {}", customerTable.getId(), createViewIntent.getId());
        
        // 添加冲突约束（在元数据中，不是边）
        boolean conflictAdded = conflictManager.addConflictConstraint(
            createEntityIntent,
            createViewIntent,
            "同一概念不能既是实体又是视图",
            ConflictType.SEMANTIC_CONFLICT
        );
        
        log.info("添加冲突约束: {} <-> {}, 成功: {}", 
                createEntityIntent.getId(), createViewIntent.getId(), conflictAdded);
        
        // 演示Orchestrator的调度逻辑
        demonstrateOrchestrationLogic(dagGraph, conflictManager, createEntityIntent, createViewIntent);
        
        // 演示冲突解决
        demonstrateConflictResolution(conflictManager, createEntityIntent, createViewIntent);
        
        log.info("=== 示例结束 ===");
    }
    
    private static void demonstrateOrchestrationLogic(DagGraph dagGraph, ConflictConstraintManager conflictManager,
                                                     IDagNode node1, IDagNode node2) {
        log.info("\n--- Orchestrator 调度逻辑演示 ---");
        
        // 模拟Orchestrator的调度决策过程
        log.info("Orchestrator 准备调度节点: {}", node1.getId());
        
        // 1. 首先检查边类型条件
        boolean edgeConditionsMet = checkEdgeConditions(dagGraph, node1);
        log.info("  边类型条件检查: {}", edgeConditionsMet ? "满足" : "不满足");
        
        // 2. 然后检查冲突约束
        boolean canBeScheduled = conflictManager.canNodeBeScheduled(node1.getId());
        log.info("  冲突约束检查: {}", canBeScheduled ? "无冲突阻止" : "存在冲突阻止");
        
        // 3. 最终调度决策
        boolean shouldSchedule = edgeConditionsMet && canBeScheduled;
        log.info("  最终调度决策: {}", shouldSchedule ? "可以调度" : "不能调度");
        
        if (!shouldSchedule && !canBeScheduled) {
            log.info("  原因: 与节点 {} 存在未解决的冲突", node2.getId());
        }
    }
    
    private static boolean checkEdgeConditions(DagGraph dagGraph, IDagNode node) {
        // 简化的边条件检查：检查所有PROVIDES边的源节点是否就绪
        return dagGraph.getIncomingEdges(node).stream()
                .filter(edge -> edge.getType() == EdgeType.PROVIDES)
                .map(edge -> dagGraph.getGraph().getEdgeSource(edge))
                .allMatch(NodeStatus::isNodeReady);
    }
    
    private static void demonstrateConflictResolution(ConflictConstraintManager conflictManager,
                                                     IDagNode node1, IDagNode node2) {
        log.info("\n--- 冲突解决演示 ---");
        
        // 查看未解决的冲突
        List<ConflictConstraint> unresolvedConflicts = conflictManager.getUnresolvedConflicts();
        log.info("未解决的冲突数量: {}", unresolvedConflicts.size());
        
        for (ConflictConstraint conflict : unresolvedConflicts) {
            log.info("  冲突: {} <-> {}, 类型: {}, 描述: {}", 
                    conflict.getNode1Id(), conflict.getNode2Id(), 
                    conflict.getConflictType(), conflict.getDescription());
        }
        
        // 解决冲突
        boolean resolved = conflictManager.resolveConflict(
            node1.getId(), 
            node2.getId(), 
            "用户选择创建实体而不是视图"
        );
        
        log.info("冲突解决结果: {}", resolved ? "成功" : "失败");
        
        // 再次检查调度条件
        boolean canNode1BeScheduled = conflictManager.canNodeBeScheduled(node1.getId());
        boolean canNode2BeScheduled = conflictManager.canNodeBeScheduled(node2.getId());
        
        log.info("解决冲突后:");
        log.info("  节点 {} 可以调度: {}", node1.getId(), canNode1BeScheduled);
        log.info("  节点 {} 可以调度: {}", node2.getId(), canNode2BeScheduled);
    }

} 