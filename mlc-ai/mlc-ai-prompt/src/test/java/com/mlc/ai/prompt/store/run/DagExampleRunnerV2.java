package com.mlc.ai.prompt.store.run;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.edge.EdgeType;
import com.mlc.ai.prompt.store.dag.handler.AssumptionHandler;
import com.mlc.ai.prompt.store.dag.handler.ClarificationHandler;
import com.mlc.ai.prompt.store.dag.handler.ConflictHandler;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.propagation.DagProviderSpreadPropagator;
import com.mlc.ai.prompt.store.dag.node.*;
import com.mlc.ai.prompt.store.dag.execution.TaskExecutionManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DAG示例运行器 V2
 * 使用重构后的传播器架构
 * 展示了更清晰的职责分离和更好的性能
 */
@Slf4j
public class DagExampleRunnerV2 {

    public static void main(String[] args) {
        System.out.println("=============== Plan-and-Solve Agent DAG 示例运行器 V2 ===============");
        System.out.println("开始运行...");

        // ==================== 1. DAG系统初始化 ====================
        System.out.println("\n>>> 1. 系统初始化 (使用重构后的架构)");
        
        // 创建核心组件
        DagGraph dagGraph = new DagGraph();
        
        // 使用重构后的传播器 - 自动管理所有子组件
        DagProviderSpreadPropagator propagatorV2 = new DagProviderSpreadPropagator();
        propagatorV2.initialize(dagGraph);
        
        // 创建辅助处理组件
        AssumptionHandler assumptionHandler = new AssumptionHandler(dagGraph);
        ConflictHandler conflictResolver = new ConflictHandler(dagGraph);
        ClarificationHandler clarificationHandler = new ClarificationHandler(dagGraph);
        TaskExecutionManager taskExecutionManager = new TaskExecutionManager(dagGraph);
        

        // ==================== 2. 用户请求解析与核心概念提取 ====================
        System.out.println("\n>>> 2. 用户请求解析与核心概念提取");
        
        String userInput = "我正在处理'车辆管理系统模型'的实体设计，添加客户表，增加字段A和字段B的统计和，请帮我设计相关模型。";
        System.out.println("用户输入: " + userInput);
        
        // 创建原始请求节点 - 自动状态设置
        UserRawRequestNode rawRequestNode = new UserRawRequestNode(userInput);
        dagGraph.addNode(rawRequestNode);
        System.out.println("原始请求节点状态: " + rawRequestNode.getStatus());
        
        // 提取核心领域概念
        DomainCoordinateNode coordinateModel = new DomainCoordinateNode("车辆管理系统模型", "'车辆管理系统模型'");
        coordinateModel.setDomainCoordinates(Collections.singletonMap("coreElement", "ORM"));
        dagGraph.addNode(coordinateModel);
        dagGraph.addEdge(rawRequestNode, coordinateModel, EdgeType.PROVIDES);
        System.out.println("核心领域: 车辆管理系统模型 (状态: " + coordinateModel.getStatus() + ")");

        // 提取概念: 客户表(实体)
        Map<String, String> customerTableDomainCoords = new HashMap<>();
        customerTableDomainCoords.put("coreElement", "Entity");
        customerTableDomainCoords.put("weight", "100%");
        DomainCoordinateNode conceptCustomerTable = new DomainCoordinateNode("客户表", "客户表");
        conceptCustomerTable.setDomainCoordinates(customerTableDomainCoords);
        dagGraph.addNode(conceptCustomerTable);
        dagGraph.addEdge(coordinateModel, conceptCustomerTable, EdgeType.PROVIDES);
        System.out.println("识别实体: 客户表 (状态: " + conceptCustomerTable.getStatus() + ")");

        // 提取需要澄清的字段概念
        Map<String, String> fieldDomainCoords = new HashMap<>();
        fieldDomainCoords.put("coreElement", "Entity.Columns");
        fieldDomainCoords.put("weight", "100%");
        
        DomainCoordinateNode conceptFieldA = new DomainCoordinateNode("字段A", "字段A");
        conceptFieldA.setDomainCoordinates(fieldDomainCoords);
        dagGraph.addNode(conceptFieldA);
        dagGraph.updateNodeStatus(conceptFieldA.getId(), NodeStatus.REQUIRES_CLARIFY);
        dagGraph.addEdge(rawRequestNode, conceptFieldA, EdgeType.PROVIDES);
        
        DomainCoordinateNode conceptFieldB = new DomainCoordinateNode("字段B", "字段B");
        conceptFieldB.setDomainCoordinates(fieldDomainCoords); 
        dagGraph.addNode(conceptFieldB);
        dagGraph.updateNodeStatus(conceptFieldB.getId(), NodeStatus.REQUIRES_CLARIFY);
        dagGraph.addEdge(rawRequestNode, conceptFieldB, EdgeType.PROVIDES);
        System.out.println("识别字段: 字段A, 字段B (均需澄清类型)");

        // 提取统计和概念
        Map<String, String> sumDomainCoords = new HashMap<>();
        sumDomainCoords.put("coreElement", "Entity.Computes");
        sumDomainCoords.put("secondaryAxis", "功能增强");
        sumDomainCoords.put("weight", "30%");
        DomainCoordinateNode conceptSum = new DomainCoordinateNode("统计和", "统计和");
        conceptSum.setDomainCoordinates(sumDomainCoords);
        dagGraph.addNode(conceptSum);
        dagGraph.addEdge(rawRequestNode, conceptSum, EdgeType.PROVIDES);
        System.out.println("识别功能: 统计和 (状态: " + conceptSum.getStatus() + ")");

        // ==================== 3. 意图识别与参数构建 ====================
        System.out.println("\n>>> 3. 意图识别与参数构建 (自动状态管理)");
        
        // 意图1: 创建实体(客户表)
        IdentifyAtomicIntentNode intentCreateEntity = new IdentifyAtomicIntentNode();
        dagGraph.addNode(intentCreateEntity);
        
        // 意图2: 创建计算字段
        IdentifyAtomicIntentNode intentCreateComputedField = new IdentifyAtomicIntentNode();
        dagGraph.addNode(intentCreateComputedField);

        // 参数构建 - 利用自动状态设置
        ParameterNode paramEntityName = new ParameterNode("entityName", "String", true);
        paramEntityName.setValue("客户表", ParameterNode.ValueSource.DERIVATION);
        dagGraph.addNode(paramEntityName);
        System.out.println("参数: 实体名称 = '客户表' (状态: " + paramEntityName.getStatus() + ")");

        // 通过假设流程的参数
        ParameterNode paramComputedFieldName = new ParameterNode("computedFieldName", "String", true);
        dagGraph.addNode(paramComputedFieldName);
        
        AssumptionNode computedFieldNameAssumption = assumptionHandler.createAssumptionInFlow(
                intentCreateComputedField.getId(), 
                "假设计算字段名称为'sum_A_B'，基于计算公式和字段名", 
                "sum_A_B"
        );

        AssumptionValidationNode validationNode = null;
        if (computedFieldNameAssumption != null) {
            validationNode = assumptionHandler.createValidationNode(
                computedFieldNameAssumption.getId(),
                "验证计算字段名称假设的合理性"
            );
            
            if (validationNode != null) {
                assumptionHandler.connectValidationToNextNode(validationNode.getId(), paramComputedFieldName.getId());
            }
        }
        System.out.println("参数: 计算字段名称 (通过假设流程，状态: " + paramComputedFieldName.getStatus() + ")");

        // 依赖澄清的参数
        ParameterNode paramFieldAForSum = new ParameterNode("sourceField1", "Entity.Column", true);
        dagGraph.addNode(paramFieldAForSum);
        dagGraph.addEdge(conceptFieldA, paramFieldAForSum, EdgeType.PROVIDES);
        System.out.println("参数: 计算源字段1 (状态: " + paramFieldAForSum.getStatus() + ")");

        ParameterNode paramFieldBForSum = new ParameterNode("sourceField2", "Entity.Column", true);
        dagGraph.addNode(paramFieldBForSum);
        dagGraph.addEdge(conceptFieldB, paramFieldBForSum, EdgeType.PROVIDES);
        System.out.println("参数: 计算源字段2 (状态: " + paramFieldBForSum.getStatus() + ")");
        
        ParameterNode paramFormula = new ParameterNode("formula", "String", true);
        paramFormula.setValue("字段A + 字段B", ParameterNode.ValueSource.DERIVATION);
        dagGraph.addNode(paramFormula);
        System.out.println("参数: 计算公式 = '字段A + 字段B' (状态: " + paramFormula.getStatus() + ")");

        // ==================== 4. 任务定义与依赖设置 ====================
        System.out.println("\n>>> 4. 任务定义与依赖设置 (自动依赖分析)");
        
        // 任务1: 创建客户表
        StructuredTaskNode taskCreateCustomerTable = new StructuredTaskNode("创建客户表");
        dagGraph.addNode(taskCreateCustomerTable);
        dagGraph.addEdge(paramEntityName, taskCreateCustomerTable, EdgeType.PROVIDES);
        System.out.println("任务1: 创建客户表 (状态: " + taskCreateCustomerTable.getStatus() + ")");

        // 任务2: 创建计算总和字段
        StructuredTaskNode taskCreateSumField = new StructuredTaskNode("为 A 和 B 创建计算总和字段");
        dagGraph.addNode(taskCreateSumField);
        dagGraph.addEdge(paramComputedFieldName, taskCreateSumField, EdgeType.PROVIDES);
        dagGraph.addEdge(paramFieldAForSum, taskCreateSumField, EdgeType.PROVIDES);
        dagGraph.addEdge(paramFieldBForSum, taskCreateSumField, EdgeType.PROVIDES);
        dagGraph.addEdge(paramFormula, taskCreateSumField, EdgeType.PROVIDES);
        System.out.println("任务2: 创建计算总和字段 (状态: " + taskCreateSumField.getStatus() + ")");

        // ==================== 当前DAG状态概览 ====================
        System.out.println("\n当前DAG状态概览:");
        printDagState(dagGraph);
        

        // ==================== 5. 澄清处理阶段 ====================
        System.out.println("\n>>> 5. 澄清处理阶段");
        
        OutsideBorderNode fieldTypeBorderNode = new OutsideBorderNode("字段类型澄清边界", null);
        dagGraph.addNode(fieldTypeBorderNode);
        
        ClarificationQuestionNode qNodeForFieldA = clarificationHandler.createClarificationQuestion(
            fieldTypeBorderNode.getId(), 
            "字段A的数据类型是什么 (例如: 数字, 文本)?", 
            Arrays.asList("数字", "文本")
        );

        if (qNodeForFieldA != null) {
            System.out.println("系统生成澄清问题: " + qNodeForFieldA.getQuestion());
            
            String userAnswerA = "数字";
            System.out.println("用户回答: " + userAnswerA);
            
            ClarificationAnswerNode answerNodeA = clarificationHandler.resolveClarification(qNodeForFieldA.getId(), userAnswerA);
            
            if (answerNodeA != null) {
                ClarificationQuestionNode qNodeForFieldB = clarificationHandler.createClarificationQuestion(
                    answerNodeA.getId(), 
                    "字段B的数据类型是什么 (例如: 数字, 文本)?", 
                    Arrays.asList("数字", "文本")
                );
                
                if (qNodeForFieldB != null) {
                    System.out.println("系统生成第二个澄清问题: " + qNodeForFieldB.getQuestion());
                    
                    String userAnswerB = "文本";
                    System.out.println("用户回答: " + userAnswerB);
                    
                    ClarificationAnswerNode answerNodeB = clarificationHandler.resolveClarification(qNodeForFieldB.getId(), userAnswerB);
                    
                    if (answerNodeB != null) {
                        IdentifyAtomicIntentNode fieldTypeIntentNode = new IdentifyAtomicIntentNode();
                        dagGraph.addNode(fieldTypeIntentNode);
                        
                        clarificationHandler.connectAnswerToNextNode(answerNodeB.getId(), fieldTypeIntentNode.getId());
                        
                        List<ClarificationHandler.ClarificationContext> clarificationContexts = 
                            clarificationHandler.getAllClarificationContexts();
                        
                        System.out.println("✅ 澄清流程完成，获得 " + clarificationContexts.size() + " 个澄清上下文");
                        
                        // 基于澄清上下文更新相关参数
                        updateParametersBasedOnClarification(clarificationContexts, paramFieldAForSum, paramFieldBForSum, dagGraph);
                        
                        dagGraph.updateNodeStatus(fieldTypeIntentNode.getId(), NodeStatus.RESOLVED);
                    }
                }
            }
        }
        
        System.out.println("\n澄清后DAG状态概览:");
        printDagState(dagGraph);

        // ==================== 6. 假设验证阶段 ====================
        System.out.println("\n>>> 6. 假设验证阶段");

        if (validationNode != null) {
            System.out.println("验证计算字段名称假设...");
            assumptionHandler.processValidationResult(validationNode.getId(), true, "字段名称符合命名规范");
            
            paramComputedFieldName.setValue("sum_A_B", ParameterNode.ValueSource.ASSUMED);
            dagGraph.updateNodeStatus(paramComputedFieldName.getId(), NodeStatus.RESOLVED);
            
            System.out.println("✅ 假设已验证为有效: " + computedFieldNameAssumption.getAssumptionDescription());
            System.out.println("计算字段名称参数已更新为: " + paramComputedFieldName.getValue());
        }
        
        System.out.println("\n假设验证后DAG状态概览:");
        printDagState(dagGraph);

        // ==================== 7. 参数值自动传播 ====================
        System.out.println("\n>>> 7. 参数值自动传播 (使用重构后的传播器)");
        
        // 使用重构后的传播器进行强制检查
        propagatorV2.forceCheckAllNodes();
        
        System.out.println("参数传播完成，最新状态:");
        printDagState(dagGraph);

        // ==================== 8. 主要任务执行阶段 ====================
        System.out.println("\n>>> 8. 主要任务执行阶段");
        
        List<StructuredTaskNode> executedTasksMain = taskExecutionManager.executeAllReadyTasks();
        if (!executedTasksMain.isEmpty()) {
            System.out.println("✅ 成功执行 " + executedTasksMain.size() + " 个任务:");
            for (StructuredTaskNode task : executedTasksMain) {
                System.out.println("- " + task.getTaskDescription() + " (状态: " + task.getStatus() + ")");
            }
        } else {
            System.out.println("❌ 当前没有准备好执行的任务");
        }

        // ==================== 9. 最终状态检查 ====================
        System.out.println("\n>>> 9. 最终状态检查");
        
        boolean allTasksCompleted = true;
        System.out.println("检查所有任务状态:");
        for (IDagNode node : dagGraph.getAllNodes()) {
            if (node instanceof StructuredTaskNode taskNode) {
                if (taskNode.getStatus() != NodeStatus.COMPLETED && taskNode.getStatus() != NodeStatus.FAILED) {
                    allTasksCompleted = false;
                    System.out.println("❌ 任务「" + taskNode.getTaskDescription() + "」未完成，当前状态: " + taskNode.getStatus());
                } else {
                    System.out.println("✅ 任务「" + taskNode.getTaskDescription() + "」已完成，状态: " + taskNode.getStatus());
                }
            }
        }
        
        if (allTasksCompleted) {
            System.out.println("\n 所有任务已完成!");
        } else {
            System.out.println("\n️ 部分任务未完成");
        }

        // 清理资源
        propagatorV2.shutdown();
        
        System.out.println("\n=============== 示例运行完成 (V2架构) ===============");
    }

    /**
     * 打印当前DAG图状态
     */
    private static void printDagState(DagGraph dagGraph) {
        System.out.println("--------- 当前DAG状态 ---------");
        
        System.out.println("◆ 用户请求和领域概念:");
        printNodesByTypes(dagGraph, Arrays.asList("USER_RAW_REQUEST", "DOMAIN_COORDINATE"));
        
        System.out.println("\n◆ 意图和参数:");
        printNodesByTypes(dagGraph, Arrays.asList("IDENTIFIED_ATOMIC_INTENT", "PARAMETER"));
        
        System.out.println("\n◆ 假设、验证和澄清:");
        printNodesByTypes(dagGraph, Arrays.asList("ASSUMPTION", "ASSUMPTION_VALIDATION", "CLARIFICATION_QUESTION", "CLARIFICATION_ANSWER"));
        
        System.out.println("\n◆ 结构化子任务:");
        printNodesByTypes(dagGraph, Arrays.asList("STRUCTURED_SUB_TASK"));
        
        System.out.println("--------------------------------");
    }
    
    private static void printNodesByTypes(DagGraph dagGraph, List<String> types) {
        boolean foundAny = false;
        for (IDagNode node : dagGraph.getAllNodes()) {
            if (types.contains(node.getType().toString())) {
                foundAny = true;
                
                if (node instanceof StructuredTaskNode taskNode) {
                    System.out.println("· 任务: " + taskNode.getTaskDescription() + 
                                     " [状态: " + taskNode.getStatus() + "]");
                } else if (node instanceof ParameterNode paramNode) {
                    System.out.println("· 参数: " + paramNode.getParameterName() + 
                                     " [" + paramNode.getStatus() + "]" +
                                     " = " + paramNode.getValue());
                } else if (node instanceof AssumptionNode assumpNode) {
                    System.out.println("· 假设: " + assumpNode.getAssumptionDescription() + 
                                     " [" + assumpNode.getStatus() + "]");
                } else if (node instanceof DomainCoordinateNode domainNode) {
                    System.out.println("· 概念: " + domainNode.getConceptName() + 
                                     " [" + domainNode.getStatus() + "]");
                } else if (node instanceof IdentifyAtomicIntentNode intentNode) {
                    System.out.println("· 意图: " + intentNode.getId() +
                                     " [" + intentNode.getStatus() + "]");
                } else {
                    System.out.println("· " + node.getType() + ": " + node.getId() + 
                                     " [" + node.getStatus() + "]");
                }
            }
        }
        if (!foundAny) {
            System.out.println("(无此类型节点)");
        }
    }

    private static void updateParametersBasedOnClarification(
            List<ClarificationHandler.ClarificationContext> clarificationContexts,
            ParameterNode paramFieldAForSum, 
            ParameterNode paramFieldBForSum,
            DagGraph dagGraph) {
        
        for (ClarificationHandler.ClarificationContext context : clarificationContexts) {
            if (context.question().contains("字段A")) {
                paramFieldAForSum.setAttachProp("resolvedValue", "field_A_column_id");
                paramFieldAForSum.setAttachProp("dataType", context.answer());
                paramFieldAForSum.setValue("field_A_column_id", ParameterNode.ValueSource.CLARIFIED);
                dagGraph.updateNodeStatus(paramFieldAForSum.getId(), NodeStatus.RESOLVED);
                System.out.println("✅ 字段A类型已澄清为'" + context.answer() + "'，参数已更新");
            } else if (context.question().contains("字段B")) {
                paramFieldBForSum.setAttachProp("resolvedValue", "field_B_column_id");
                paramFieldBForSum.setAttachProp("dataType", context.answer());
                paramFieldBForSum.setValue("field_B_column_id", ParameterNode.ValueSource.CLARIFIED);
                dagGraph.updateNodeStatus(paramFieldBForSum.getId(), NodeStatus.RESOLVED);
                System.out.println("✅ 字段B类型已澄清为'" + context.answer() + "'，参数已更新");
            }
        }
    }
} 