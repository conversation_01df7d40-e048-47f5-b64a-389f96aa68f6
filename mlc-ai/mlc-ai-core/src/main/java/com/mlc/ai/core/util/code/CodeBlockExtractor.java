package com.mlc.ai.core.util.code;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class CodeBlockExtractor {

    private static final Pattern CODE_BLOCK_REGEX = Pattern.compile("```([a-zA-Z0-9]*)\\s?([\\s\\S]*?)```", Pattern.DOTALL);

    /**
     * 代码块信息实体类
     */
    @Data
    public static class CodeBlock {
        private String language;
        private String code;
        private String raw;
        private int index;
        private String fileName;

        public CodeBlock(String language, String code, String raw, int index) {
            this.language = language;
            this.code = code;
            this.raw = raw;
            this.index = index;
            this.fileName = "code-" + language + "-" + Instant.now().toEpochMilli() + "." + getFileExtension(language);
        }
    }

    /**
     * 解析结果类，包含代码块和非代码块内容
     */
    @Data
    public static class ParseResult {
        private List<CodeBlock> codeBlocks;
        private List<String> textSegments;
        private String originalContent;

        public ParseResult(String originalContent, List<CodeBlock> codeBlocks, List<String> textSegments) {
            this.originalContent = originalContent;
            this.codeBlocks = codeBlocks;
            this.textSegments = textSegments;
        }

        /**
         * 重建原始内容，可以用于在处理代码块后重新组合
         * @return 重建的内容
         */
        public String rebuildContent() {
            if (codeBlocks.isEmpty()) {
                return originalContent;
            }

            StringBuilder result = new StringBuilder();
            for (int i = 0; i < textSegments.size(); i++) {
                result.append(textSegments.get(i));
                if (i < codeBlocks.size()) {
                    result.append(codeBlocks.get(i).getRaw());
                }
            }
            return result.toString();
        }

        /**
         * 重建内容，但使用处理后的代码块内容
         * @param processedCodeMap 处理后的代码块映射，键为索引，值为处理后的代码
         * @return 重建的内容
         */
        public String rebuildWithProcessedCode(Map<Integer, String> processedCodeMap) {
            if (codeBlocks.isEmpty()) {
                return originalContent;
            }

            StringBuilder result = new StringBuilder();
            for (int i = 0; i < textSegments.size(); i++) {
                result.append(textSegments.get(i));
                if (i < codeBlocks.size()) {
                    CodeBlock block = codeBlocks.get(i);
                    if (processedCodeMap.containsKey(i)) {
                        result.append("```").append(block.getLanguage()).append("\n")
                              .append(processedCodeMap.get(i))
                              .append("\n```");
                    } else {
                        result.append(block.getRaw());
                    }
                }
            }
            return result.toString();
        }
    }

    /**
     * 解析内容，提取代码块并保留非代码块文本
     *
     * @param content 待解析的内容
     * @return 解析结果，包含代码块和文本段
     */
    public static ParseResult parseContent(String content) {
        if (StringUtils.isBlank(content)) {
            return new ParseResult("", new ArrayList<>(), List.of(""));
        }

        List<CodeBlock> codeBlocks = new ArrayList<>();
        List<String> textSegments = new ArrayList<>();
        
        Matcher matcher = CODE_BLOCK_REGEX.matcher(content);
        int lastEnd = 0;
        
        while (matcher.find()) {
            String language = StringUtils.isBlank(matcher.group(1)) ? "text" : matcher.group(1);
            String rawCode = matcher.group(2);
            
            // 添加代码块前的文本
            textSegments.add(content.substring(lastEnd, matcher.start()));
            
            if (StringUtils.isNotBlank(rawCode)) {
                CodeBlock codeBlock = new CodeBlock(
                    language,
                    rawCode,
                    matcher.group(0),
                    matcher.start()
                );
                codeBlocks.add(codeBlock);
                
                log.debug("提取到代码块: 语言={}, 长度={}", language, rawCode.length());
            }
            
            lastEnd = matcher.end();
        }
        
        // 添加最后一个代码块后的文本
        textSegments.add(content.substring(lastEnd));
        
        return new ParseResult(content, codeBlocks, textSegments);
    }

    /**
     * 根据语言获取对应的文件扩展名
     *
     * @param language 编程语言名称
     * @return 对应的文件扩展名
     */
    private static String getFileExtension(String language) {
        if (StringUtils.isBlank(language)) {
            return "txt";
        }

        Map<String, String> extensionMap = new HashMap<>();
        extensionMap.put("java", "java");
        extensionMap.put("python", "py");
        extensionMap.put("javascript", "js");
        extensionMap.put("js", "js");
        extensionMap.put("typescript", "ts");
        extensionMap.put("ts", "ts");
        extensionMap.put("html", "html");
        extensionMap.put("css", "css");
        extensionMap.put("json", "json");
        extensionMap.put("xml", "xml");
        extensionMap.put("yaml", "yaml");
        extensionMap.put("yml", "yml");
        extensionMap.put("sql", "sql");
        extensionMap.put("markdown", "md");
        extensionMap.put("md", "md");
        extensionMap.put("shell", "sh");
        extensionMap.put("bash", "sh");
        extensionMap.put("sh", "sh");
        extensionMap.put("c", "c");
        extensionMap.put("cpp", "cpp");
        extensionMap.put("c++", "cpp");
        extensionMap.put("csharp", "cs");
        extensionMap.put("cs", "cs");
        extensionMap.put("go", "go");
        extensionMap.put("rust", "rs");
        extensionMap.put("ruby", "rb");
        extensionMap.put("php", "php");
        extensionMap.put("kotlin", "kt");
        extensionMap.put("kt", "kt");
        extensionMap.put("scala", "scala");
        extensionMap.put("swift", "swift");
        extensionMap.put("dart", "dart");
        
        return extensionMap.getOrDefault(language.toLowerCase(), "txt");
    }
}
