package com.mlc.ai.core.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * 分支配置构建器
 */
public class BranchConfigBuilder {

    /**
     * 分支配置类
     */
    @Data
    @Builder(builderClassName = "Builder")
    public static class BranchConfig {
        /**
         * 单个分支ID
         */
        private String branchId;

        /**
         * 多个分支ID列表
         */
        @lombok.Builder.Default
        private List<String> branchIds = List.of();

        /**
         * 分支匹配模式
         */
        @lombok.Builder.Default
        private BranchMatchMode matchMode = BranchMatchMode.ANY;

        /**
         * 分支条件表达式（可选）
         * 用于更复杂的分支匹配逻辑
         */
        private String condition;

        /**
         * 是否为默认分支（无条件执行）
         */
        @lombok.Builder.Default
        private boolean isDefault = false;

        /**
         * 获取所有分支ID
         */
        public List<String> getAllBranchIds() {
            List<String> allIds = new ArrayList<>();
            if (branchId != null && !branchId.isEmpty()) {
                allIds.add(branchId);
            }
            if (branchIds != null && !branchIds.isEmpty()) {
                allIds.addAll(branchIds);
            }
            return allIds;
        }

        /**
         * 检查是否匹配指定分支
         */
        public boolean matches(String selectedBranch) {
            if (isDefault) {
                return true;
            }

            List<String> allIds = getAllBranchIds();
            if (allIds.isEmpty()) {
                return false;
            }

            return switch (matchMode) {
                case ANY -> allIds.contains(selectedBranch);
                case ALL -> allIds.size() == 1 && allIds.get(0).equals(selectedBranch);
                case NONE -> !allIds.contains(selectedBranch);
            };
        }
    }

    /**
     * 分支匹配模式
     */
    public enum BranchMatchMode {
        /**
         * 任意匹配 - 只要选中的分支在配置的分支列表中即可
         */
        ANY,

        /**
         * 全部匹配 - 必须完全匹配（主要用于单分支场景）
         */
        ALL,

        /**
         * 排除匹配 - 选中的分支不在配置的分支列表中时执行
         */
        NONE
    }

    /**
     * 创建单分支配置
     * 
     * @param branchId 分支ID
     * @return 分支配置
     */
    public static BranchConfig forBranch(String branchId) {
        return BranchConfig.builder()
            .branchId(branchId)
            .matchMode(BranchMatchMode.ANY)
            .build();
    }
    
    /**
     * 创建多分支配置（任意匹配）
     * 
     * @param branchIds 分支ID列表
     * @return 分支配置
     */
    public static BranchConfig forAnyBranch(String... branchIds) {
        return BranchConfig.builder()
            .branchIds(Arrays.asList(branchIds))
            .matchMode(BranchMatchMode.ANY)
            .build();
    }
    
    /**
     * 创建多分支配置（任意匹配）
     * 
     * @param branchIds 分支ID列表
     * @return 分支配置
     */
    public static BranchConfig forAnyBranch(List<String> branchIds) {
        return BranchConfig.builder()
            .branchIds(branchIds)
            .matchMode(BranchMatchMode.ANY)
            .build();
    }
    
    /**
     * 创建排除分支配置
     * 
     * @param branchIds 要排除的分支ID列表
     * @return 分支配置
     */
    public static BranchConfig excludeBranches(String... branchIds) {
        return BranchConfig.builder()
            .branchIds(Arrays.asList(branchIds))
            .matchMode(BranchMatchMode.NONE)
            .build();
    }
    
    /**
     * 创建默认分支配置（无条件执行）
     * 
     * @return 分支配置
     */
    public static BranchConfig defaultBranch() {
        return BranchConfig.builder().isDefault(true).build();
    }
    
    /**
     * 创建条件分支配置
     * 
     * @param condition 条件表达式
     * @return 分支配置
     */
    public static BranchConfig withCondition(String condition) {
        return BranchConfig.builder().condition(condition).build();
    }
}