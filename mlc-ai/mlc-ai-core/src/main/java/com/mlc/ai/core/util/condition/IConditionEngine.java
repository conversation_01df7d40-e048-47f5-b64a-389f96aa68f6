package com.mlc.ai.core.util.condition;


import com.mlc.ai.core.enums.ConditionEngineType;
import java.util.Map;

/**
 * 条件表达式引擎接口
 * 支持不同类型的条件表达式评估
 */
public interface IConditionEngine {
    
    /**
     * 评估条件表达式
     *
     * @param expression 条件表达式
     * @param context 上下文变量
     * @return 评估结果
     * @throws ConditionEvaluationException 条件评估异常
     */
    boolean evaluate(String expression, Map<String, Object> context) throws ConditionEvaluationException;
    
    /**
     * 获取引擎类型
     *
     * @return 引擎类型
     */
    ConditionEngineType getEngineType();
    
    /**
     * 验证表达式语法
     *
     * @param expression 条件表达式
     * @return 验证结果
     */
    ValidationResult validateExpression(String expression);
    
    /**
     * 条件评估异常
     */
    class ConditionEvaluationException extends Exception {
        public ConditionEvaluationException(String message) {
            super(message);
        }
        
        public ConditionEvaluationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * 表达式验证结果
     */
    record ValidationResult(boolean isValid, String errorMessage) {
        public static ValidationResult valid() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult invalid(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }
    }
} 