package com.mlc.meta.service.entity;

import com.mlc.base.common.enums.common.YesOrNoEnum;
import com.mlc.base.common.enums.meta.ControlTypeEnum;
import com.mlc.base.common.enums.meta.DeleteBehaviorEnum;
import com.mlc.base.common.enums.meta.EntityTypeEnum;
import com.mlc.meta.dao.entity.MlcMetaModelDomain;
import com.mlc.meta.dao.entity.MlcMetaModelEntityRelation;
import com.mlc.meta.dao.entity.MlcMetaModelObjectEntity;
import com.mlc.meta.dao.entity.MlcMetaModelObjectEntityField;
import com.mlc.meta.dao.entity._gen._MlcMetaModelDomain;
import com.mlc.meta.dao.entity._gen._MlcMetaModelEntityRelation;
import com.mlc.meta.dao.entity._gen._MlcMetaModelObjectEntityField;
import com.mlc.meta.service.MlcMetaConstants;
import com.mlc.meta.service.MlcMetaErrors;
import com.mlc.meta.service.model.ObjectEntityToOrmModel;
import com.mlc.meta.service.model.OrmModelToObjectEntity;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.core.Name;
import io.nop.api.core.beans.FilterBeans;
import io.nop.api.core.exceptions.NopException;
import io.nop.biz.crud.CrudBizModel;
import io.nop.commons.util.StringHelper;
import io.nop.commons.util.TagsHelper;
import io.nop.core.context.IServiceContext;
import io.nop.core.lang.sql.SQL;
import io.nop.dao.api.IEntityDao;
import io.nop.dao.jdbc.IJdbcTemplate;
import io.nop.orm.ddl.DdlSqlCreator;
import io.nop.orm.model.OrmEntityModel;
import io.nop.orm.model.OrmRelationType;
import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.Builder;

/**
 * 实体关系业务模型类
 * 负责处理实体间的关联关系创建和管理，支持一对一、一对多、多对一、多对多四种关系类型
 */
@BizModel("MlcMetaModelEntityRelation")
public class MlcMetaModelEntityRelationBizModel extends CrudBizModel<MlcMetaModelEntityRelation> {
    public MlcMetaModelEntityRelationBizModel() {
        setEntityName(MlcMetaModelEntityRelation.class.getName());
    }

    @Inject
    protected MlcMetaModelObjectEntityFieldBizModel objectEntityFieldBizModel;

    @Inject
    protected IJdbcTemplate jdbcTemplate;

    @Override
    public MlcMetaModelEntityRelation save(@Name("data") Map<String, Object> data, IServiceContext context) {
        // 提取多对多关系所需的参数
        M2MRelationParams m2mParams = extractM2MParams(data);
        
        // 保存基础实体关系
        MlcMetaModelEntityRelation entityRelation = super.save(data, context);
        
        // 根据关系类型处理具体逻辑
        processRelationByType(entityRelation, m2mParams, data, context);
        
        return entityRelation;
    }

    /**
     * 提取多对多关系参数
     */
    private M2MRelationParams extractM2MParams(Map<String, Object> data) {
        return M2MRelationParams.builder()
            .refRelationName(data.remove("refRelationName"))
            .refRelationDisplayName(data.remove("refRelationDisplayName"))
            .middleEntityName(data.remove("middleEntityName"))
            .middleTableName(data.remove("middleTableName"))
            .middleEntityDisplayName(data.remove("middleEntityDisplayName"))
            .build();
    }

    /**
     * 根据关系类型处理不同的关联逻辑
     */
    private void processRelationByType(MlcMetaModelEntityRelation entityRelation, M2MRelationParams m2mParams,
                                     Map<String, Object> data, IServiceContext context) {
        String relationType = entityRelation.getRelationType();
        List<MlcMetaModelObjectEntityField> primaryKeyFields = this.getPrimaryKeyFields(entityRelation, context);

        RelationTypeHandler handler = switch (OrmRelationType.valueOf(relationType)) {
            case m2o, o2o -> new ManyToOneHandler();
            case o2m -> new OneToManyHandler();
            case m2m -> new ManyToManyHandler(m2mParams);
        };
        handler.handle(entityRelation, primaryKeyFields, data, context);
    }

    /**
     * 关系类型处理器接口
     */
    private interface RelationTypeHandler {
        void handle(MlcMetaModelEntityRelation entityRelation,
                   List<MlcMetaModelObjectEntityField> primaryKeyFields,
                   Map<String, Object> data,
                   IServiceContext context);
    }

    /**
     * 多对一和一对一关系处理器
     */
    private class ManyToOneHandler implements RelationTypeHandler {
        @Override
        public void handle(MlcMetaModelEntityRelation entityRelation,
                          List<MlcMetaModelObjectEntityField> primaryKeyFields,
                          Map<String, Object> data,
                          IServiceContext context) {
            // 使用右表的主键作为右连接字段
            String rightFieldId = primaryKeyFields.get(1).getObjectEntityFieldId();
            entityRelation.setRightObjectEntityFieldId(rightFieldId);

            String leftFieldId = data.get(_MlcMetaModelEntityRelation.PROP_NAME_leftObjectEntityFieldId).toString();
            configureRelationTags(context, entityRelation, leftFieldId, entityRelation.getRelationType());
        }
    }

    /**
     * 一对多关系处理器
     */
    private class OneToManyHandler implements RelationTypeHandler {
        @Override
        public void handle(MlcMetaModelEntityRelation entityRelation,
                          List<MlcMetaModelObjectEntityField> primaryKeyFields,
                          Map<String, Object> data,
                          IServiceContext context) {
            // 使用左表的主键作为左连接字段
            String leftFieldId = primaryKeyFields.get(0).getObjectEntityFieldId();
            entityRelation.setLeftObjectEntityFieldId(leftFieldId);

            String rightFieldId = data.get(_MlcMetaModelEntityRelation.PROP_NAME_rightObjectEntityFieldId).toString();
            configureRelationTags(context, entityRelation, rightFieldId, entityRelation.getRelationType());
        }
    }

    /**
     * 多对多关系处理器
     */
    private class ManyToManyHandler implements RelationTypeHandler {

        private final M2MRelationParams m2mParams;

        public ManyToManyHandler(M2MRelationParams m2mParams) {
            this.m2mParams = m2mParams;
        }

        @Override
        public void handle(MlcMetaModelEntityRelation entityRelation,
                          List<MlcMetaModelObjectEntityField> primaryKeyFields,
                          Map<String, Object> data,
                          IServiceContext context) {
            // 1. 创建中间表实体
            MlcMetaModelObjectEntity middleEntity = createMiddleObjectEntity(
                m2mParams.middleTableName,
                m2mParams.middleEntityName,
                m2mParams.middleEntityDisplayName,
                entityRelation
            );

            // 2. 创建中间表关联字段
            MiddleEntityFields middleFields = createMiddleEntityFields(entityRelation, middleEntity);

            // 3. 配置正向关联
            configureForwardRelation(entityRelation, primaryKeyFields, middleFields.leftField);

            // 4. 创建反向关联
            createReverseRelation(entityRelation, m2mParams, primaryKeyFields, middleFields.rightField);

            // 5. 构建完整的中间表模型
            buildCompleteMiddleEntityModel(middleEntity, middleFields);

            // 6. 创建数据库表
            createMiddleTable(middleEntity, middleFields);
        }
    }

    /**
     * 获取左右表的主键字段
     */
    private List<MlcMetaModelObjectEntityField> getPrimaryKeyFields(MlcMetaModelEntityRelation entityRelation,
                                                                   IServiceContext context) {
        List<MlcMetaModelObjectEntityField> list = objectEntityFieldBizModel.findList(
            FilterBeans.and(
                FilterBeans.eq(_MlcMetaModelObjectEntityField.PROP_NAME_isPrimary, true),
                FilterBeans.in(_MlcMetaModelObjectEntityField.PROP_NAME_objectEntityId,
                             Arrays.asList(entityRelation.getObjectEntityId(), entityRelation.getRefObjectEntityId()))
            ).toQueryBean(), null, context);
            
        // 确保左右表主键字段的正确顺序
        if (list.get(0).getObjectEntityId().equals(entityRelation.getRefObjectEntityId())) {
            MlcMetaModelObjectEntityField temp = list.get(0);
            list.set(0, list.get(1));
            list.set(1, temp);
        }
        return list;
    }

    /**
     * 配置关系标签
     */
    private void configureRelationTags(IServiceContext context, MlcMetaModelEntityRelation entityRelation,
                                     String fieldId, String relationType) {
        Set<String> tagSet = MlcMetaConstants.DEFAULT_ENTITY_RELATION_TAGS_TEXT;
        String domainName = objectEntityFieldBizModel.get(fieldId, true, context)
                                                   .getModelDomain()
                                                   .getDomainName();

        // 根据控件类型添加特定标签
        addControlTypeSpecificTags(tagSet, domainName, relationType);
        
        // 添加删除行为标签
        addDeleteBehaviorTags(tagSet, entityRelation.getDeleteBehavior());
        
        entityRelation.setTagsText(TagsHelper.toString(tagSet));
    }

    /**
     * 根据控件类型添加特定标签
     */
    private void addControlTypeSpecificTags(Set<String> tagSet, String domainName, String relationType) {
        // 关联控件添加明细展示标签
        if (domainName.equals(ControlTypeEnum.RELATE_SHEET.getDomain())) {
            tagSet.add("relateSheetSourceValue");
        }

        // 一对多关系的特殊标签
        if (relationType.equals(OrmRelationType.o2m.name())) {
            tagSet.add("connection");
            if (domainName.equals(ControlTypeEnum.SUB_LIST.getDomain())) {
                tagSet.add("subListCount");
            }
        }
    }

    /**
     * 添加删除行为标签
     */
    private void addDeleteBehaviorTags(Set<String> tagSet, Integer deleteBehavior) {
        if (Objects.equals(deleteBehavior, DeleteBehaviorEnum.CASCADE_DELETE.getValue())) {
            tagSet.add(DeleteBehaviorEnum.CASCADE_DELETE.getConstants());
        } else if (Objects.equals(deleteBehavior, DeleteBehaviorEnum.FORBID_DELETE.getValue())) {
            tagSet.add(DeleteBehaviorEnum.FORBID_DELETE.getConstants());
        }
    }

    /**
     * 创建中间表实体
     */
    private MlcMetaModelObjectEntity createMiddleObjectEntity(Object middleTableName, Object middleEntityName,
                                                             Object middleEntityDisplayName, MlcMetaModelEntityRelation entityRelation) {
        IEntityDao<MlcMetaModelObjectEntity> objectEntityDao = daoFor(MlcMetaModelObjectEntity.class);
        MlcMetaModelObjectEntity middleObjectEntity = objectEntityDao.newEntity();
        
        middleObjectEntity.setEntityName(middleEntityName.toString());
        middleObjectEntity.setDisplayName(middleEntityDisplayName.toString());
        middleObjectEntity.setTableName(middleTableName.toString());
        middleObjectEntity.setDataConnectorId(entityRelation.getObjectEntity().getDataConnectorId());
        middleObjectEntity.setModelDefinitionId(entityRelation.getObjectEntity().getModelDefinitionId());
        middleObjectEntity.setEntityType(EntityTypeEnum.MIDDLE.getValue());
        middleObjectEntity.setStatus(YesOrNoEnum.YES.value());
        
        objectEntityDao.saveEntity(middleObjectEntity);
        return middleObjectEntity;
    }

    /**
     * 创建中间表的关联字段
     */
    private MiddleEntityFields createMiddleEntityFields(MlcMetaModelEntityRelation entityRelation,
                                                       MlcMetaModelObjectEntity middleEntity) {
        IEntityDao<MlcMetaModelObjectEntityField> entityFieldDao = daoFor(MlcMetaModelObjectEntityField.class);
        
        // 创建左侧关联字段
        MlcMetaModelObjectEntityField leftField = buildEntityFieldProp(
            entityFieldDao, entityRelation.getObjectEntity()
        );
        middleEntity.getModelObjectEntityFields().add(leftField);

        // 创建右侧关联字段
        MlcMetaModelObjectEntityField rightField = buildEntityFieldProp(
            entityFieldDao, entityRelation.getRefObjectEntity()
        );
        middleEntity.getModelObjectEntityFields().add(rightField);

        return new MiddleEntityFields(leftField, rightField);
    }

    /**
     * 配置正向关联
     */
    private void configureForwardRelation(MlcMetaModelEntityRelation entityRelation,
                                        List<MlcMetaModelObjectEntityField> primaryKeyFields,
                                        MlcMetaModelObjectEntityField leftField) {
        entityRelation.setLeftObjectEntityFieldId(primaryKeyFields.get(0).getObjectEntityFieldId());
        entityRelation.setRightObjectEntityFieldId(leftField.getObjectEntityFieldId());
        entityRelation.setMiddleObjectEntityId(leftField.getModelObjectEntity().getObjectEntityId());
    }

    /**
     * 创建反向关联
     */
    private void createReverseRelation(MlcMetaModelEntityRelation entityRelation, 
                                     M2MRelationParams m2mParams,
                                     List<MlcMetaModelObjectEntityField> primaryKeyFields, 
                                     MlcMetaModelObjectEntityField rightField) {
        MlcMetaModelEntityRelation refEntityRelation = entityRelation.cloneInstance();
        refEntityRelation.setEntityRelationId(null);
        refEntityRelation.setObjectEntityId(entityRelation.getRefObjectEntityId());
        refEntityRelation.setRefObjectEntityId(entityRelation.getObjectEntityId());
        refEntityRelation.setRelationName(m2mParams.refRelationName.toString());
        refEntityRelation.setRelationDisplayName(m2mParams.refRelationDisplayName.toString());
        refEntityRelation.setLeftObjectEntityFieldId(primaryKeyFields.get(1).getObjectEntityFieldId());
        refEntityRelation.setRightObjectEntityFieldId(rightField.getObjectEntityFieldId());
        
        dao().saveEntity(refEntityRelation);
    }

    /**
     * 构建完整的中间表模型
     */
    private void buildCompleteMiddleEntityModel(MlcMetaModelObjectEntity middleEntity,
                                              MiddleEntityFields middleFields) {
        // 添加默认字段
        ObjectEntityToOrmModel entityToOrmModel = new ObjectEntityToOrmModel(middleEntity);
        OrmEntityModel defaultEntityModel = entityToOrmModel.buildDefaultStructure().getOrmEntityModel();
        middleEntity.getModelObjectEntityFields().addAll(
            new OrmModelToObjectEntity(defaultEntityModel, true).buildObjectEntityFields()
        );
        dao().flushSession();

        // 构建完整模型
        OrmEntityModel ormEntityModel = entityToOrmModel.buildColumnStructure().getOrmEntityModel();
        ormEntityModel.init();
        
        // 保存到中间字段以供后续使用
        middleFields.entityToOrmModel = entityToOrmModel;
        middleFields.ormEntityModel = ormEntityModel;
    }

    /**
     * 创建中间表数据库表
     */
    private void createMiddleTable(MlcMetaModelObjectEntity middleEntity, MiddleEntityFields middleFields) {
        String querySpace = middleFields.ormEntityModel.getQuerySpace();
        
        // 检查表是否已存在
        if (jdbcTemplate.existsTable(querySpace, middleEntity.getTableName())) {
            throw new NopException(MlcMetaErrors.ERR_META_ENTITY_TABLE_EXIST);
        }

        // 创建表结构
        DdlSqlCreator ddlSqlCreator = new DdlSqlCreator(jdbcTemplate.getDialectForQuerySpace(querySpace));
        middleFields.ormEntityModel.addUniqueKey(
            middleFields.entityToOrmModel.toOrmUniqueKeyModel(middleFields.leftField, middleFields.rightField)
        );
        String createSql = ddlSqlCreator.createTable(middleFields.ormEntityModel, false);

        // 执行创建表SQL
        jdbcTemplate.executeUpdate(
            SQL.begin().querySpace(querySpace).name("Create:" + middleEntity.getTableName()).sql(createSql).end()
        );
    }

    /**
     * 构建实体字段属性
     */
    private MlcMetaModelObjectEntityField buildEntityFieldProp(IEntityDao<MlcMetaModelObjectEntityField> entityDao,
                                                              MlcMetaModelObjectEntity objectEntity) {
        // 查找关联表控件域
        MlcMetaModelDomain relateSheetDomain = daoFor(MlcMetaModelDomain.class).findFirstByQuery(
            FilterBeans.eq(_MlcMetaModelDomain.PROP_NAME_domainName,
                         ControlTypeEnum.RELATE_SHEET.name().toLowerCase(Locale.ROOT)).toQueryBean());
        
        MlcMetaModelObjectEntityField entityField = entityDao.newEntity().normalFieldSet();
        entityField.setFieldName(objectEntity.getTableName() + "_id");
        entityField.setDisplayName(objectEntity.getDisplayName() + "_关联键");
        entityField.setPropName(StringHelper.colCodeToPropName(entityField.getFieldName()));
        entityField.setModelDomainId(relateSheetDomain.getModelDomainId());
        entityField.setStdSqlType(relateSheetDomain.getStdSqlType());
        entityField.setPrecision(relateSheetDomain.getPrecision());
        entityField.setScale(relateSheetDomain.getScale());
        entityField.setIsUnique(true);
        
        entityDao.saveEntity(entityField);
        return entityField;
    }

    /**
     * 多对多关系参数封装类
     */
    @Builder
    private record M2MRelationParams(Object refRelationName, Object refRelationDisplayName,
                                     Object middleEntityName, Object middleTableName, Object middleEntityDisplayName) {}

    /**
     * 中间表字段封装类
     */
    private static class MiddleEntityFields {
        final MlcMetaModelObjectEntityField leftField;
        final MlcMetaModelObjectEntityField rightField;
        ObjectEntityToOrmModel entityToOrmModel;
        OrmEntityModel ormEntityModel;

        MiddleEntityFields(MlcMetaModelObjectEntityField leftField, MlcMetaModelObjectEntityField rightField) {
            this.leftField = leftField;
            this.rightField = rightField;
        }
    }
}
