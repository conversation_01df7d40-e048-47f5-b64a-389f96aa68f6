-- 注册用户的时候需要填充的表数据:mlc_uop_user_setting, mlc_uop_user_personal,mlc_uop_user

-- mlc_uop_user 用户表:
INSERT INTO `mlc_dev`.`mlc_uop_user` (`user_id`, `user_name`, `password`, `salt`, `open_id`, `mobile_phone`, `email`, `email_verified`, `user_type`, `status`, `pwd_updated_at`, `change_pwd_at_login`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('1', 'admin', '$2a$10$uehruebQWVWBVAIfkDI1F.MsmRGCj/WNlcM3BDKG6v0dR6LtwiGiW', 'c4a414bda46742b9bf9dedf4a56ffe6e', '0', '+8618500235210', '<EMAIL>', 1, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW());

-- mlc_uop_user_personal 用户个人信息表:
INSERT INTO `mlc_dev`.`mlc_uop_user_personal` (`sid`, `user_id`, `avatar`, `full_name`, `nick_name`, `gender`, `birthday`, `company_name`, `profession`, `address`, `weixin`, `linkedin`, `sina`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('1', '1', NULL, 'liu', NULL, 1, '1990-01-04', NULL, NULL, NULL, '3123123', NULL, NULL, 0, '1', NOW(), 'admin', NOW());

-- mlc_uop_user_setting 系统用户设置表:
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('1', '1', 'isPrivateMobile', '0', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('2', '1', 'isPrivateEmail', '0', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('3', '1', 'isTwoStepVerification', '0', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('4', '1', 'isOpenWeixinNotification', '1', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('5', '1', 'allowMultipleDevicesUse', '1', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('6', '1', 'lang', '1', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('7', '1', 'isOpenMessageSound', '1', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('8', '1', 'isOpenMessageTwinkle', '1', '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_user_setting` (`sid`, `user_id`, `field_key`, `field_value`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('9', '1', 'backHomepageWay', '2', '1', NOW(), '1', NOW());

-- mlc_uop_tenant 租户表:
INSERT INTO `mlc_dev`.`mlc_uop_tenant` (`project_id`, `company_name`, `project_code`, `industry_id`, `geography_id`, `status`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`, `remark`) VALUES ('78b9647a1abf44b1b376689d7298577b', '公司2222', 'b440053c', '23', NULL, 1, 0, 5, '1', NOW(), '1', NOW(), NULL);

-- mlc_uop_tenant_setting 租户设置表: 后面要改成创建租户的时候插入
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('1', '78b9647a1abf44b1b376689d7298577b', 'allowProjectCodeJoin', 'true', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('2', '78b9647a1abf44b1b376689d7298577b', 'userAuditEnabled', 'true', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('3', '78b9647a1abf44b1b376689d7298577b', 'userFillCompanyEnabled', 'false', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('4', '78b9647a1abf44b1b376689d7298577b', 'userFillDepartmentEnabled', 'true', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('5', '78b9647a1abf44b1b376689d7298577b', 'userFillJobEnabled', 'true', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('6', '78b9647a1abf44b1b376689d7298577b', 'userFillWorkSiteEnabled', 'false', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('7', '78b9647a1abf44b1b376689d7298577b', 'userFillJobNumberEnabled', 'false', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('8', '78b9647a1abf44b1b376689d7298577b', 'allowStructureForAll', 'false', 0, '1', NOW(), '1', NOW());
INSERT INTO `mlc_dev`.`mlc_uop_tenant_setting` (`sid`, `project_id`, `field_key`, `field_value`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('9', '78b9647a1abf44b1b376689d7298577b', 'allowStructureSelfEdit', 'false', 0, '1', NOW(), '1', NOW());


-- mlc_message_template 消息模板表:
INSERT INTO `mlc_dev`.`mlc_message_template` (`template_id`, `template_key`, `template_name`, `send_channel`, `send_type`, `send_cycle`, `send_client_key`, `source_form`, `source_identifier`, `content_type`, `content`, `effective_amount`, `status`, `remark`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('1', 'emailInviteJoin', '邀请加入组织', 1, 1, NULL, NULL, 1, 'newsTemplate/invite/email/join.html', 2, NULL, 3600, 0, NULL, 0, 0, '1', '2024-04-05 08:35:19', '1', '2024-04-05 08:35:32');
INSERT INTO `mlc_dev`.`mlc_message_template` (`template_id`, `template_key`, `template_name`, `send_channel`, `send_type`, `send_cycle`, `send_client_key`, `source_form`, `source_identifier`, `content_type`, `content`, `effective_amount`, `status`, `remark`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('2', 'emailApplyRegister', '邮件注册验证码', 1, 1, NULL, NULL, 1, 'newsTemplate/apply/email/register.html', 2, NULL, 5, 0, NULL, 0, 0, '1', '2024-04-05 08:35:19', '1', '2024-04-05 08:35:32');
INSERT INTO `mlc_dev`.`mlc_message_template` (`template_id`, `template_key`, `template_name`, `send_channel`, `send_type`, `send_cycle`, `send_client_key`, `source_form`, `source_identifier`, `content_type`, `content`, `effective_amount`, `status`, `remark`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('3', 'smsApplyRegistration', '手机号注册验证码', 2, 1, NULL, 'tencentDefault', 3, '2114341', 1, '您正在申请手机号注册，验证码为：#{[verificationCode]}，#{[effectiveAmount]}分钟内有效！', 5, 0, NULL, 0, 0, '1', '2024-04-05 08:35:19', '1', '2024-04-05 08:35:32');
INSERT INTO `mlc_dev`.`mlc_message_template` (`template_id`, `template_key`, `template_name`, `send_channel`, `send_type`, `send_cycle`, `send_client_key`, `source_form`, `source_identifier`, `content_type`, `content`, `effective_amount`, `status`, `remark`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('4', 'emailModifyAccount', '修改邮箱验证码', 1, 1, NULL, '', 1, 'newsTemplate/modify/email/account.html', 2, '', 5, 0, NULL, 0, 0, '1', '2024-04-12 13:47:10', '1', '2024-04-12 13:47:14');
INSERT INTO `mlc_dev`.`mlc_message_template` (`template_id`, `template_key`, `template_name`, `send_channel`, `send_type`, `send_cycle`, `send_client_key`, `source_form`, `source_identifier`, `content_type`, `content`, `effective_amount`, `status`, `remark`, `del_flag`, `version`, `create_by`, `created_at`, `update_by`, `updated_at`) VALUES ('5', 'smsModifyAccount', '修改手机号验证码', 2, 1, NULL, 'tencentDefault', 3, '2125888', 1, '您正在修改注册手机号码，验证码为：{1}，{2}分钟有效，请勿泄露给他人！', 5, 0, NULL, 0, 0, '1', '2024-04-12 13:47:10', '1', '2024-04-12 13:47:14');

-- mlc_uop_resource 资源表:
-- 插入顶层权限
INSERT INTO mlc_uop_resource (resource_id, resource_name, description, status, create_by, created_at, update_by, updated_at)
VALUES
    (10000, '用户', '', 1, 1, 'admin', NOW(), 'admin', NOW()),
    (13000, '组织', '', 1, 1, 'admin', NOW(), 'admin', NOW()),
    (15000, '应用', '', 1, 1, 'admin', NOW(), 'admin', NOW()),
    (16000, '商户服务', '', 1, 1, 'admin', NOW(), 'admin', NOW()),
    (17000, '日志', '', 1, 1, 'admin', NOW(), 'admin', NOW()),
    (19000, '集成和插件中心', '', 1, 1, 'admin', NOW(), 'admin', NOW());

-- 插入子权限 (用户)
INSERT INTO mlc_uop_resource (resource_id, resource_name, description, parent_id, status, create_by, created_at, update_by, updated_at)
VALUES
    (10100, '成员管理', '管理成员、部门、汇报关系、角色成员', 1, 10000, 1, 'admin', NOW(), 'admin', NOW()),
    (10300, '角色管理', '添加、删除组织角色，管理角色成员', 1, 10000, 1, 'admin', NOW(), 'admin', NOW()),
    (10500, '群组管理', '', 1, 10000, 1, 'admin', NOW(), 'admin', NOW()),
    (10700, '外部用户管理', '', 1, 10000, 1, 'admin', NOW(), 'admin', NOW()),
    (10900, '账号集成', '', 1, 10000, 1, 'admin', NOW(), 'admin', NOW()),
-- 插入10900的子权限
    (10910, '第三方平台集成', '', 1, 10900, 1, 'admin', NOW(), 'admin', NOW()),
    (10920, '微信公众号集成', '', 1, 10900, 1, 'admin', NOW(), 'admin', NOW()),
    (10930, 'LDAP用户目录', '', 1, 10900, 1, 'admin', NOW(), 'admin', NOW()),
    (10940, 'SSO登录', '', 1, 10900, 1, 'admin', NOW(), 'admin', NOW()),
    (10950, '开放接口', '', 1, 10900, 1, 'admin', NOW(), 'admin', NOW()),
    (10960, '平台账号登录', '', 1, 10900, 1, 'admin', NOW(), 'admin', NOW());

-- 插入子权限 (组织)
INSERT INTO mlc_uop_resource (resource_id, resource_name, description, parent_id, status, create_by, created_at, update_by, updated_at)
VALUES
    (13100, '基础设置', '', 1, 13000, 1, 'admin', NOW(), 'admin', NOW()),
    (13300, '账务', '管理账务、支付时可使用余额付款', 1, 13000, 1, 'admin', NOW(), 'admin', NOW()),
    (13500, '工作台设置', '可对组织工作台进行设置', 1, 13000, 1, 'admin', NOW(), 'admin', NOW()),
    (13700, '网络广播员', '可发送全员动态并置顶', 1, 13000, 1, 'admin', NOW(), 'admin', NOW()),
    (13800, '管理动态', '可对组织下其他成员的动态删除、修改可见范围', 1, 13000, 1, 'admin', NOW(), 'admin', NOW()),
    (13900, '安全', '', 1, 13000, 1, 'admin', NOW(), 'admin', NOW());

-- 插入子权限 (应用)
INSERT INTO mlc_uop_resource (resource_id, resource_name, description, parent_id, status, create_by, created_at, update_by, updated_at)
VALUES
    (15100, '创建应用', '在工作台创建应用。当对普通用户关闭时（安全-功能），则只有包含此权限的管理角色可以创建。', 1, 15000, 1, 'admin', NOW(), 'admin', NOW()),
    (15300, '应用服务和资源', '应用、工作流管理、全局变量、专属资源、聚合表、组织应用分组、交接工作(可在“成员与部门”下使用交接工作)', 1, 15000, 1, 'admin', NOW(), 'admin', NOW()),
    (15500, '使用分析', '', 1, 15000, 1, 'admin', NOW(), 'admin', NOW()),
    (15700, '通用设置', '', 1, 15000, 1, 'admin', NOW(), 'admin', NOW());

-- 插入子权限 (日志)
INSERT INTO mlc_uop_resource (resource_id, resource_name, description, parent_id, status, create_by, created_at, update_by, updated_at)
VALUES
    (17100, '应用日志', '', 1, 17000, 1, 'admin', NOW(), 'admin', NOW()),
    (17300, '登录日志', '', 1, 17000, 1, 'admin', NOW(), 'admin', NOW()),
    (17500, '组织管理日志', '', 1, 17000, 1, 'admin', NOW(), 'admin', NOW());

-- 插入子权限 (集成和插件中心)
INSERT INTO mlc_uop_resource (resource_id, resource_name, description, parent_id, status, create_by, created_at, update_by, updated_at)
VALUES
    (19100, 'API集成', '', 1, 19000, 1, 'admin', NOW(), 'admin', NOW()),
-- 插入19100的子权限
    (19110, '创建API连接', '在集成中心创建API连接。当对普通用户关闭时（安全-功能），则只有包含此权限的管理角色可以创建。', 1, 19100, 1, 'admin', NOW(), 'admin', NOW()),
    (19120, '管理所有API连接', '', 1, 19100, 1, 'admin', NOW(), 'admin', NOW()),
    (19300, '数据集成', '', 1, 19000, 1, 'admin', NOW(), 'admin', NOW()),
-- 插入19300的子权限
    (19310, '创建同步任务', '', 1, 19300, 1, 'admin', NOW(), 'admin', NOW()),
    (19320, '管理所有同步任务', '', 1, 19300, 1, 'admin', NOW(), 'admin', NOW()),
    (19330, '管理所有数据源', '', 1, 19300, 1, 'admin', NOW(), 'admin', NOW()),
    (19350, '管理工作表数据镜像', '', 1, 19300, 1, 'admin', NOW(), 'admin', NOW()),
    (19500, '插件', '', 1, 19000, 1, 'admin', NOW(), 'admin', NOW()),
-- 插入19500的子权限
    (19510, '开发插件', '在插件中心创建插件。当对普通用户关闭时（安全-功能），则只有包含此权限的管理角色可以创建。', 1, 19500, 1, 'admin', NOW(), 'admin', NOW()),
    (19520, '管理所有插件', '管理组织里已发布的插件', 1, 19500, 1, 'admin', NOW(), 'admin', NOW());


--- 工作表开关表：mlc_app_worksheet_switch
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('1132b2d27690484e6bb9fa3fb9a82aec', 'b1417b921414404198e93638ff47c2ff', 29, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('2463cdd8c114d94ccab45a8b91cada23', 'b1417b921414404198e93638ff47c2ff', 40, 0, 0, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('2cbcc585b611834d637200b51a2825f0', 'b1417b921414404198e93638ff47c2ff', 34, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('32b54fd411929c43ebab63c2c7de998d', 'b1417b921414404198e93638ff47c2ff', 14, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('36c0f05a11edeb41709944ecdf063310', 'b1417b921414404198e93638ff47c2ff', 22, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('3f21678af11d8d4c2bae8e7175973dc6', 'b1417b921414404198e93638ff47c2ff', 38, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('410ae698911ff6406c885f4d36161b3f', 'b1417b921414404198e93638ff47c2ff', 12, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('45eabc6c22011b47eda5136f579c533a', 'b1417b921414404198e93638ff47c2ff', 27, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('5154ba567ba114401c92b1cf3cf3f8fa', 'b1417b921414404198e93638ff47c2ff', 24, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('54d4c8bd26a11a4a6ea8e563ec1e7540', 'b1417b921414404198e93638ff47c2ff', 26, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('57d34a4b8911bb4a82a8d97695409b8e', 'b1417b921414404198e93638ff47c2ff', 35, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('5ab82d16b115754c51edd9fc7aa3243a', 'b1417b921414404198e93638ff47c2ff', 10, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('5bcb07f1121a3f4bbcb9efd9d90943e9', 'b1417b921414404198e93638ff47c2ff', 25, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('712f5f113077bd4cc591f9b122eb32db', 'b1417b921414404198e93638ff47c2ff', 39, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('82f2019d1112c449e3880257acb5f7b5', 'b1417b921414404198e93638ff47c2ff', 11, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('83dec9d67edb11428ca46211713a3de2', 'b1417b921414404198e93638ff47c2ff', 28, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('9914e0fdcfbb4112850f8239d4d479bd', 'b1417b921414404198e93638ff47c2ff', 20, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('995c7cafa7e1443dec22a2abafeced84', 'b1417b921414404198e93638ff47c2ff', 13, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('9f3a865a683a4ba34df52f865e8162ed', 'b1417b921414404198e93638ff47c2ff', 32, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('a1dcba148d31f34ee3dfff51ee1b2a70', 'b1417b921414404198e93638ff47c2ff', 37, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('cbaee389be434d88ab1db599510f9e72', 'b1417b921414404198e93638ff47c2ff', 36, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('d33aa47072144108beedb5a351391e92', 'b1417b921414404198e93638ff47c2ff', 30, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('d3aafc1643264f80954e6820af59acf1', 'b1417b921414404198e93638ff47c2ff', 41, 0, 0, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('dd765a56de674c849c8baacff54b34fa', 'b1417b921414404198e93638ff47c2ff', 33, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('ed8747e712ed48a9833ed43957e6508a', 'b1417b921414404198e93638ff47c2ff', 51, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('ee16e1ecd94543a88b0d5dab51287534', 'b1417b921414404198e93638ff47c2ff', 50, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('eed33c48e4b535587cfa45e3f257055', 'b1417b921414404198e93638ff47c2ff', 52, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('f68deff10f45c0af8913d60f200d5450', 'b1417b921414404198e93638ff47c2ff', 21, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');
INSERT INTO `mlc_application`.`mlc_app_worksheet_switch` (`switch_id`, `worksheet_id`, `type`, `role_type`, `state`, `view`, `view_ids`, `version`, `del_flag`, `create_by`, `created_at`, `update_by`, `updated_at`, `NOP_TENANT_ID`) VALUES ('f8a2372e83121718974220616076b', 'b1417b921414404198e93638ff47c2ff', 23, 0, 1, NULL, NULL, 0, 0, '1', NOW(), '1', NOW(), '1fbcccf92c4e48389bd36cfbef83a43b');