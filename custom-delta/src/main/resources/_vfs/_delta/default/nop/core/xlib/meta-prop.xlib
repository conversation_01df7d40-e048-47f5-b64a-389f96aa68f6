<!--
  - 版权所有 © 晓与行信息技术(天津)有限公司
  -
  - 联系方式：
  - 手机：18500235210
  - 邮箱：<EMAIL>
  -
  - 授权声明：
  - 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
  - 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
  - 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
  -
  - 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
  - 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
  -
  - 请尊重版权，合法使用本软件。
  -->

<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" x:extends="super">
  <tags>

    <domain-list-json-string outputMode="node">
      <attr name="propNode"/>
      <source>
        <prop name="${propNode.getAttr('name')}">
          <schema type="String"/>
          <transformIn>
            if (value instanceof String) {
                // 如果是字符串，并且两侧有引号，去掉引号
                return value.replaceAll("^[\"'](.*?)[\"']$", "$1");
            }

            // 默认是 List 处理成 JSON 字符串
            return $Jackson.toJsonString(value);
          </transformIn>
        </prop>
      </source>
    </domain-list-json-string>

    <domain-csv-list-with-null outputMode="node" x:prototype="domain-csv-list">
      <source x:prototype-override="merge">
        <prop name="${propNode.getAttr('name')}">
          <transformOut>
            import com.mlc.base.core.helper.ConvertExtHelper;
            return ConvertExtHelper.toCsvListWithNull(value);
          </transformOut>
        </prop>
      </source>
    </domain-csv-list-with-null>

  </tags>
</lib>