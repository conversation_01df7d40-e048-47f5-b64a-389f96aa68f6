
<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" x:extends="super" >
  <tags>

    <appendDataAuth outputMode="sql">
      <description>在当前sql-lib中，为SQL语句追加数据权限过滤条件</description>

      <attr name="authName" mandatory="true"/>
      <attr name="sqlLibModel" implicit="true"/>

      <!--
      通过 $scope.getValue("svcCtx") 可以获取到当前请求的上下文信息，
      $scope.getValue("svcCtx").getUserContext().getUserId() 可以直接获取用户id，不用强转
      -->
      <source>
<!--      <c:script>-->
<!--      logInfo("(((authName))):{}", $scope.getValue("svcCtx").getUserContext().getUserId());-->
<!--      logInfo("slot_default.contentValue:{}" , slot_default.contentValue);-->
<!--      </c:script>-->
        <c:script>
          import com.mlc.custom.expand.sql_lib.ExtSqlLibModel;
          const ext = new ExtSqlLibModel();
        </c:script>

        <c:unit>${ext.buildSqlLibAuth(authName, $scope)}</c:unit>
      </source>
    </appendDataAuth>

  </tags>
</lib>