
<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" x:extends="super">
<!--  <tags>-->
<!--    <TenantSupport x:override="replace">-->
<!--      &lt;!&ndash;-->
<!--         _dsl_root为元编程阶段可访问的配置文件根节点-->
<!--      &ndash;&gt;-->
<!--      <attr name="_dsl_root" implicit="true"/>-->

<!--      <description>-->
<!--        明确标注了标注no-tenant标签的表不会启用tenant过滤，例如租户表和用户表缺省情况下标注了no-tenant。-->
<!--        并且设置了 tenantProp 为 projectId-->
<!--      </description>-->

<!--      <source>-->
<!--        <c:script><![CDATA[-->
<!--                for(let entityNode of _dsl_root.childByTag('entities').children){-->
<!--                    if(xpl('thisLib:NotAutoTenant',entityNode))-->
<!--                       continue;-->

<!--                    // 如果没有明确指定是否启用tenant，则缺省启用-->
<!--                    if(entityNode.getAttr('useTenant') == null){-->
<!--                       entityNode.setAttr('useTenant',true);-->
<!--                       entityNode.setAttr('tenantProp', 'projectId');-->
<!--                    }-->
<!--                }-->
<!--              ]]></c:script>-->
<!--      </source>-->
<!--    </TenantSupport>-->
<!--  </tags>-->
</lib>