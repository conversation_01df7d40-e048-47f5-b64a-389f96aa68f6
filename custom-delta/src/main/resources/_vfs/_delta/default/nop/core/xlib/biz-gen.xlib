<lib x:schema="/nop/schema/xlib.xdef" x:extends="super"
  xmlns:x="/nop/schema/xdsl.xdef" xmlns:c="c" xmlns:thisLib="thisLib" xmlns:xdsl="xdsl" xmlns:xpl="xpl">

  <tags>

    <DefaultBizPostExtends outputMode="node">
      <source x:override="append">
        <thisLib:ExpandBoExtMethod/>
      </source>
    </DefaultBizPostExtends>

    <ExpandBoExtMethod outputMode="node">
      <attr name="_dsl_root" implicit="true"/>

      <xdsl:config xpl:ignoreTag="true">
        <c:import from="/custom/delta/xlib/bo-ex.xlib"/>
      </xdsl:config>

      <source>
        <c:script>
          logInfo("init biz");
        </c:script>

        <biz>
          <actions>
          </actions>
        </biz>
      </source>

    </ExpandBoExtMethod>
  </tags>
</lib>