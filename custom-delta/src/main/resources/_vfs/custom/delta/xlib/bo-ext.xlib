<?xml version="1.0" encoding="UTF-8" ?>
<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef"  xmlns:c="c" xmlns:bo-gen="bo-gen"
  xmlns:xpl="xpl" xmlns:thisLib="thisLib" x:extends="/nop/biz/xlib/bo.xlib">
  <tags>

    <findTreePageForlist>
      <attr name="id"/>
      <attr name="ignoreUnknown" optional="true"/>
      <attr name="selection" optional="true" type="io.nop.api.core.beans.FieldSelectionBean"/>
      <attr name="bizObjName" optional="true" />
      <attr name="thisObj" implicit="true"/>
      <attr name="svcCtx" implicit="true"/>

      <description>
        根据id获取实体，会自动验证数据权限
      </description>

      <source>
        <c:script>
          const ret = thisObj.invoke('get',{id,ignoreUnknown},null,svcCtx);
          if(ret and selection)
          inject('nopOrmTemplate').batchLoadSelectionForEntity(ret, selection);
          return ret;
        </c:script>
      </source>
    </findTreePageForlist>

  </tags>

</lib>