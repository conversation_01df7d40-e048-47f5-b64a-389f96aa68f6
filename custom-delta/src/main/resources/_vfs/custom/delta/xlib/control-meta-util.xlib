<!--
  - 版权所有 © 晓与行信息技术(天津)有限公司
  -
  - 联系方式：
  - 手机：18500235210
  - 邮箱：<EMAIL>
  -
  - 授权声明：
  - 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
  - 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
  - 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
  -
  - 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
  - 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
  -
  - 请尊重版权，合法使用本软件。
  -->

<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:xdsl="xdsl" xmlns:xpl="xpl" xmlns:c="c" xmlns:ui="ui">
  <tags>

    <GenControlMeta outputMode="xml">
      <attr name="worksheetControls" />
      <description>
        生成控件元数据相关的扩展配置项
      </description>
      <source>

      </source>
    </GenControlMeta>

    <GetRelateSheetTitle>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <description>
        获取关联表单的标题控件ID
      </description>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.dataSource){
              //   const titleControl = inject('nopBizObjectManager').getBizObject("MlcAppWorksheet")
              //                              .invoke('getWorksheetControl', {worksheetId: findControl.dataSource}, svcCtx)
              const titleControl = inject("com.mlc.application.service.entity.MlcAppWorksheetBizModel")
                                          .getWorksheetControl(findControl.dataSource);
              return titleControl?.controlId;
            }
            return null;
        ]]></c:script>
      </source>
    </GetRelateSheetTitle>


    <GetRelateSheetShowType>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <description>
        获取关联表单的显示类型, 1: 卡片 2: 下拉框  4: 列表 5:标签页
      </description>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 29){
                return findControl.advancedSetting.showtype;
            }
            return null;
          ]]></c:script>
      </source>
    </GetRelateSheetShowType>

    <GetRelationSearchMaxFetchSize>
      <description>
        获取多态关联表单的最大查询数量
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 51){
                return findControl?.advancedSetting?.maxcount;
            }
            return null;
          ]]></c:script>
      </source>
    </GetRelationSearchMaxFetchSize>

    <GetRelationShowControls>
      <description>
        获取关联表单的显示控件,只有在关联表单为卡片或标签页时才有显示控件
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 29){
                 const result = findControl?.showControls;
                 if(result.length == 0) return null;
                 return result.join(",");
            }
            return null;
          ]]></c:script>
      </source>
    </GetRelationShowControls>

    <GetAutoIdControlRule>
      <description>
        获取自动编号控件的规则名称
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetId" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 33){
                 return worksheetId + "_" + findControl?.controlId;
            }
            return null;
          ]]></c:script>
      </source>
    </GetAutoIdControlRule>


    <GetBoadViewGroupSetting>
      <description>
        获取看板视图分组字段
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           import java.util.List;
           import java.util.Map;

           const resultMap = new Map();
           resultMap.put('isStatisticsEmpty', false);

           const findControl = worksheetControls.filter(item => item.controlId.contains(propName))[0];

            // 9: 单选框 10: 多选框 11: 下拉框
            if(findControl?.type == 9 || findControl?.type == 10 || findControl?.type == 11){
                const groupList = findControl.options.map2((item, index) => {
                    return {
                      'key': item.key,
                      'name': item.value,
                      'sort': index,
                    };
                 });
                resultMap.put('groupList', groupList);
                resultMap.put('isBeforeGroup', false);
            }else{
                const result = new List();
                result.add(Map.of('key', findControl?.controlId, 'name', findControl?.controlName));

                resultMap.put('groupList', result);
                resultMap.put('isBeforeGroup', true);
            }

            return resultMap;
          ]]></c:script>
      </source>
    </GetBoadViewGroupSetting>

    <GetNavigateGroupSetting>
      <description>
        获取导航视图分组字段
      </description>
      <attr name="navGroupFilter" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           import java.util.List;
           import java.util.Map;

           const propName = navGroupFilter[0].controlId;
           const findControl = worksheetControls.filter(item => item.controlId.contains(propName))[0];
           logInfo("findControlfindControlfindControlfindControl: {}", findControl.type);

           const resultMap = new Map();
           resultMap.put('controlId', findControl.controlId);
           resultMap.put('isStatisticsEmpty', false);

            // 9: 单选框 10: 多选框 11: 下拉框
            if(findControl?.type == 9 || findControl?.type == 10 || findControl?.type == 11){
                const groupList = findControl.options.map2((item, index) => {
                    return {
                      'key': item.key,
                      'name': item.value,
                      'sort': index,
                    };
                 });
                resultMap.put('groupList', groupList);
                resultMap.put('isBeforeGroup', false);
            }else{
                const result = new List();
                result.add(Map.of('key', findControl?.controlId, 'name', findControl?.controlName));

                resultMap.put('groupList', result);
                resultMap.put('isBeforeGroup', true);
            }

            return resultMap;
          ]]></c:script>
      </source>
    </GetNavigateGroupSetting>
  </tags>

</lib>