<?xml version="1.0" encoding="UTF-8" ?>
<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:c="c">

  <tags>
    <DefaultCommonVar outputMode="node">
      <attr name="_dsl_root" implicit="true"/>
      <source/>
    </DefaultCommonVar>

    <dataDiff outputMode="text">
      <attr name="objMeta" type="io.nop.xlang.xmeta.IObjMeta" optional="true"/>
      <source>
        <c:script>
          logInfo("Hello from X-Script");
        </c:script>
        <c:unit>测试返回值</c:unit>
      </source>
    </dataDiff>

  </tags>
</lib>