/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.expand;

import static com.mlc.base.common.beans.group.GroupItemDataBean.ROWS_NAME;
import static com.mlc.base.common.beans.group.GroupItemDataBean.TOTAL_NUM_NAME;
import static io.nop.biz.BizConstants.BIZ_OBJ_NAME_THIS_OBJ;
import static io.nop.graphql.core.GraphQLConstants.SYS_OPERATION_FETCH_RESULTS;

import com.mlc.base.common.beans.group.GroupItemDataBean;
import com.mlc.base.common.meta.IObjGroupModel;
import com.mlc.custom.CustomErrors;
import com.mlc.base.common.meta.group.ObjGroupItemModel;
import com.mlc.base.common.meta.group.ObjGroupModel;
import com.mlc.custom.graphQL.CustomGraphQLExecutor;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizMutation;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.api.core.annotations.core.Description;
import io.nop.api.core.annotations.core.Name;
import io.nop.api.core.annotations.core.Optional;
import io.nop.api.core.annotations.graphql.GraphQLReturn;
import io.nop.api.core.beans.FieldSelectionBean;
import io.nop.api.core.beans.FilterBeans;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.api.core.beans.query.QueryFieldBean;
import io.nop.api.core.exceptions.NopException;
import io.nop.api.core.util.FutureHelper;
import io.nop.biz.crud.CrudBizModel;
import io.nop.commons.functional.IAsyncFunctionInvoker;
import io.nop.core.context.IServiceContext;
import io.nop.core.reflect.bean.BeanTool;
import io.nop.graphql.core.IGraphQLExecutionContext;
import io.nop.graphql.core.IGraphQLHook;
import io.nop.graphql.core.ast.GraphQLOperationType;
import io.nop.graphql.core.ast.GraphQLType;
import io.nop.graphql.core.engine.GraphQLEngine;
import io.nop.graphql.core.engine.IGraphQLEngine;
import io.nop.graphql.core.engine.IGraphQLExecutor;
import io.nop.graphql.core.parse.GraphQLDocumentParser;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.xlang.xmeta.IObjMeta;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

@Setter
@Slf4j
@BizModel("")
public class ViewCrudBizModel extends CrudBizModel<DynamicOrmEntity> {

    private IGraphQLEngine graphQLEngine;

    private IAsyncFunctionInvoker operationInvoker;

    private IGraphQLHook graphQLHook;


    /**
     * 保存数据并且更新子表数据
     * <p>
     *  {mName=AAAAAA,
     *    shebeiToMany=[
     *        {number=111111, sname=11111, chargeUser=["1"]},
     *        {number=2222, sname=2222, oneToManyLingJian=[{id=7ab81}, {id=dasda2}], chargeUser=["2"]}
     *     ]
     *  }
     * <p>
     * 全量保存，但是增加了针对子表数据的更新 <br>
     * 例如：一对多一般情况下主子表都是新增数据，有种特殊情况：子表数据可能是选择现有数据，这时候就需要更新子表数据 <br>
     * 操作方法：nop-orm/src/main/java/io/nop/orm/session/CascadeFlusher.java:addOwnerJoinProps
     * 操作原理：有了模型信息之后一切根据模型信息可以推导的知识不应该需要显式表达，
     *         子表对象是放到IOrmEntitySet集合中，关联了主表实体作为owner，当 orm 发现子表对象的owner属性是相对主表时，就会自动更新子表对主表的外键关联成主表的主键值
     * 为什么视为全量保存：数据完整性视角，整个主子表数据达到一致性和完整性；事务性视角，整体是一种全量性的覆盖式保存
     * </p>
     */
    @BizMutation
    @GraphQLReturn(bizObjName = BIZ_OBJ_NAME_THIS_OBJ)
    public DynamicOrmEntity saveAndUpdateToMany(@Name("data") Map<String, Object> data, IServiceContext context) {
        return super.save(data, context);
    }

    /**
     * 调用示例:
     * const payload =
     *      `query getResult($query: Map) {
     *        data:caigoumain_iqosm21_view_1be3f9d9a85f4188be319003f97bf970__fetchPolyResult(){
     *           relationLingjian(query: $query){
     *             items{name}
     *           }
     *         }
     *       }`;
     * params = { query: { limit: 10, offset: 0, filter: { "$type": "eq", "name": "name", "value": 123 } } }
     *
     * <p>
     * 灵活性:
     * 动态多对多允许一个实体动态关联多个类型的对象，这种多样性通常超出了数据库外键约束的能力范围。
     * 数据库无法直接支持“一个外键字段动态指向多个表”的关系，因此多态关联的实现依赖于中间表和类型字段。
     * 非严格约束:
     * 数据库通常无法为多态字段（如 taggable_type 和 taggable_id）施加外键约束，因为它们的目标表动态变化。
     * 数据一致性检查（如删除操作时的级联影响）需要在应用层逻辑中完成，而不是数据库的外键规则。
     * </p>
     */
    @Description("查询多态结果")
    @BizQuery
    public Object fetchPolyResult(@Name("fieldSelection") Object fieldSelection, IServiceContext context) {

        FieldSelectionBean selection = (FieldSelectionBean) fieldSelection;
        IGraphQLExecutor executor = new CustomGraphQLExecutor(operationInvoker, graphQLHook, null, graphQLEngine);

        GraphQLType gqlType = new GraphQLDocumentParser().parseType(null, super.getBizObjName());
        IGraphQLExecutionContext gcontext = graphQLEngine.newGraphQLContextFromContext(context);
        ((GraphQLEngine)graphQLEngine).initForReturnType(gcontext, GraphQLOperationType.query,
                                                         SYS_OPERATION_FETCH_RESULTS, null, gqlType, selection);

        CompletionStage<Object> objectCompletionStage = executor.fetchResult(null, gcontext);
        return FutureHelper.syncGet(objectCompletionStage);
    }

    /**
     * 分组查询服务函数，根据 meta 元信息推导出分组查询的结果
     * @param groupId 分组ID
     * @param query 查询条件
     * @param selection 字段选择
     * @param context 上下文
     * @return 分组查询结果
     *
     * 注释：基础meta为根据模型生成的xmeta，而 xmeta 可以作为视图，每个视图都是独立存在的，可以在独立的视图上施加对象级别的转换
     * 最终的形式为 base xmeta + object data, 最终是基于当前视图的产物
     * <p>
     * Object Data => Entity + Meta => Agg(Entity) + Agg(Meta)
     * Selection 作用于基础对象结构字段选择，而分组查询的结果是基于 object data 的结果，所以 selection 作用于 object data
     */
    @BizQuery
    @GraphQLReturn(bizObjName = BIZ_OBJ_NAME_THIS_OBJ)
    public List<GroupItemDataBean<DynamicOrmEntity>> findViewGroup(@Name("groupId") String groupId,
        @Optional @Name("query") QueryBean query, FieldSelectionBean selection, IServiceContext context) {

        QueryBean queryBean = query;
        if (queryBean == null) queryBean = new QueryBean();

        IObjMeta objMeta = getThisObj().requireObjMeta();
        // 是否有分组相关的元信息
        Object viewGroups = objMeta.prop_get(ObjGroupModel.GROUP_MODEL);
        if(viewGroups == null){
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_GROUP_QUERY);
        }

        IObjGroupModel ObjGroupModel = BeanTool.castBeanToType(viewGroups, IObjGroupModel.class);

        Boolean isBeforeGroup = ObjGroupModel.getIsBeforeGroup(); // 是否需要先进行前置分组查询
        if (isBeforeGroup){
            // 前置分组,需要先对要查询的字段进行分组
//            ViewGroupHelper.buildBeforeGroupQuery(objMeta, ObjGroupModel, queryBean.getFilter());
        }

        List<GroupItemDataBean<DynamicOrmEntity>> bodyData = new ArrayList<>();
        for (ObjGroupItemModel groupItemMeta : ObjGroupModel.getGroupItems()) {
            QueryBean cloneQuery = queryBean.cloneInstance();
            TreeBean findFilter = FilterBeans.contains(ObjGroupModel.getGroupId(), groupItemMeta.getKey());
            cloneQuery.addFilter(findFilter);

            GroupItemDataBean<DynamicOrmEntity> groupItemData = new GroupItemDataBean<>();
            BeanUtils.copyProperties(groupItemMeta, groupItemData);
            bodyData.add(groupItemData);

            Long totalNum = 0L;
            if (selection.hasSourceField(TOTAL_NUM_NAME)) {
                QueryBean groupCountQuery = new QueryBean();
                groupCountQuery.setSourceName(getEntityName());
                groupCountQuery.addField(QueryFieldBean.forField(ObjGroupModel.getGroupId()).count().alias(TOTAL_NUM_NAME));
                groupCountQuery.setFilter(findFilter);
                groupCountQuery.addGroupField(ObjGroupModel.getGroupId());

                //todo: 加上数据权限过滤
                Map<String, Object> groupCountMap = orm().findFirstByQuery(groupCountQuery);

                if (groupCountMap != null && (Long) groupCountMap.get(TOTAL_NUM_NAME) > 0) {
                    totalNum = (Long) groupCountMap.get(TOTAL_NUM_NAME);
                    groupItemData.setTotalNum(totalNum);
                }
            }

            if (selection.hasSourceField(ROWS_NAME) && totalNum > 0) {
                List<DynamicOrmEntity> pageData = super.findList(cloneQuery, selection, context);
                if (!CollectionUtils.isEmpty(pageData)) {
                    groupItemData.setRows(pageData);
                }
            }
        }
        return bodyData;
    }

}
