--------------------- 两部查询  ---------------------
SELECT
    count( o.illegal_items ) AS c1
FROM
    vm_violation_registration AS o
WHERE
    illegal_items = '["cc013323-da5b-43fe-9ce6-6a8137f0ac74"]'
GROUP BY
    illegal_items;

SELECT
    *
FROM
    vm_violation_registration AS o
WHERE
    illegal_items = '["cc013323-da5b-43fe-9ce6-6a8137f0ac74"]';


-- 子查询 --
SELECT
    v.illegal_items AS `key`,
    g.totalNum,
    v.rowid,
    v.illegal_items,
    v.violation_date,
    v.ownerid
FROM
    (
        SELECT
            illegal_items AS `key`,
            COUNT(*) AS totalNum
        FROM
            vm_violation_registration
        GROUP BY
            illegal_items
    ) g
        JOIN
    vm_violation_registration v
    ON
        g.key = v.illegal_items
WHERE
    v.illegal_items = '["cc013323-da5b-43fe-9ce6-6a8137f0ac74"]';
--------------------- 两部查询  ---------------------


---------------------  CTE  ---------------------

-- 使用 CTE 进行统计
    WITH grouped_illegal_items AS (
    SELECT
        illegal_items AS `key`,
        COUNT(*) AS totalNum
    FROM
        vm_violation_registration
    GROUP BY
        illegal_items
)
-- 将统计数据和详细数据关联
SELECT
    g.key,
    g.totalNum,
    v.rowid,
    v.illegal_items,
    v.violation_date,
    v.ownerid
FROM
    grouped_illegal_items g
        JOIN
    vm_violation_registration v
    ON
        g.key = v.illegal_items
WHERE v.illegal_items = '["cc013323-da5b-43fe-9ce6-6a8137f0ac74"]'
---------------------  CTE  ---------------------