/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.expand;

import com.mlc.custom.expand.helper.ViewReflectionBizModelBuilder;
import io.nop.api.core.convert.ConvertHelper;
import io.nop.biz.BizConstants;
import io.nop.biz.api.IBizObject;
import io.nop.biz.api.IBizObjectManager;
import io.nop.biz.crud.CrudToolProvider;
import io.nop.biz.impl.BizObjectBuildHelper;
import io.nop.commons.functional.IAsyncFunctionInvoker;
import io.nop.dao.api.IDaoProvider;
import io.nop.dao.txn.ITransactionTemplate;
import io.nop.graphql.core.IGraphQLHook;
import io.nop.graphql.core.biz.IBizObjectQueryProcessorBuilder;
import io.nop.graphql.core.biz.IGraphQLBizInitializer;
import io.nop.graphql.core.biz.IGraphQLBizObject;
import io.nop.graphql.core.engine.IGraphQLEngine;
import io.nop.graphql.core.reflection.GraphQLBizModel;
import io.nop.graphql.core.reflection.GraphQLBizModels;
import io.nop.graphql.core.schema.TypeRegistry;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 扩展 CrudBizModel 的方式和步骤：
 * 1.在 biz-defaults.beans.xml 中初始化 nopBizObjectManager 的时候，会利用 collect-beans 收集 IGraphQLBizInitializer 接口的所有实现类
 * 2.组装成 bizInitializers 的集合注入到 BizObjectManager 中
 * 3.在 io.nop.biz.impl.BizObjectBuilder#buildBizObject 中会调用
 * initializer.initialize，此方法会循环调用并使用 ReflectionBizModelBuilder.INSTANCE.build 找出所有标注了 BizModel、BizMutation、BizQuery 注解的方法，
 * 并且将这些方法按照 Map<String, Method> 的格式收集存储到 GraphQLBizModel 中
 */
@Slf4j
public class ViewCrudBizInitializer implements IGraphQLBizInitializer {

    @Inject
    protected IDaoProvider daoProvider;

    @Inject
    protected ITransactionTemplate transactionTemplate;

    @Inject
    protected IBizObjectManager bizObjectManager;

    @Inject
    protected IGraphQLEngine graphQLEngine;

    @Inject
    protected CrudToolProvider crudToolProvider;

    @Inject
    @Named("nopGraphQLOperationInvoker")
    protected IAsyncFunctionInvoker operationInvoker;

    @Inject
    @Named("nopMetricsGraphQLHook")
    protected IGraphQLHook graphQLHook;

    private ViewCrudBizModel newBizModelBean(IGraphQLBizObject bizObj) {
        ViewCrudBizModel biz = new ViewCrudBizModel();
        biz.setBizObjName(bizObj.getBizObjName());
        biz.setEntityName(bizObj.getEntityName());
        biz.setDaoProvider(daoProvider);
        biz.setTransactionTemplate(transactionTemplate);
        biz.setBizObjectManager(bizObjectManager);
        biz.setGraphQLEngine(graphQLEngine);
        biz.setGraphQLHook(graphQLHook);
        biz.setOperationInvoker(operationInvoker);
        biz.setCrudToolProvider(crudToolProvider);
        return biz;
    }

    @Override
    public void initialize(IGraphQLBizObject bizObj, IBizObjectQueryProcessorBuilder queryProcessorBuilder,
        TypeRegistry typeRegistry, GraphQLBizModels bizModels) {
        log.info("LowCodeCrudBizInitializer initialize");
        Set<String> base = ConvertHelper.toCsvSet(bizObj.getExtAttribute(BizConstants.GRAPHQL_BASE_NAME));
        if (base != null && base.contains("lowCodeCrud")) {

            ViewCrudBizModel bean = newBizModelBean(bizObj);
            GraphQLBizModel bizModel = ViewReflectionBizModelBuilder.INSTANCE.build(
                ((IBizObject) bizObj).getObjMeta(), bean, typeRegistry);
            BizObjectBuildHelper.addDefaultAction(bizObj, bizModel, null);
        }
    }
}
