/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.expand.helper;


import com.mlc.custom.CustomErrors;
import com.mlc.base.common.meta.group.ObjGroupModel;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.exceptions.NopException;
import io.nop.core.lang.sql.SQL;
import io.nop.orm.dao.DaoQueryHelper;
import io.nop.xlang.xmeta.IObjMeta;


public class ViewGroupHelper {

    /**
     * -- 使用 CTE 进行统计
     * WITH grouped_illegal_items AS (
     *     SELECT illegal_items AS `key`, COUNT(*) AS totalNum
     *     FROM vm_violation_registration
     *     GROUP BY illegal_items
     * )
     * -- 将统计数据和详细数据关联
     * SELECT
     *     g.key, g.totalNum, v.rowid,v.illegal_items, v.violator,
     * FROM grouped_illegal_items g
     * JOIN vm_violation_registration v
     * ON g.key = v.illegal_items
     * WHERE v.illegal_items = '["beafd2af-87a3-4d53-998f-c00638fc2be2"]'
     */
    public static SQL.SqlBuilder buildGroupQuerySql(IObjMeta objMeta, ObjGroupModel viewGroup, TreeBean filter) {
        if (viewGroup == null) {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_GROUP_ITEM);
        }

        SQL.SqlBuilder sb = SQL.begin();

        String entityName = objMeta.getEntityName();
        if (entityName == null)
            entityName = objMeta.getName();

        String groupId = viewGroup.getGroupId();

        sb.sql(" with grouped_items as (");
        sb.sql(" select " + groupId + " as keyName, count(*) as totalNum ");
        sb.sql(" from " + entityName + " ");
        sb.sql(" group by " + groupId + " ");
        sb.sql(" ) ");

        sb.sql(" select g.keyName, g.totalNum, v.violator, v.ownerid "); // v.violator, v.ownerid 应该为 selection 选择字段
        sb.sql(" from grouped_items g ");
        sb.sql(" join " + entityName + " v ");
        sb.sql(" on g.keyName = v." + groupId + " ");
        if (filter != null) {
            sb.where();
            DaoQueryHelper.appendFilter(sb, "v", filter);
        }
        return sb;
    }

    public static void buildBeforeGroupQuery(IObjMeta objMeta, ObjGroupModel viewGroup, TreeBean filter) {

    }
}
