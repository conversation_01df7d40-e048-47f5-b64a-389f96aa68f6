package com.mlc.custom.replenish.domain.handler;

import com.mlc.base.common.enums.meta.ControlTypeEnum;
import io.nop.xlang.xdef.domain.CheckStdDomainHandler;
import org.dromara.hutool.core.lang.Validator;


public class ControlDomainHandlers {

    public static class EmailType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return ControlTypeEnum.EMAIL.getDomain();
        }

        @Override
        protected boolean isValid(String text) {
            return Validator.isEmail(text);
        }
    }

    public static class TextType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return ControlTypeEnum.TEXT.getDomain();
        }

        @Override
        protected boolean isValid(String text) {
            // 怎么判断文本类型的数据是否合法？
            return true;
        }
    }

    public static class RelateSheetType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "relateSheet"; //  ControlTypeEnum.RELATE_SHEET.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class SubListType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "subList"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class FlatMenuType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "flatMenu"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class IdCardType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "idCard"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return Validator.isCitizenId(text);
        }
    }

    public static class MobileType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "mobile"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return Validator.isMobile(text);
        }
    }

    public static class LandlineType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "landline"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class MoneyType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "money"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return Validator.isMoney(text);
        }
    }

    public static class MultiSelectType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "multiSelect"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class DateType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "date"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class DateTimeType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "dateTime"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class NumberType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "number"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return Validator.isNumber(text);
        }
    }

    public static class UserPickerType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "userPicker"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class DepartmentType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "department"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class FormulaNumberType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "formulaNumber"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }

    public static class SubTotalType extends CheckStdDomainHandler {
        @Override
        public String getName() {
            return "subTotal"; //  ControlTypeEnum.SUB_LIST.getDomain()，后续等控件模型确定后再修改
        }

        @Override
        protected boolean isValid(String text) {
            return true;
        }
    }
}


