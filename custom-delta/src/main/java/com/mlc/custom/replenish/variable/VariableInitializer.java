package com.mlc.custom.replenish.variable;

import com.mlc.custom.replenish.variable.items.JacksonHelper;
import com.mlc.custom.replenish.variable.items.ValidateHelper;
import io.nop.core.CoreConstants;
import io.nop.core.initialize.ICoreInitializer;
import io.nop.core.lang.eval.global.EvalGlobalRegistry;
import io.nop.core.lang.eval.global.StaticClassGlobalVariableDefinition;


public class VariableInitializer implements ICoreInitializer {

    @Override
    public int order() {
        return CoreConstants.INITIALIZER_PRIORITY_REGISTER_XLANG;
    }

    @Override
    public void initialize() {
        EvalGlobalRegistry.instance().registerVariable("$Validate", new StaticClassGlobalVariableDefinition(
            ValidateHelper.class));

        EvalGlobalRegistry.instance().registerVariable("$Jackson", new StaticClassGlobalVariableDefinition(
            JacksonHelper.class));
    }
}
