package com.mlc.custom.replenish.variable.items;


import io.nop.api.core.annotations.core.Description;
import org.dromara.hutool.core.lang.Validator;


@Description("提供一些常用的验证方法")
public class ValidateHelper {

    public static boolean isEmail(String email) {
        return Validator.isEmail(email);
    }

    public static boolean isMobilePhone(String mobilePhone) {
        return Validator.isMobile(mobilePhone);
    }

}
