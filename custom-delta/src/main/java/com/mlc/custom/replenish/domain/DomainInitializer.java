package com.mlc.custom.replenish.domain;

import com.mlc.custom.replenish.domain.handler.ControlDomainHandlers;
import com.mlc.custom.replenish.domain.handler.CommonDomainHandlers;
import com.mlc.custom.replenish.domain.handler.WorksheetDomainHandlers;
import io.nop.core.CoreConstants;
import io.nop.core.initialize.ICoreInitializer;
import io.nop.xlang.xdef.domain.StdDomainRegistry;


/**
 * 注册业务领域处理器（domain），用于校验和转换业务数据
 */
public class DomainInitializer implements ICoreInitializer {

    @Override
    public int order() {
        return CoreConstants.INITIALIZER_PRIORITY_REGISTER_XLANG;
    }

    @Override
    public void initialize() {
        // ------------------ 控件领域处理器 ------------------
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.EmailType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.RelateSheetType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.SubListType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.TextType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.FlatMenuType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.IdCardType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.MobileType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.LandlineType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.MoneyType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.MultiSelectType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.DateType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.DateTimeType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.NumberType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.UserPickerType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.DepartmentType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.FormulaNumberType());
        StdDomainRegistry.instance().registerStdDomainHandler(new ControlDomainHandlers.SubTotalType());
        // ------------------ 控件领域处理器 ------------------

        StdDomainRegistry.instance().registerStdDomainHandler(new WorksheetDomainHandlers.ConvertComponentList());
        StdDomainRegistry.instance().registerStdDomainHandler(new CommonDomainHandlers.ListJsonStringType());
        StdDomainRegistry.instance().registerStdDomainHandler(new CommonDomainHandlers.CsvListWithNullType());


    }
}
