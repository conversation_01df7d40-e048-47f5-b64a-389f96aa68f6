package com.mlc.custom.transform.query.strategy;

import com.mlc.custom.CustomConstants;
import com.mlc.custom.CustomErrors;
import com.mlc.custom.transform.query.TreeBeanValueTransStrategy;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.api.core.beans.query.QueryFieldBean;
import io.nop.api.core.exceptions.NopException;
import io.nop.api.core.ioc.BeanContainer;
import io.nop.core.lang.sql.SQL;
import io.nop.core.lang.xml.XNode;
import io.nop.core.lang.xml.json.StdJsonToXNodeTransformer;
import io.nop.orm.IOrmSessionFactory;
import io.nop.orm.dao.DaoQueryHelper;
import io.nop.xlang.xmeta.IObjMeta;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component(CustomConstants.FILTER_PREFIX)
public class FilterEqlPrefixTrans implements TreeBeanValueTransStrategy {

    @Override
    public void process(TreeBean treeBean, IObjMeta objMeta) {
        @SuppressWarnings("unchecked")
        Map<String, Object> valueMap = (Map<String, Object>) treeBean.getAttr("value");

        Object tableName = valueMap.get(CustomConstants.TABLE_NAME);
        if (Objects.isNull(tableName)){
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_INPUT);
        }

        QueryBean queryBean = new QueryBean();

        if(valueMap.get("condition") instanceof Map<?,?> conditionMap && !conditionMap.isEmpty()) {
            @SuppressWarnings("unchecked")
            XNode xNode = StdJsonToXNodeTransformer.INSTANCE.transformMap((Map<String, Object>) conditionMap);
            queryBean = xNode.toTreeBean().toQueryBean();
        }

        if(valueMap.get("field") instanceof String fieldStr && !fieldStr.isBlank()) {
            QueryFieldBean queryFieldBean = QueryFieldBean.forField(valueMap.get("field").toString());
            queryBean.addField(queryFieldBean);
        } else {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_INPUT);
        }

        queryBean.setSourceName(tableName.toString());
        SQL assembleSql = DaoQueryHelper.queryToSelectFieldsSql(queryBean, null).end();
        try {
            // 预编译sql,当用户输入 eql 或者业务拼接完 eql 之后传输到后端，提供校验 eql 合规性、权限等
            BeanContainer.getBeanByType(IOrmSessionFactory.class)
                         .compileSql(CustomConstants.PRE_EXECUTION, assembleSql.getText(),
                                     true, null, true, false, true);
        } catch (Exception e){
            log.error("@@filterEql pre-execution sql error, sql is{}, error is {}", assembleSql.getText(), e.getMessage());
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_QUERY);
        }

        treeBean.setAttr("value", assembleSql);
    }
}
