package com.mlc.custom.transform.query;

import com.mlc.custom.transform.query.TreeBeanValueTransStrategy.Selector;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.api.core.ioc.BeanContainer;
import io.nop.biz.api.IBizObject;
import io.nop.biz.crud.IQueryTransformer;
import io.nop.core.context.IServiceContext;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class GlobalQueryTransformerImpl implements IQueryTransformer {

    /**
     * 查询值的抽象 @filter:xxx 和 查询算子抽象 <filter:xxx> 目标是最终表达语义时仅涉及到业务相关的概念，不用管如何去实现，设计的关键是如何表达业务且只表达业务 现有三种变量类型:
     * <p>
     *  1. 以@@var:开头的变量，表示纯变量 </br>
     *  2. 以@@filter开头的变量，表示完整的 QueryBean 语法，例如：{"guide":"@@filterEql", "tableName":"MlcUopUserJob","field":"userId","condition":{"$type":"eq","name":"jobId","value":*}} </br>
     *  3. 以@@derivation开头的变量，表示推导语法，例如：@@derive:userMapper
     * </p>
     */
    @Override
    public void transform(QueryBean filter, String authObjName, String action,
                            IBizObject bizObj, IServiceContext context) {
        log.info("custom made query transformer begin");

        TreeBean withFilter = filter.getFilter();
        if (withFilter == null) {
            return;
        }

        Selector selector = BeanContainer.getBeanByType(TreeBeanValueTransStrategy.Selector.class);

        withFilter.transformChild(null, treeBean -> {
            //  节点的转换条件
            TreeBeanValueTransStrategy transformer =
                selector.chooseTransformer(treeBean.getAttr("value"));

            if (transformer != null) {
                transformer.process(treeBean, bizObj.getObjMeta());
            }
            return treeBean;
        }, true);
    }

}
