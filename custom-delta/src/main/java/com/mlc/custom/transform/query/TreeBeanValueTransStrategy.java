package com.mlc.custom.transform.query;

import com.mlc.custom.CustomConstants;
import io.nop.api.core.beans.TreeBean;
import io.nop.xlang.xmeta.IObjMeta;
import jakarta.annotation.Resource;
import java.util.Map;
import org.springframework.stereotype.Component;

public interface TreeBeanValueTransStrategy {

    void process(TreeBean treeBean, IObjMeta objMeta);

    @Component
    class Selector {

        @Resource
        protected Map<String, TreeBeanValueTransStrategy> transMap;

        public TreeBeanValueTransStrategy chooseTransformer(Object value) {
            if (value == null) {
                return null;
            }

            if (value instanceof String && value.toString().startsWith(CustomConstants.VAR_PREFIX)) {
                return transMap.get(CustomConstants.VAR_PREFIX);
            }
            if (value instanceof String && value.toString().startsWith(CustomConstants.DERIVATION_PREFIX)) {
                return transMap.get(CustomConstants.DERIVATION_PREFIX);
            }
            if (value instanceof Map<?, ?> && ((Map<?, ?>) value).get("guide").equals(CustomConstants.FILTER_PREFIX)) {
                return transMap.get(CustomConstants.FILTER_PREFIX);
            }
            return null;
        }
    }
}
