package com.mlc.custom.transform.query.strategy;

import com.mlc.custom.CustomConstants;
import com.mlc.custom.transform.query.TreeBeanValueTransStrategy;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.ioc.BeanContainer;
import io.nop.orm.impl.OrmTemplateImpl;
import io.nop.orm.model.IEntityModel;
import io.nop.xlang.xmeta.IObjMeta;
import io.nop.xlang.xmeta.IObjPropMeta;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component(CustomConstants.DERIVATION_PREFIX)
public class DerivationPrefixTrans implements TreeBeanValueTransStrategy {

    // docs/dev-guide/biz/validate.md BizObject的get方法可以验证实体的存在性
    @Override
    public void process(TreeBean treeBean, IObjMeta objMeta) {
        String subStringed = treeBean.getAttr("value").toString().substring(CustomConstants.DERIVATION_PREFIX.length());

        IObjPropMeta propMeta = objMeta.requireProp(subStringed);

        OrmTemplateImpl ormTemplate = BeanContainer.getBeanByType(OrmTemplateImpl.class);
        IEntityModel entityModel = ormTemplate.getOrmModel().requireEntityModel(objMeta.getEntityName());

//        List<? extends IEntityRelationModel> relations = entityModel.getRelations();
//        IEntityRelationModel relModel = entityModel.getRelation(relName, true);
//        if(relModel == null) {
//            throw new RuntimeException("relation not found: " + relName);
//        }
//
//        boolean toOneRelation = relModel.isToOneRelation();
    }
}
