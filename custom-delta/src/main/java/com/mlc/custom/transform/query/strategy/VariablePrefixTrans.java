package com.mlc.custom.transform.query.strategy;

import com.mlc.custom.CustomConstants;
import com.mlc.custom.transform.query.TreeBeanValueTransStrategy;
import io.nop.api.core.beans.DictBean;
import io.nop.api.core.beans.DictOptionBean;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.util.Guard;
import io.nop.core.dict.DictModel;
import io.nop.core.lang.eval.IEvalScope;
import io.nop.core.lang.json.JObject;
import io.nop.core.reflect.IClassModel;
import io.nop.core.reflect.ReflectionManager;
import io.nop.core.resource.component.ResourceComponentManager;
import io.nop.xlang.api.AbstractEvalAction;
import io.nop.xlang.api.XLang;
import io.nop.xlang.xmeta.IObjMeta;
import org.springframework.stereotype.Component;


@Component(CustomConstants.VAR_PREFIX)
public class VariablePrefixTrans implements TreeBeanValueTransStrategy {

    @Override
    public void process(TreeBean treeBean, IObjMeta objMeta) {
        String tagName = treeBean.getAttr("value").toString().substring(CustomConstants.VAR_PREFIX.length());

        IEvalScope scope = XLang.newEvalScope();
        scope.setLocalValue("objMeta", objMeta);
        AbstractEvalAction tagAction = XLang.getTagAction(CustomConstants.TAG_ACTION_PATH, tagName, false);

        if (tagAction != null) {
            treeBean.setAttr("value", tagAction.generateText(scope));
            return;
        }

        DictModel model = (DictModel) ResourceComponentManager.instance().loadComponentModel(
            CustomConstants.VAR_EXT_PATH);
        DictBean dictBean = model.getDictBean();
        if (dictBean != null) {
            DictOptionBean optionByValue = dictBean.getOptionByValue(tagName);
            if (optionByValue != null) {
                treeBean.setAttr("value", getTypeValue(optionByValue));
            }
        }
    }

    private static Object getTypeValue(DictOptionBean optionByValue) {
        JObject jObject = (JObject) optionByValue.getAttrs().get("attrs");
        if (jObject == null) {
            return optionByValue.getCode();
        }

        String[] split = optionByValue.getCode().split("#");
        Guard.checkArgument(split.length == 2, "不正确的类名或方法名");

        IClassModel classModel = ReflectionManager.instance().loadClassModel(split[0]);
        if ("class".equals(jObject.get("type"))) {
            return classModel.getStaticField(split[1]).getValue(null);
        }
        if ("staticMethod".equals(jObject.get("type"))) {
            return classModel.getStaticMethod(split[1], 0).call0(null, null);
        }
        return null;
    }
}
