/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.graphQL;

import com.mlc.base.common.beans.group.GroupItemDataBean;
import io.nop.core.type.IGenericType;
import io.nop.graphql.core.ast.GraphQLFieldDefinition;
import io.nop.graphql.core.ast.GraphQLNamedType;
import io.nop.graphql.core.ast.GraphQLObjectDefinition;
import io.nop.graphql.core.ast.GraphQLType;
import io.nop.graphql.core.ast.GraphQLTypeDefinition;
import io.nop.graphql.core.fetcher.BeanPropertyFetcher;
import io.nop.graphql.core.reflection.ReflectionGraphQLTypeFactory;
import io.nop.graphql.core.schema.TypeRegistry;
import io.nop.graphql.core.utils.GraphQLTypeHelper;
import java.util.Map;

public class CustomReflectionGraphQLTypeFactory extends ReflectionGraphQLTypeFactory {

    protected GraphQLType buildViewGroupBeanType(GraphQLType type, TypeRegistry registry,
        Map<String, GraphQLTypeDefinition> creatingTypes, boolean input) {
        String viewGroupTypeName = GroupItemDataBean.OBJ_THIS_NAME + "_" + type;
        GraphQLTypeDefinition objDef = creatingTypes.get(viewGroupTypeName);
        if (objDef == null) {
            objDef = registry.getType(viewGroupTypeName);
            if (objDef == null) {
                objDef = buildViewGroupBeanType(viewGroupTypeName, type, registry, creatingTypes, input);
                registry.registerType(objDef);
            }
        }
        GraphQLNamedType namedType = GraphQLTypeHelper.namedType(viewGroupTypeName);
        namedType.setResolvedType(objDef);
        return namedType;
    }

    protected GraphQLObjectDefinition buildViewGroupBeanType(String typeName, GraphQLType type, TypeRegistry registry,
        Map<String, GraphQLTypeDefinition> creatingTypes, boolean input) {
        GraphQLObjectDefinition objDef = (GraphQLObjectDefinition) buildDef(GroupItemDataBean.OBJ_THIS_NAME, GroupItemDataBean.class,
                                                                            registry, creatingTypes, input);
        objDef = objDef.deepClone();
        creatingTypes.put(typeName, objDef);
        objDef.setName(typeName);
        GraphQLFieldDefinition field = objDef.getField("rows");
        field.setType(GraphQLTypeHelper.listType(type));
        field.setFetcher(BeanPropertyFetcher.INSTANCE);
        return objDef;
    }

    @Override
    protected GraphQLType buildGraphQLType(IGenericType type, String bizObjName, TypeRegistry registry,
        Map<String, GraphQLTypeDefinition> creatingTypes, boolean input) {

        if(type.getRawClass() == GroupItemDataBean.class) {
            IGenericType componentType = type.getTypeParameters().get(0);
            GraphQLType beanType = buildGraphQLType(componentType, bizObjName, registry, creatingTypes, input);
            return buildViewGroupBeanType(beanType, registry, creatingTypes, input);
        }

        return super.buildGraphQLType(type, bizObjName, registry, creatingTypes, input);
    }
}
