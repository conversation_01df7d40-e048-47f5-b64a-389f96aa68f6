/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import io.nop.api.core.ApiConfigs;
import io.nop.api.core.config.AppConfig;
import io.nop.codegen.XCodeGenerator;
import io.nop.core.initialize.CoreInitialization;
import io.nop.core.lang.eval.IEvalScope;
import io.nop.core.resource.IFile;
import io.nop.core.resource.ResourceHelper;
import io.nop.core.resource.tpl.TemplateGenPath;
import io.nop.core.unittest.BaseTestCase;
import io.nop.xlang.api.XLang;
import java.time.LocalDateTime;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class TestXCodeGenerator extends BaseTestCase {
    @BeforeAll
    public static void setUp() {
        AppConfig.getConfigProvider().updateConfigValue(ApiConfigs.CFG_EXCEPTION_FILL_STACKTRACE, true);
        System.out.println("setUp");
        CoreInitialization.initialize();
    }

    @AfterAll
    public static void tearDown() {
        CoreInitialization.destroy();
    }

    @Test
    public void testSubDir() {
        IFile targetDir = getTargetResource("/codegen");
        ResourceHelper.deleteAll(targetDir);
        XCodeGenerator gen = new XCodeGenerator("/test/tpls", targetDir.getStdPath());

        IEvalScope scope = XLang.newEvalScope();
        LocalDateTime time = LocalDateTime.now();
        scope.setLocalValue(null, "currentTime", time);
        scope.setLocalValue(null, "forInt", new int[]{1111, 22222, 3333});

        gen.execute("/{child}", scope);
    }

    @Test
    public void testCopy() {
        IFile targetDir = getTargetResource("/codegen");
        ResourceHelper.deleteAll(targetDir);
        XCodeGenerator gen = new XCodeGenerator("/test/tpls", targetDir.getStdPath());

        IEvalScope scope = XLang.newEvalScope();
        LocalDateTime time = LocalDateTime.now();
        scope.setLocalValue(null, "currentTime", time);


        gen.execute("/", scope);

        assertTrue(targetDir.getResource("other").exists());
        assertTrue(!targetDir.getResource("@init.xrun").exists());
        assertEquals("other", ResourceHelper.readText(targetDir.getResource("other/other.txt")));
    }

    @Test
    public void testGenText() {
        IFile targetDir = getTargetResource("/codegen");
        ResourceHelper.deleteAll(targetDir);
        XCodeGenerator gen = new XCodeGenerator("/test/gen", targetDir.getStdPath());

        IEvalScope scope = XLang.newEvalScope();
        scope.setLocalValue(null, "moduleName", "aa");

        gen.execute("/", scope);

        String text = ResourceHelper.readText(targetDir.getResource("web.i18n.yaml"));
        assertEquals("\n" +
                "\"x:extends\": _aa-web.i18n.yaml\n" +
                "\n" +
                "# key: \"value\"\n", normalizeCRLF(text));
    }

    @Test
    public void testGenPath() {
        TemplateGenPath genPath = new TemplateGenPath();
        IEvalScope scope = XLang.newEvalScope();
        genPath.push("a");
        genPath.resolveTop(scope);
        genPath.push("{deltaDir}");
        genPath.resolveTop(scope);
        genPath.push("test");
        genPath.resolveTop(scope);
        String path = genPath.getTargetPath();
        assertEquals("a/test", path);
    }
}
