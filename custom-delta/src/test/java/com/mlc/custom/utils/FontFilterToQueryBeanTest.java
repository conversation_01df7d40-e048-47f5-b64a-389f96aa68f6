/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.base.common.beans.filter.FrontViewFilterGroupBean;
import com.mlc.base.common.beans.filter.FrontViewFilterNormalBean;
import com.mlc.base.common.utils.convert.FilterConvertSqlContent;
import io.nop.api.core.beans.TreeBean;
import io.nop.core.lang.sql.SQL;
import io.nop.core.lang.xml.XNode;
import io.nop.orm.dao.DaoQueryHelper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
public class FontFilterToQueryBeanTest {

    private static final String TYPE_A = """
        [{
                "controlId": "f0770abbc24148579176254487a2c21a",
                    "dataType": 2,
                    "spliceType": 2,
                    "filterType": 2,
                    "isDynamicsource": false,
                    "dynamicSource": [],
                "values": ["wwwww"],
                "conditionGroupType": 1,
                    "type": 2
            }, {
                "controlId": "d6be0cdfaa1841f48b7f24808c3ae690",
                    "dataType": 2,
                    "spliceType": 2,
                    "filterType": 1,
                    "dateRange": 0,
                    "dateRangeType": 1,
                    "isDynamicsource": false,
                    "dynamicSource": [],
                "values": ["ddddwww"],
                "conditionGroupType": 1,
                    "type": 1
            }, {
                "controlId": "c84083555cc147e8af85ea99f474c501",
                    "dataType": 2,
                    "spliceType": 2,
                    "filterType": 3,
                    "dateRange": 0,
                    "dateRangeType": 1,
                    "isDynamicsource": false,
                    "dynamicSource": [],
                "values": ["eewqeqw"],
                "conditionGroupType": 1,
                    "type": 3
            }]
        """;

    private static final String TYPE_B = """
        
        [{
        	"spliceType": 2,
        	"isGroup": true,
        	"groupFilters": [{
        		"controlId": "ag",
        		"dataType": 2,
        		"spliceType": 2,
        		"filterType": 1,
        		"dateRange": 0,
        		"dateRangeType": 1,
        		"isDynamicsource": false,
        		"dynamicSource": [],
        		"values": ["3333", "333322"],
        		"conditionGroupType": 1,
        		"type": 2
        	}, {
        		"controlId": "ag",
        		"dataType": 2,
        		"spliceType": 2,
        		"filterType": 2,
        		"isDynamicsource": false,
        		"dynamicSource": [],
        		"values": ["312312312"],
        		"conditionGroupType": 1,
        		"type": 2
        	}]
        }, {
        	"spliceType": 2,
        	"isGroup": true,
        	"groupFilters": [{
        		"controlId": "name",
        		"dataType": 2,
        		"spliceType": 1,
        		"filterType": 2,
        		"isDynamicsource": false,
        		"dynamicSource": [],
        		"values": ["renren"],
        		"conditionGroupType": 1,
        		"type": 2
        	}]
        }]
        """;

//    @Test
//    public void nestingFilterTest() throws JsonProcessingException {
//
//        String testStr = TYPE_A;
//        boolean groupFilters = testStr.contains("groupFilters");
//        TreeBean result;
//        List<FrontViewFilterNormalBean> conditions = new ObjectMapper().readValue(testStr, new TypeReference<>() {
//        });
//        if (groupFilters) {
//            result = convertGroup(conditions);
//        } else {
//            result = convertUsually(conditions);
//        }
//        validateTreeBean(result);
//    }
//
//    private static TreeBean convertGroup(List<FrontViewFilterGroupBean> conditions) {
//        TreeBean treeBean = new TreeBean(conditions.get(0).getSpliceTypeStr());
//        conditions.forEach(group -> treeBean.addChild(convertUsually(group.getGroupFilters())));
//        return treeBean;
//    }
//
//    private static TreeBean convertUsually(List<FrontViewFilterNormalBean> conditions) {
//        List<TreeBean> list = conditions.stream()
//                                        .map(FilterConvertSqlContent.INSTANCE::convertSqlBody)
//                                        .toList();
//        TreeBean treeBean = new TreeBean(conditions.get(0).getSpliceTypeStr());
//        list.forEach(treeBean::addChild);
//        return treeBean;
//    }
//
//    public void validateTreeBean(TreeBean result) throws JsonProcessingException {
////        String value = new ObjectMapper().writeValueAsString(result);
////        System.out.println("value--------" + value);
//
//        log.info("xml----------- \n{}", XNode.fromTreeBean(result).xml());
//
//        log.info("treeBean-------- \n{}", new ObjectMapper().writeValueAsString(result.toJsonObject()));
//
//        SQL mlcUopUser = DaoQueryHelper.queryToSelectObjectSql("MlcUopUser", result.toQueryBean());
//        log.info("sql------ \n{}", mlcUopUser);
//    }
}
