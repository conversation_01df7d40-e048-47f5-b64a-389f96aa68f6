/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.utils;

import static io.nop.api.core.beans.FilterBeans.and;
import static io.nop.api.core.beans.FilterBeans.or;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.nop.api.core.beans.FilterBeans;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.core.lang.sql.SQL;
import io.nop.core.lang.xml.XNode;
import io.nop.core.lang.xml.json.StdJsonToXNodeTransformer;
import io.nop.orm.dao.DaoQueryHelper;
import java.util.Map;
import java.util.function.Function;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Disabled
public class TestQueryTransform {

    @Test
    public void spliceSubquery() {

        QueryBean queryBean = new QueryBean();
        queryBean.addFilter(FilterBeans.eq("userId", "12345"));

        TreeBean sqlFilter = new TreeBean();
        sqlFilter.setTagName("sql");
        sqlFilter.setAttr("value", SQL.begin().sql("o.orderNum > '100' ").end());
        queryBean.addFilter(and(sqlFilter));

        Function<TreeBean, Object> ojectFunction = treeBean -> {

            if (treeBean.getChildren().get(0).getAttr("name").equals("userId")) {
                treeBean.getChildren().get(0).setAttr("value", SQL.begin().sql("select a from b where c > '1000' ").end());
            }
            return treeBean;
        };
        queryBean.transformFilter(ojectFunction);
        System.out.println("queryBean::::" + queryBean.getFilter().toJsonObject());
    }


    @Test
    public void toSelectSql() throws JsonProcessingException {

        String condition = """
            {
              "$type": "and",
              "$body": [{
                "$type": "eq",
                "name": "AAAAAA",
                "value": "12345"
              }, {
                "$type": "or",
                "$body": [{
                  "$type": "eq",
                  "name": "BBBBBBB",
                  "value": "admin"
                }, {
                  "$type": "eq",
                  "name": "CCCCCCC",
                  "value": "admin"
                }]
              }, {
                "$type": "eq",
                "name": "VVVVVVV",
                "value": "12345"
              }]
            }
            """;
        Map<String, Object> map = new ObjectMapper().readValue(condition, Map.class);
        XNode xNode = StdJsonToXNodeTransformer.INSTANCE.transformMap(map);
        System.out.println(xNode.toTreeBean());

        SQL sourceName = DaoQueryHelper.queryToSelectObjectSql("MlcUopUser", xNode.toTreeBean().toQueryBean());
        System.out.println("sourceName:::::" + sourceName);
    }

    @Test
    public void testJsonDsl() {

//        String xml = "<dao:FindPage xpl:lib='/nop/orm/xlib/dao.xlib' offset='0' limit='10'> select o from NopAuthUser o where o.id= ${id}</dao:FindPage>";
//
//        XNode node = XNodeParser.instance().parseFromText(null, xml);
//        IEvalAction action = XLang.newCompileTool().allowUnregisteredScopeVar(true).compileTag(node);
//        IEvalScope scope = XLang.newEvalScope();
//        scope.setLocalValue("id", 1);
//
//        List<NopAuthUser> list = (List<NopAuthUser>) action.invoke(scope);
//        assertTrue(!list.get(0).getUserName().isEmpty());
    }

    @Test
    public void testNestingQueryBean() throws JsonProcessingException {

        TreeBean and = and(FilterBeans.eq("AAAAAA", "12345"),
                           or(FilterBeans.eq("BBBBBBB", "admin"), FilterBeans.eq("CCCCCCC", "admin")),
                           FilterBeans.eq("VVVVVVV", "12345"));
        Object jsonObject = and.toJsonObject();
        System.out.println("jsonObject::::" + new ObjectMapper().writeValueAsString(jsonObject));
        SQL mlcUopUser = DaoQueryHelper.queryToSelectObjectSql("MlcUopUser", and.toQueryBean());
        System.out.println("mlcUopUser::::" + mlcUopUser);
    }


}
