/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.custom.mapper;

import com.mlc.custom.expand.helper.ViewReflectionBizModelBuilder;
import io.nop.api.core.convert.ConvertHelper;
import io.nop.biz.BizConstants;
import io.nop.biz.api.IBizObjectManager;
import io.nop.biz.decorator.IActionDecoratorCollector;
import io.nop.biz.impl.BizObjectBuildHelper;
import io.nop.dao.api.IDaoProvider;
import io.nop.dao.txn.ITransactionTemplate;
import io.nop.graphql.core.biz.IBizObjectQueryProcessorBuilder;
import io.nop.graphql.core.biz.IGraphQLBizInitializer;
import io.nop.graphql.core.biz.IGraphQLBizObject;
import io.nop.graphql.core.reflection.GraphQLBizModel;
import io.nop.graphql.core.reflection.GraphQLBizModels;
import io.nop.graphql.core.schema.TypeRegistry;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Set;

public class MappingCrudBizInitializer implements IGraphQLBizInitializer {

    private IDaoProvider daoProvider;
    private ITransactionTemplate transactionTemplate;

    private IBizObjectManager bizObjectManager;

    private List<IActionDecoratorCollector> collectors;

    @Inject
    public void setDaoProvider(IDaoProvider daoProvider) {
        this.daoProvider = daoProvider;
    }

    @Inject
    public void setBizObjectManager(IBizObjectManager bizObjectManager) {
        this.bizObjectManager = bizObjectManager;
    }

    @Inject
    public void setTransactionTemplate(ITransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }

    public void setDecoratorCollectors(List<IActionDecoratorCollector> collectors) {
        this.collectors = collectors;
    }

    private MappingDynamicCrudBizModel newBizModelBean(IGraphQLBizObject bizObj) {
        MappingDynamicCrudBizModel biz = new MappingDynamicCrudBizModel();
        biz.setBizObjName(bizObj.getBizObjName());
        biz.setEntityName(bizObj.getEntityName());
        biz.setDaoProvider(daoProvider);
        biz.setTransactionTemplate(transactionTemplate);
        biz.setBizObjectManager(bizObjectManager);
        return biz;
    }

    @Override
    public void initialize(IGraphQLBizObject bizObj,
                           IBizObjectQueryProcessorBuilder queryProcessorBuilder,
                           TypeRegistry typeRegistry, GraphQLBizModels bizModels) {
        Set<String> base = ConvertHelper.toCsvSet(bizObj.getExtAttribute(BizConstants.GRAPHQL_BASE_NAME));
        if (base != null && base.contains(BizConstants.BASE_CRUD)) {
            MappingDynamicCrudBizModel bean = newBizModelBean(bizObj);
            GraphQLBizModel bizModel = ViewReflectionBizModelBuilder.INSTANCE.build(bean, typeRegistry, bizModels);

            BizObjectBuildHelper.addDefaultAction(bizObj, bizModel, collectors);
            System.out.println("MappingCrudBizInitializer initialize");
        }
    }

}
