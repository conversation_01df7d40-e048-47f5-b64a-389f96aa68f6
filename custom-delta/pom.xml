<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>flowweb-mlc-backend</artifactId>
    <groupId>flowweb-zhongzhi</groupId>
    <version>${revision}</version>
  </parent>

  <artifactId>custom-delta</artifactId>

  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-base-core</artifactId>
    </dependency>
    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-application-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.entropy-cloud</groupId>
      <artifactId>nop-xlang-debugger</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.entropy-cloud</groupId>
      <artifactId>nop-autotest-junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
            </manifest>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>