package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppWorksheetView;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  工作表视图: mlc_app_worksheet_view
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppWorksheetView extends DynamicOrmEntity{
    
    /* 视图 ID: view_id VARCHAR */
    public static final String PROP_NAME_viewId = "viewId";
    public static final int PROP_ID_viewId = 1;
    
    /* 工作表 ID: worksheet_id VARCHAR */
    public static final String PROP_NAME_worksheetId = "worksheetId";
    public static final int PROP_ID_worksheetId = 2;
    
    /* 工作表名称: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 是否已读: un_read BOOLEAN */
    public static final String PROP_NAME_unRead = "unRead";
    public static final int PROP_ID_unRead = 4;
    
    /* 排序字段: sort_cid VARCHAR */
    public static final String PROP_NAME_sortCid = "sortCid";
    public static final int PROP_ID_sortCid = 5;
    
    /* 排序类型: sort_type INTEGER */
    public static final String PROP_NAME_sortType = "sortType";
    public static final int PROP_ID_sortType = 6;
    
    /* 封面字段: cover_cid VARCHAR */
    public static final String PROP_NAME_coverCid = "coverCid";
    public static final int PROP_ID_coverCid = 7;
    
    /* 封面类型: cover_type INTEGER */
    public static final String PROP_NAME_coverType = "coverType";
    public static final int PROP_ID_coverType = 8;
    
    /* 是否配置自定义显示列: custom_display BOOLEAN */
    public static final String PROP_NAME_customDisplay = "customDisplay";
    public static final int PROP_ID_customDisplay = 9;
    
    /* 显示字段: display_controls VARCHAR */
    public static final String PROP_NAME_displayControls = "displayControls";
    public static final int PROP_ID_displayControls = 10;
    
    /* 视图类型: view_type INTEGER */
    public static final String PROP_NAME_viewType = "viewType";
    public static final int PROP_ID_viewType = 11;
    
    /* 层级类型: child_type INTEGER */
    public static final String PROP_NAME_childType = "childType";
    public static final int PROP_ID_childType = 12;
    
    /* 视图维度ID(分组ID): view_control VARCHAR */
    public static final String PROP_NAME_viewControl = "viewControl";
    public static final int PROP_ID_viewControl = 13;
    
    /* 行高: row_height INTEGER */
    public static final String PROP_NAME_rowHeight = "rowHeight";
    public static final int PROP_ID_rowHeight = 14;
    
    /* 显示控件名称: show_control_name BOOLEAN */
    public static final String PROP_NAME_showControlName = "showControlName";
    public static final int PROP_ID_showControlName = 15;
    
    /* Web显示字段: show_controls VARCHAR */
    public static final String PROP_NAME_showControls = "showControls";
    public static final int PROP_ID_showControls = 16;
    
    /* 层级名称: layers_name VARCHAR */
    public static final String PROP_NAME_layersName = "layersName";
    public static final int PROP_ID_layersName = 17;
    
    /* 视图隐藏字段: controls VARCHAR */
    public static final String PROP_NAME_controls = "controls";
    public static final int PROP_ID_controls = 18;
    
    /* 多表层级视图控件: view_controls VARCHAR */
    public static final String PROP_NAME_viewControls = "viewControls";
    public static final int PROP_ID_viewControls = 19;
    
    /* 字段排序: controls_sorts VARCHAR */
    public static final String PROP_NAME_controlsSorts = "controlsSorts";
    public static final int PROP_ID_controlsSorts = 20;
    
    /* 导航分组: nav_group VARCHAR */
    public static final String PROP_NAME_navGroup = "navGroup";
    public static final int PROP_ID_navGroup = 21;
    
    /* 初始过滤: filters VARCHAR */
    public static final String PROP_NAME_filters = "filters";
    public static final int PROP_ID_filters = 22;
    
    /* 快速过滤: fast_filters VARCHAR */
    public static final String PROP_NAME_fastFilters = "fastFilters";
    public static final int PROP_ID_fastFilters = 23;
    
    /* 更多排序: more_sort VARCHAR */
    public static final String PROP_NAME_moreSort = "moreSort";
    public static final int PROP_ID_moreSort = 24;
    
    /* 高级设置: advanced_setting VARCHAR */
    public static final String PROP_NAME_advancedSetting = "advancedSetting";
    public static final int PROP_ID_advancedSetting = 25;
    
    /* 收藏状态: is_marked BOOLEAN */
    public static final String PROP_NAME_isMarked = "isMarked";
    public static final int PROP_ID_isMarked = 26;
    
    /* 排序: order_num INTEGER */
    public static final String PROP_NAME_orderNum = "orderNum";
    public static final int PROP_ID_orderNum = 27;
    
    /* 状态: status INTEGER */
    public static final String PROP_NAME_status = "status";
    public static final int PROP_ID_status = 28;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 29;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 30;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 31;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 32;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 33;
    

    private static int _PROP_ID_BOUND = 34;

    
    /* relation: 所属工作表 */
    public static final String PROP_NAME_worksheet = "worksheet";
    
    /* component:  */
    public static final String PROP_NAME_advancedSettingComponent = "advancedSettingComponent";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_viewId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_viewId};

    private static final String[] PROP_ID_TO_NAME = new String[34];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_viewId] = PROP_NAME_viewId;
          PROP_NAME_TO_ID.put(PROP_NAME_viewId, PROP_ID_viewId);
      
          PROP_ID_TO_NAME[PROP_ID_worksheetId] = PROP_NAME_worksheetId;
          PROP_NAME_TO_ID.put(PROP_NAME_worksheetId, PROP_ID_worksheetId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_unRead] = PROP_NAME_unRead;
          PROP_NAME_TO_ID.put(PROP_NAME_unRead, PROP_ID_unRead);
      
          PROP_ID_TO_NAME[PROP_ID_sortCid] = PROP_NAME_sortCid;
          PROP_NAME_TO_ID.put(PROP_NAME_sortCid, PROP_ID_sortCid);
      
          PROP_ID_TO_NAME[PROP_ID_sortType] = PROP_NAME_sortType;
          PROP_NAME_TO_ID.put(PROP_NAME_sortType, PROP_ID_sortType);
      
          PROP_ID_TO_NAME[PROP_ID_coverCid] = PROP_NAME_coverCid;
          PROP_NAME_TO_ID.put(PROP_NAME_coverCid, PROP_ID_coverCid);
      
          PROP_ID_TO_NAME[PROP_ID_coverType] = PROP_NAME_coverType;
          PROP_NAME_TO_ID.put(PROP_NAME_coverType, PROP_ID_coverType);
      
          PROP_ID_TO_NAME[PROP_ID_customDisplay] = PROP_NAME_customDisplay;
          PROP_NAME_TO_ID.put(PROP_NAME_customDisplay, PROP_ID_customDisplay);
      
          PROP_ID_TO_NAME[PROP_ID_displayControls] = PROP_NAME_displayControls;
          PROP_NAME_TO_ID.put(PROP_NAME_displayControls, PROP_ID_displayControls);
      
          PROP_ID_TO_NAME[PROP_ID_viewType] = PROP_NAME_viewType;
          PROP_NAME_TO_ID.put(PROP_NAME_viewType, PROP_ID_viewType);
      
          PROP_ID_TO_NAME[PROP_ID_childType] = PROP_NAME_childType;
          PROP_NAME_TO_ID.put(PROP_NAME_childType, PROP_ID_childType);
      
          PROP_ID_TO_NAME[PROP_ID_viewControl] = PROP_NAME_viewControl;
          PROP_NAME_TO_ID.put(PROP_NAME_viewControl, PROP_ID_viewControl);
      
          PROP_ID_TO_NAME[PROP_ID_rowHeight] = PROP_NAME_rowHeight;
          PROP_NAME_TO_ID.put(PROP_NAME_rowHeight, PROP_ID_rowHeight);
      
          PROP_ID_TO_NAME[PROP_ID_showControlName] = PROP_NAME_showControlName;
          PROP_NAME_TO_ID.put(PROP_NAME_showControlName, PROP_ID_showControlName);
      
          PROP_ID_TO_NAME[PROP_ID_showControls] = PROP_NAME_showControls;
          PROP_NAME_TO_ID.put(PROP_NAME_showControls, PROP_ID_showControls);
      
          PROP_ID_TO_NAME[PROP_ID_layersName] = PROP_NAME_layersName;
          PROP_NAME_TO_ID.put(PROP_NAME_layersName, PROP_ID_layersName);
      
          PROP_ID_TO_NAME[PROP_ID_controls] = PROP_NAME_controls;
          PROP_NAME_TO_ID.put(PROP_NAME_controls, PROP_ID_controls);
      
          PROP_ID_TO_NAME[PROP_ID_viewControls] = PROP_NAME_viewControls;
          PROP_NAME_TO_ID.put(PROP_NAME_viewControls, PROP_ID_viewControls);
      
          PROP_ID_TO_NAME[PROP_ID_controlsSorts] = PROP_NAME_controlsSorts;
          PROP_NAME_TO_ID.put(PROP_NAME_controlsSorts, PROP_ID_controlsSorts);
      
          PROP_ID_TO_NAME[PROP_ID_navGroup] = PROP_NAME_navGroup;
          PROP_NAME_TO_ID.put(PROP_NAME_navGroup, PROP_ID_navGroup);
      
          PROP_ID_TO_NAME[PROP_ID_filters] = PROP_NAME_filters;
          PROP_NAME_TO_ID.put(PROP_NAME_filters, PROP_ID_filters);
      
          PROP_ID_TO_NAME[PROP_ID_fastFilters] = PROP_NAME_fastFilters;
          PROP_NAME_TO_ID.put(PROP_NAME_fastFilters, PROP_ID_fastFilters);
      
          PROP_ID_TO_NAME[PROP_ID_moreSort] = PROP_NAME_moreSort;
          PROP_NAME_TO_ID.put(PROP_NAME_moreSort, PROP_ID_moreSort);
      
          PROP_ID_TO_NAME[PROP_ID_advancedSetting] = PROP_NAME_advancedSetting;
          PROP_NAME_TO_ID.put(PROP_NAME_advancedSetting, PROP_ID_advancedSetting);
      
          PROP_ID_TO_NAME[PROP_ID_isMarked] = PROP_NAME_isMarked;
          PROP_NAME_TO_ID.put(PROP_NAME_isMarked, PROP_ID_isMarked);
      
          PROP_ID_TO_NAME[PROP_ID_orderNum] = PROP_NAME_orderNum;
          PROP_NAME_TO_ID.put(PROP_NAME_orderNum, PROP_ID_orderNum);
      
          PROP_ID_TO_NAME[PROP_ID_status] = PROP_NAME_status;
          PROP_NAME_TO_ID.put(PROP_NAME_status, PROP_ID_status);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 视图 ID: view_id */
    private java.lang.String _viewId;
    
    /* 工作表 ID: worksheet_id */
    private java.lang.String _worksheetId;
    
    /* 工作表名称: name */
    private java.lang.String _name;
    
    /* 是否已读: un_read */
    private java.lang.Boolean _unRead;
    
    /* 排序字段: sort_cid */
    private java.lang.String _sortCid;
    
    /* 排序类型: sort_type */
    private java.lang.Integer _sortType;
    
    /* 封面字段: cover_cid */
    private java.lang.String _coverCid;
    
    /* 封面类型: cover_type */
    private java.lang.Integer _coverType;
    
    /* 是否配置自定义显示列: custom_display */
    private java.lang.Boolean _customDisplay;
    
    /* 显示字段: display_controls */
    private java.lang.String _displayControls;
    
    /* 视图类型: view_type */
    private java.lang.Integer _viewType;
    
    /* 层级类型: child_type */
    private java.lang.Integer _childType;
    
    /* 视图维度ID(分组ID): view_control */
    private java.lang.String _viewControl;
    
    /* 行高: row_height */
    private java.lang.Integer _rowHeight;
    
    /* 显示控件名称: show_control_name */
    private java.lang.Boolean _showControlName;
    
    /* Web显示字段: show_controls */
    private java.lang.String _showControls;
    
    /* 层级名称: layers_name */
    private java.lang.String _layersName;
    
    /* 视图隐藏字段: controls */
    private java.lang.String _controls;
    
    /* 多表层级视图控件: view_controls */
    private java.lang.String _viewControls;
    
    /* 字段排序: controls_sorts */
    private java.lang.String _controlsSorts;
    
    /* 导航分组: nav_group */
    private java.lang.String _navGroup;
    
    /* 初始过滤: filters */
    private java.lang.String _filters;
    
    /* 快速过滤: fast_filters */
    private java.lang.String _fastFilters;
    
    /* 更多排序: more_sort */
    private java.lang.String _moreSort;
    
    /* 高级设置: advanced_setting */
    private java.lang.String _advancedSetting;
    
    /* 收藏状态: is_marked */
    private java.lang.Boolean _isMarked;
    
    /* 排序: order_num */
    private java.lang.Integer _orderNum;
    
    /* 状态: status */
    private java.lang.Integer _status;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppWorksheetView(){
        // for debug
    }

    protected MlcAppWorksheetView newInstance(){
        MlcAppWorksheetView entity = new MlcAppWorksheetView();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppWorksheetView cloneInstance() {
        MlcAppWorksheetView entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppWorksheetView";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_viewId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_viewId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_viewId:
               return getViewId();
        
            case PROP_ID_worksheetId:
               return getWorksheetId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_unRead:
               return getUnRead();
        
            case PROP_ID_sortCid:
               return getSortCid();
        
            case PROP_ID_sortType:
               return getSortType();
        
            case PROP_ID_coverCid:
               return getCoverCid();
        
            case PROP_ID_coverType:
               return getCoverType();
        
            case PROP_ID_customDisplay:
               return getCustomDisplay();
        
            case PROP_ID_displayControls:
               return getDisplayControls();
        
            case PROP_ID_viewType:
               return getViewType();
        
            case PROP_ID_childType:
               return getChildType();
        
            case PROP_ID_viewControl:
               return getViewControl();
        
            case PROP_ID_rowHeight:
               return getRowHeight();
        
            case PROP_ID_showControlName:
               return getShowControlName();
        
            case PROP_ID_showControls:
               return getShowControls();
        
            case PROP_ID_layersName:
               return getLayersName();
        
            case PROP_ID_controls:
               return getControls();
        
            case PROP_ID_viewControls:
               return getViewControls();
        
            case PROP_ID_controlsSorts:
               return getControlsSorts();
        
            case PROP_ID_navGroup:
               return getNavGroup();
        
            case PROP_ID_filters:
               return getFilters();
        
            case PROP_ID_fastFilters:
               return getFastFilters();
        
            case PROP_ID_moreSort:
               return getMoreSort();
        
            case PROP_ID_advancedSetting:
               return getAdvancedSetting();
        
            case PROP_ID_isMarked:
               return getIsMarked();
        
            case PROP_ID_orderNum:
               return getOrderNum();
        
            case PROP_ID_status:
               return getStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_viewId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_viewId));
               }
               setViewId(typedValue);
               break;
            }
        
            case PROP_ID_worksheetId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_worksheetId));
               }
               setWorksheetId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_unRead:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_unRead));
               }
               setUnRead(typedValue);
               break;
            }
        
            case PROP_ID_sortCid:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_sortCid));
               }
               setSortCid(typedValue);
               break;
            }
        
            case PROP_ID_sortType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_sortType));
               }
               setSortType(typedValue);
               break;
            }
        
            case PROP_ID_coverCid:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_coverCid));
               }
               setCoverCid(typedValue);
               break;
            }
        
            case PROP_ID_coverType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_coverType));
               }
               setCoverType(typedValue);
               break;
            }
        
            case PROP_ID_customDisplay:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_customDisplay));
               }
               setCustomDisplay(typedValue);
               break;
            }
        
            case PROP_ID_displayControls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_displayControls));
               }
               setDisplayControls(typedValue);
               break;
            }
        
            case PROP_ID_viewType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_viewType));
               }
               setViewType(typedValue);
               break;
            }
        
            case PROP_ID_childType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_childType));
               }
               setChildType(typedValue);
               break;
            }
        
            case PROP_ID_viewControl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_viewControl));
               }
               setViewControl(typedValue);
               break;
            }
        
            case PROP_ID_rowHeight:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_rowHeight));
               }
               setRowHeight(typedValue);
               break;
            }
        
            case PROP_ID_showControlName:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_showControlName));
               }
               setShowControlName(typedValue);
               break;
            }
        
            case PROP_ID_showControls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_showControls));
               }
               setShowControls(typedValue);
               break;
            }
        
            case PROP_ID_layersName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_layersName));
               }
               setLayersName(typedValue);
               break;
            }
        
            case PROP_ID_controls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_controls));
               }
               setControls(typedValue);
               break;
            }
        
            case PROP_ID_viewControls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_viewControls));
               }
               setViewControls(typedValue);
               break;
            }
        
            case PROP_ID_controlsSorts:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_controlsSorts));
               }
               setControlsSorts(typedValue);
               break;
            }
        
            case PROP_ID_navGroup:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_navGroup));
               }
               setNavGroup(typedValue);
               break;
            }
        
            case PROP_ID_filters:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_filters));
               }
               setFilters(typedValue);
               break;
            }
        
            case PROP_ID_fastFilters:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_fastFilters));
               }
               setFastFilters(typedValue);
               break;
            }
        
            case PROP_ID_moreSort:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_moreSort));
               }
               setMoreSort(typedValue);
               break;
            }
        
            case PROP_ID_advancedSetting:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_advancedSetting));
               }
               setAdvancedSetting(typedValue);
               break;
            }
        
            case PROP_ID_isMarked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isMarked));
               }
               setIsMarked(typedValue);
               break;
            }
        
            case PROP_ID_orderNum:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_orderNum));
               }
               setOrderNum(typedValue);
               break;
            }
        
            case PROP_ID_status:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_status));
               }
               setStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_viewId:{
               onInitProp(propId);
               this._viewId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_worksheetId:{
               onInitProp(propId);
               this._worksheetId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_unRead:{
               onInitProp(propId);
               this._unRead = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_sortCid:{
               onInitProp(propId);
               this._sortCid = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_sortType:{
               onInitProp(propId);
               this._sortType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_coverCid:{
               onInitProp(propId);
               this._coverCid = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_coverType:{
               onInitProp(propId);
               this._coverType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_customDisplay:{
               onInitProp(propId);
               this._customDisplay = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_displayControls:{
               onInitProp(propId);
               this._displayControls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_viewType:{
               onInitProp(propId);
               this._viewType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_childType:{
               onInitProp(propId);
               this._childType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_viewControl:{
               onInitProp(propId);
               this._viewControl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_rowHeight:{
               onInitProp(propId);
               this._rowHeight = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_showControlName:{
               onInitProp(propId);
               this._showControlName = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_showControls:{
               onInitProp(propId);
               this._showControls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_layersName:{
               onInitProp(propId);
               this._layersName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_controls:{
               onInitProp(propId);
               this._controls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_viewControls:{
               onInitProp(propId);
               this._viewControls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_controlsSorts:{
               onInitProp(propId);
               this._controlsSorts = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_navGroup:{
               onInitProp(propId);
               this._navGroup = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_filters:{
               onInitProp(propId);
               this._filters = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_fastFilters:{
               onInitProp(propId);
               this._fastFilters = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_moreSort:{
               onInitProp(propId);
               this._moreSort = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_advancedSetting:{
               onInitProp(propId);
               this._advancedSetting = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_isMarked:{
               onInitProp(propId);
               this._isMarked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_orderNum:{
               onInitProp(propId);
               this._orderNum = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_status:{
               onInitProp(propId);
               this._status = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 视图 ID: view_id
     */
    public final java.lang.String getViewId(){
         onPropGet(PROP_ID_viewId);
         return _viewId;
    }

    /**
     * 视图 ID: view_id
     */
    public final void setViewId(java.lang.String value){
        if(onPropSet(PROP_ID_viewId,value)){
            this._viewId = value;
            internalClearRefs(PROP_ID_viewId);
            orm_id();
        }
    }
    
    /**
     * 工作表 ID: worksheet_id
     */
    public final java.lang.String getWorksheetId(){
         onPropGet(PROP_ID_worksheetId);
         return _worksheetId;
    }

    /**
     * 工作表 ID: worksheet_id
     */
    public final void setWorksheetId(java.lang.String value){
        if(onPropSet(PROP_ID_worksheetId,value)){
            this._worksheetId = value;
            internalClearRefs(PROP_ID_worksheetId);
            
        }
    }
    
    /**
     * 工作表名称: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 工作表名称: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 是否已读: un_read
     */
    public final java.lang.Boolean getUnRead(){
         onPropGet(PROP_ID_unRead);
         return _unRead;
    }

    /**
     * 是否已读: un_read
     */
    public final void setUnRead(java.lang.Boolean value){
        if(onPropSet(PROP_ID_unRead,value)){
            this._unRead = value;
            internalClearRefs(PROP_ID_unRead);
            
        }
    }
    
    /**
     * 排序字段: sort_cid
     */
    public final java.lang.String getSortCid(){
         onPropGet(PROP_ID_sortCid);
         return _sortCid;
    }

    /**
     * 排序字段: sort_cid
     */
    public final void setSortCid(java.lang.String value){
        if(onPropSet(PROP_ID_sortCid,value)){
            this._sortCid = value;
            internalClearRefs(PROP_ID_sortCid);
            
        }
    }
    
    /**
     * 排序类型: sort_type
     */
    public final java.lang.Integer getSortType(){
         onPropGet(PROP_ID_sortType);
         return _sortType;
    }

    /**
     * 排序类型: sort_type
     */
    public final void setSortType(java.lang.Integer value){
        if(onPropSet(PROP_ID_sortType,value)){
            this._sortType = value;
            internalClearRefs(PROP_ID_sortType);
            
        }
    }
    
    /**
     * 封面字段: cover_cid
     */
    public final java.lang.String getCoverCid(){
         onPropGet(PROP_ID_coverCid);
         return _coverCid;
    }

    /**
     * 封面字段: cover_cid
     */
    public final void setCoverCid(java.lang.String value){
        if(onPropSet(PROP_ID_coverCid,value)){
            this._coverCid = value;
            internalClearRefs(PROP_ID_coverCid);
            
        }
    }
    
    /**
     * 封面类型: cover_type
     */
    public final java.lang.Integer getCoverType(){
         onPropGet(PROP_ID_coverType);
         return _coverType;
    }

    /**
     * 封面类型: cover_type
     */
    public final void setCoverType(java.lang.Integer value){
        if(onPropSet(PROP_ID_coverType,value)){
            this._coverType = value;
            internalClearRefs(PROP_ID_coverType);
            
        }
    }
    
    /**
     * 是否配置自定义显示列: custom_display
     */
    public final java.lang.Boolean getCustomDisplay(){
         onPropGet(PROP_ID_customDisplay);
         return _customDisplay;
    }

    /**
     * 是否配置自定义显示列: custom_display
     */
    public final void setCustomDisplay(java.lang.Boolean value){
        if(onPropSet(PROP_ID_customDisplay,value)){
            this._customDisplay = value;
            internalClearRefs(PROP_ID_customDisplay);
            
        }
    }
    
    /**
     * 显示字段: display_controls
     */
    public final java.lang.String getDisplayControls(){
         onPropGet(PROP_ID_displayControls);
         return _displayControls;
    }

    /**
     * 显示字段: display_controls
     */
    public final void setDisplayControls(java.lang.String value){
        if(onPropSet(PROP_ID_displayControls,value)){
            this._displayControls = value;
            internalClearRefs(PROP_ID_displayControls);
            
        }
    }
    
    /**
     * 视图类型: view_type
     */
    public final java.lang.Integer getViewType(){
         onPropGet(PROP_ID_viewType);
         return _viewType;
    }

    /**
     * 视图类型: view_type
     */
    public final void setViewType(java.lang.Integer value){
        if(onPropSet(PROP_ID_viewType,value)){
            this._viewType = value;
            internalClearRefs(PROP_ID_viewType);
            
        }
    }
    
    /**
     * 层级类型: child_type
     */
    public final java.lang.Integer getChildType(){
         onPropGet(PROP_ID_childType);
         return _childType;
    }

    /**
     * 层级类型: child_type
     */
    public final void setChildType(java.lang.Integer value){
        if(onPropSet(PROP_ID_childType,value)){
            this._childType = value;
            internalClearRefs(PROP_ID_childType);
            
        }
    }
    
    /**
     * 视图维度ID(分组ID): view_control
     */
    public final java.lang.String getViewControl(){
         onPropGet(PROP_ID_viewControl);
         return _viewControl;
    }

    /**
     * 视图维度ID(分组ID): view_control
     */
    public final void setViewControl(java.lang.String value){
        if(onPropSet(PROP_ID_viewControl,value)){
            this._viewControl = value;
            internalClearRefs(PROP_ID_viewControl);
            
        }
    }
    
    /**
     * 行高: row_height
     */
    public final java.lang.Integer getRowHeight(){
         onPropGet(PROP_ID_rowHeight);
         return _rowHeight;
    }

    /**
     * 行高: row_height
     */
    public final void setRowHeight(java.lang.Integer value){
        if(onPropSet(PROP_ID_rowHeight,value)){
            this._rowHeight = value;
            internalClearRefs(PROP_ID_rowHeight);
            
        }
    }
    
    /**
     * 显示控件名称: show_control_name
     */
    public final java.lang.Boolean getShowControlName(){
         onPropGet(PROP_ID_showControlName);
         return _showControlName;
    }

    /**
     * 显示控件名称: show_control_name
     */
    public final void setShowControlName(java.lang.Boolean value){
        if(onPropSet(PROP_ID_showControlName,value)){
            this._showControlName = value;
            internalClearRefs(PROP_ID_showControlName);
            
        }
    }
    
    /**
     * Web显示字段: show_controls
     */
    public final java.lang.String getShowControls(){
         onPropGet(PROP_ID_showControls);
         return _showControls;
    }

    /**
     * Web显示字段: show_controls
     */
    public final void setShowControls(java.lang.String value){
        if(onPropSet(PROP_ID_showControls,value)){
            this._showControls = value;
            internalClearRefs(PROP_ID_showControls);
            
        }
    }
    
    /**
     * 层级名称: layers_name
     */
    public final java.lang.String getLayersName(){
         onPropGet(PROP_ID_layersName);
         return _layersName;
    }

    /**
     * 层级名称: layers_name
     */
    public final void setLayersName(java.lang.String value){
        if(onPropSet(PROP_ID_layersName,value)){
            this._layersName = value;
            internalClearRefs(PROP_ID_layersName);
            
        }
    }
    
    /**
     * 视图隐藏字段: controls
     */
    public final java.lang.String getControls(){
         onPropGet(PROP_ID_controls);
         return _controls;
    }

    /**
     * 视图隐藏字段: controls
     */
    public final void setControls(java.lang.String value){
        if(onPropSet(PROP_ID_controls,value)){
            this._controls = value;
            internalClearRefs(PROP_ID_controls);
            
        }
    }
    
    /**
     * 多表层级视图控件: view_controls
     */
    public final java.lang.String getViewControls(){
         onPropGet(PROP_ID_viewControls);
         return _viewControls;
    }

    /**
     * 多表层级视图控件: view_controls
     */
    public final void setViewControls(java.lang.String value){
        if(onPropSet(PROP_ID_viewControls,value)){
            this._viewControls = value;
            internalClearRefs(PROP_ID_viewControls);
            
        }
    }
    
    /**
     * 字段排序: controls_sorts
     */
    public final java.lang.String getControlsSorts(){
         onPropGet(PROP_ID_controlsSorts);
         return _controlsSorts;
    }

    /**
     * 字段排序: controls_sorts
     */
    public final void setControlsSorts(java.lang.String value){
        if(onPropSet(PROP_ID_controlsSorts,value)){
            this._controlsSorts = value;
            internalClearRefs(PROP_ID_controlsSorts);
            
        }
    }
    
    /**
     * 导航分组: nav_group
     */
    public final java.lang.String getNavGroup(){
         onPropGet(PROP_ID_navGroup);
         return _navGroup;
    }

    /**
     * 导航分组: nav_group
     */
    public final void setNavGroup(java.lang.String value){
        if(onPropSet(PROP_ID_navGroup,value)){
            this._navGroup = value;
            internalClearRefs(PROP_ID_navGroup);
            
        }
    }
    
    /**
     * 初始过滤: filters
     */
    public final java.lang.String getFilters(){
         onPropGet(PROP_ID_filters);
         return _filters;
    }

    /**
     * 初始过滤: filters
     */
    public final void setFilters(java.lang.String value){
        if(onPropSet(PROP_ID_filters,value)){
            this._filters = value;
            internalClearRefs(PROP_ID_filters);
            
        }
    }
    
    /**
     * 快速过滤: fast_filters
     */
    public final java.lang.String getFastFilters(){
         onPropGet(PROP_ID_fastFilters);
         return _fastFilters;
    }

    /**
     * 快速过滤: fast_filters
     */
    public final void setFastFilters(java.lang.String value){
        if(onPropSet(PROP_ID_fastFilters,value)){
            this._fastFilters = value;
            internalClearRefs(PROP_ID_fastFilters);
            
        }
    }
    
    /**
     * 更多排序: more_sort
     */
    public final java.lang.String getMoreSort(){
         onPropGet(PROP_ID_moreSort);
         return _moreSort;
    }

    /**
     * 更多排序: more_sort
     */
    public final void setMoreSort(java.lang.String value){
        if(onPropSet(PROP_ID_moreSort,value)){
            this._moreSort = value;
            internalClearRefs(PROP_ID_moreSort);
            
        }
    }
    
    /**
     * 高级设置: advanced_setting
     */
    public final java.lang.String getAdvancedSetting(){
         onPropGet(PROP_ID_advancedSetting);
         return _advancedSetting;
    }

    /**
     * 高级设置: advanced_setting
     */
    public final void setAdvancedSetting(java.lang.String value){
        if(onPropSet(PROP_ID_advancedSetting,value)){
            this._advancedSetting = value;
            internalClearRefs(PROP_ID_advancedSetting);
            
        }
    }
    
    /**
     * 收藏状态: is_marked
     */
    public final java.lang.Boolean getIsMarked(){
         onPropGet(PROP_ID_isMarked);
         return _isMarked;
    }

    /**
     * 收藏状态: is_marked
     */
    public final void setIsMarked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isMarked,value)){
            this._isMarked = value;
            internalClearRefs(PROP_ID_isMarked);
            
        }
    }
    
    /**
     * 排序: order_num
     */
    public final java.lang.Integer getOrderNum(){
         onPropGet(PROP_ID_orderNum);
         return _orderNum;
    }

    /**
     * 排序: order_num
     */
    public final void setOrderNum(java.lang.Integer value){
        if(onPropSet(PROP_ID_orderNum,value)){
            this._orderNum = value;
            internalClearRefs(PROP_ID_orderNum);
            
        }
    }
    
    /**
     * 状态: status
     */
    public final java.lang.Integer getStatus(){
         onPropGet(PROP_ID_status);
         return _status;
    }

    /**
     * 状态: status
     */
    public final void setStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_status,value)){
            this._status = value;
            internalClearRefs(PROP_ID_status);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 所属工作表
     */
    public final com.mlc.application.dao.entity.MlcAppWorksheet getWorksheet(){
       return (com.mlc.application.dao.entity.MlcAppWorksheet)internalGetRefEntity(PROP_NAME_worksheet);
    }

    public final void setWorksheet(com.mlc.application.dao.entity.MlcAppWorksheet refEntity){
   
           if(refEntity == null){
           
                   this.setWorksheetId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_worksheet, refEntity,()->{
           
                           this.setWorksheetId(refEntity.getWorksheetId());
                       
           });
           }
       
    }
       
   private io.nop.orm.component.JsonOrmComponent _advancedSettingComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_advancedSettingComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_advancedSettingComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_advancedSetting);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getAdvancedSettingComponent(){
      if(_advancedSettingComponent == null){
          _advancedSettingComponent = new io.nop.orm.component.JsonOrmComponent();
          _advancedSettingComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_advancedSettingComponent);
      }
      return _advancedSettingComponent;
   }

}
// resume CPD analysis - CPD-ON
