package com.mlc.application.dao.entity;

import com.mlc.base.common.enums.application.AppPermissionOperationEnum;
import io.nop.api.core.annotations.biz.BizObjName;
import com.mlc.application.dao.entity._gen._MlcAppRoleResource;


@BizObjName("MlcAppRoleResource")
public class MlcAppRoleResource extends _MlcAppRoleResource{

    public MlcAppRoleResource initRoleResource(String roleId, String resourceId, AppPermissionOperationEnum operationEnum) {
        setRoleId(roleId);
        setResourceId(resourceId);
        setOperation(operationEnum.getValue());
        return this;
    }
}
