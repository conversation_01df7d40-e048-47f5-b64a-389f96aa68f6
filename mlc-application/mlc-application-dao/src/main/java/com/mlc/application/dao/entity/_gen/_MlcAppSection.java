package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppSection;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  应用分组: mlc_app_section
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppSection extends DynamicOrmEntity{
    
    /* 应用分组ID: app_section_id VARCHAR */
    public static final String PROP_NAME_appSectionId = "appSectionId";
    public static final int PROP_ID_appSectionId = 1;
    
    /* 应用ID: app_id VARCHAR */
    public static final String PROP_NAME_appId = "appId";
    public static final int PROP_ID_appId = 2;
    
    /* 应用名: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 父级ID: parent_id VARCHAR */
    public static final String PROP_NAME_parentId = "parentId";
    public static final int PROP_ID_parentId = 4;
    
    /* 图标: icon VARCHAR */
    public static final String PROP_NAME_icon = "icon";
    public static final int PROP_ID_icon = 5;
    
    /* 图标路径: icon_url VARCHAR */
    public static final String PROP_NAME_iconUrl = "iconUrl";
    public static final int PROP_ID_iconUrl = 6;
    
    /* 锁定状态: is_lock BOOLEAN */
    public static final String PROP_NAME_isLock = "isLock";
    public static final int PROP_ID_isLock = 7;
    
    /* 维护状态: fixed BOOLEAN */
    public static final String PROP_NAME_fixed = "fixed";
    public static final int PROP_ID_fixed = 8;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 9;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 10;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 11;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 12;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 13;
    

    private static int _PROP_ID_BOUND = 14;

    
    /* relation: 应用信息 */
    public static final String PROP_NAME_appInfo = "appInfo";
    
    /* relation: 父资源 */
    public static final String PROP_NAME_parent = "parent";
    
    /* relation: 子资源 */
    public static final String PROP_NAME_childSections = "childSections";
    
    /* relation: 关联应用组 */
    public static final String PROP_NAME_workSheetInfo = "workSheetInfo";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_appSectionId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_appSectionId};

    private static final String[] PROP_ID_TO_NAME = new String[14];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_appSectionId] = PROP_NAME_appSectionId;
          PROP_NAME_TO_ID.put(PROP_NAME_appSectionId, PROP_ID_appSectionId);
      
          PROP_ID_TO_NAME[PROP_ID_appId] = PROP_NAME_appId;
          PROP_NAME_TO_ID.put(PROP_NAME_appId, PROP_ID_appId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_parentId] = PROP_NAME_parentId;
          PROP_NAME_TO_ID.put(PROP_NAME_parentId, PROP_ID_parentId);
      
          PROP_ID_TO_NAME[PROP_ID_icon] = PROP_NAME_icon;
          PROP_NAME_TO_ID.put(PROP_NAME_icon, PROP_ID_icon);
      
          PROP_ID_TO_NAME[PROP_ID_iconUrl] = PROP_NAME_iconUrl;
          PROP_NAME_TO_ID.put(PROP_NAME_iconUrl, PROP_ID_iconUrl);
      
          PROP_ID_TO_NAME[PROP_ID_isLock] = PROP_NAME_isLock;
          PROP_NAME_TO_ID.put(PROP_NAME_isLock, PROP_ID_isLock);
      
          PROP_ID_TO_NAME[PROP_ID_fixed] = PROP_NAME_fixed;
          PROP_NAME_TO_ID.put(PROP_NAME_fixed, PROP_ID_fixed);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 应用分组ID: app_section_id */
    private java.lang.String _appSectionId;
    
    /* 应用ID: app_id */
    private java.lang.String _appId;
    
    /* 应用名: name */
    private java.lang.String _name;
    
    /* 父级ID: parent_id */
    private java.lang.String _parentId;
    
    /* 图标: icon */
    private java.lang.String _icon;
    
    /* 图标路径: icon_url */
    private java.lang.String _iconUrl;
    
    /* 锁定状态: is_lock */
    private java.lang.Boolean _isLock;
    
    /* 维护状态: fixed */
    private java.lang.Boolean _fixed;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppSection(){
        // for debug
    }

    protected MlcAppSection newInstance(){
        MlcAppSection entity = new MlcAppSection();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppSection cloneInstance() {
        MlcAppSection entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppSection";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_appSectionId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_appSectionId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_appSectionId:
               return getAppSectionId();
        
            case PROP_ID_appId:
               return getAppId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_parentId:
               return getParentId();
        
            case PROP_ID_icon:
               return getIcon();
        
            case PROP_ID_iconUrl:
               return getIconUrl();
        
            case PROP_ID_isLock:
               return getIsLock();
        
            case PROP_ID_fixed:
               return getFixed();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_appSectionId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appSectionId));
               }
               setAppSectionId(typedValue);
               break;
            }
        
            case PROP_ID_appId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appId));
               }
               setAppId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_parentId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_parentId));
               }
               setParentId(typedValue);
               break;
            }
        
            case PROP_ID_icon:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_icon));
               }
               setIcon(typedValue);
               break;
            }
        
            case PROP_ID_iconUrl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_iconUrl));
               }
               setIconUrl(typedValue);
               break;
            }
        
            case PROP_ID_isLock:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isLock));
               }
               setIsLock(typedValue);
               break;
            }
        
            case PROP_ID_fixed:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_fixed));
               }
               setFixed(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_appSectionId:{
               onInitProp(propId);
               this._appSectionId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_appId:{
               onInitProp(propId);
               this._appId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_parentId:{
               onInitProp(propId);
               this._parentId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_icon:{
               onInitProp(propId);
               this._icon = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_iconUrl:{
               onInitProp(propId);
               this._iconUrl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_isLock:{
               onInitProp(propId);
               this._isLock = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_fixed:{
               onInitProp(propId);
               this._fixed = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 应用分组ID: app_section_id
     */
    public final java.lang.String getAppSectionId(){
         onPropGet(PROP_ID_appSectionId);
         return _appSectionId;
    }

    /**
     * 应用分组ID: app_section_id
     */
    public final void setAppSectionId(java.lang.String value){
        if(onPropSet(PROP_ID_appSectionId,value)){
            this._appSectionId = value;
            internalClearRefs(PROP_ID_appSectionId);
            orm_id();
        }
    }
    
    /**
     * 应用ID: app_id
     */
    public final java.lang.String getAppId(){
         onPropGet(PROP_ID_appId);
         return _appId;
    }

    /**
     * 应用ID: app_id
     */
    public final void setAppId(java.lang.String value){
        if(onPropSet(PROP_ID_appId,value)){
            this._appId = value;
            internalClearRefs(PROP_ID_appId);
            
        }
    }
    
    /**
     * 应用名: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 应用名: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 父级ID: parent_id
     */
    public final java.lang.String getParentId(){
         onPropGet(PROP_ID_parentId);
         return _parentId;
    }

    /**
     * 父级ID: parent_id
     */
    public final void setParentId(java.lang.String value){
        if(onPropSet(PROP_ID_parentId,value)){
            this._parentId = value;
            internalClearRefs(PROP_ID_parentId);
            
        }
    }
    
    /**
     * 图标: icon
     */
    public final java.lang.String getIcon(){
         onPropGet(PROP_ID_icon);
         return _icon;
    }

    /**
     * 图标: icon
     */
    public final void setIcon(java.lang.String value){
        if(onPropSet(PROP_ID_icon,value)){
            this._icon = value;
            internalClearRefs(PROP_ID_icon);
            
        }
    }
    
    /**
     * 图标路径: icon_url
     */
    public final java.lang.String getIconUrl(){
         onPropGet(PROP_ID_iconUrl);
         return _iconUrl;
    }

    /**
     * 图标路径: icon_url
     */
    public final void setIconUrl(java.lang.String value){
        if(onPropSet(PROP_ID_iconUrl,value)){
            this._iconUrl = value;
            internalClearRefs(PROP_ID_iconUrl);
            
        }
    }
    
    /**
     * 锁定状态: is_lock
     */
    public final java.lang.Boolean getIsLock(){
         onPropGet(PROP_ID_isLock);
         return _isLock;
    }

    /**
     * 锁定状态: is_lock
     */
    public final void setIsLock(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isLock,value)){
            this._isLock = value;
            internalClearRefs(PROP_ID_isLock);
            
        }
    }
    
    /**
     * 维护状态: fixed
     */
    public final java.lang.Boolean getFixed(){
         onPropGet(PROP_ID_fixed);
         return _fixed;
    }

    /**
     * 维护状态: fixed
     */
    public final void setFixed(java.lang.Boolean value){
        if(onPropSet(PROP_ID_fixed,value)){
            this._fixed = value;
            internalClearRefs(PROP_ID_fixed);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 应用信息
     */
    public final com.mlc.application.dao.entity.MlcAppInfo getAppInfo(){
       return (com.mlc.application.dao.entity.MlcAppInfo)internalGetRefEntity(PROP_NAME_appInfo);
    }

    public final void setAppInfo(com.mlc.application.dao.entity.MlcAppInfo refEntity){
   
           if(refEntity == null){
           
                   this.setAppId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appInfo, refEntity,()->{
           
                           this.setAppId(refEntity.getAppId());
                       
           });
           }
       
    }
       
    /**
     * 父资源
     */
    public final com.mlc.application.dao.entity.MlcAppSection getParent(){
       return (com.mlc.application.dao.entity.MlcAppSection)internalGetRefEntity(PROP_NAME_parent);
    }

    public final void setParent(com.mlc.application.dao.entity.MlcAppSection refEntity){
   
           if(refEntity == null){
           
                   this.setParentId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_parent, refEntity,()->{
           
                           this.setParentId(refEntity.getAppSectionId());
                       
           });
           }
       
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppSection> _childSections = new OrmEntitySet<>(this, PROP_NAME_childSections,
        com.mlc.application.dao.entity.MlcAppSection.PROP_NAME_parent, null,com.mlc.application.dao.entity.MlcAppSection.class);

    /**
     * 子资源。 refPropName: parent, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppSection> getChildSections(){
       return _childSections;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheet> _workSheetInfo = new OrmEntitySet<>(this, PROP_NAME_workSheetInfo,
        com.mlc.application.dao.entity.MlcAppWorksheet.PROP_NAME_appSection, null,com.mlc.application.dao.entity.MlcAppWorksheet.class);

    /**
     * 关联应用组。 refPropName: appSection, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheet> getWorkSheetInfo(){
       return _workSheetInfo;
    }
       
}
// resume CPD analysis - CPD-ON
