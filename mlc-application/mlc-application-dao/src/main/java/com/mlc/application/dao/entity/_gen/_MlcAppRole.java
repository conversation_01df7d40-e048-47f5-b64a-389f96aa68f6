package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppRole;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  应用角色: mlc_app_role
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppRole extends DynamicOrmEntity{
    
    /* 角色 ID: role_id VARCHAR */
    public static final String PROP_NAME_roleId = "roleId";
    public static final int PROP_ID_roleId = 1;
    
    /* 应用 ID: app_id VARCHAR */
    public static final String PROP_NAME_appId = "appId";
    public static final int PROP_ID_appId = 2;
    
    /* 名称: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 角色类型: role_type INTEGER */
    public static final String PROP_NAME_roleType = "roleType";
    public static final int PROP_ID_roleType = 4;
    
    /* 设置: app_settings_enum INTEGER */
    public static final String PROP_NAME_appSettingsEnum = "appSettingsEnum";
    public static final int PROP_ID_appSettingsEnum = 5;
    
    /* 是否通知: notify BOOLEAN */
    public static final String PROP_NAME_notify = "notify";
    public static final int PROP_ID_notify = 6;
    
    /* 是否开启调试: is_debug BOOLEAN */
    public static final String PROP_NAME_isDebug = "isDebug";
    public static final int PROP_ID_isDebug = 7;
    
    /* 隐藏应用: hide_app_for_members BOOLEAN */
    public static final String PROP_NAME_hideAppForMembers = "hideAppForMembers";
    public static final int PROP_ID_hideAppForMembers = 8;
    
    /* 权限定义方式: permission_way INTEGER */
    public static final String PROP_NAME_permissionWay = "permissionWay";
    public static final int PROP_ID_permissionWay = 9;
    
    /* 操作标签: extend_attrs VARCHAR */
    public static final String PROP_NAME_extendAttrs = "extendAttrs";
    public static final int PROP_ID_extendAttrs = 10;
    
    /* 操作标签: optional_controls VARCHAR */
    public static final String PROP_NAME_optionalControls = "optionalControls";
    public static final int PROP_ID_optionalControls = 11;
    
    /* 允许新增: general_add BOOLEAN */
    public static final String PROP_NAME_generalAdd = "generalAdd";
    public static final int PROP_ID_generalAdd = 12;
    
    /* 允许分享: general_share BOOLEAN */
    public static final String PROP_NAME_generalShare = "generalShare";
    public static final int PROP_ID_generalShare = 13;
    
    /* 允许导入: general_import BOOLEAN */
    public static final String PROP_NAME_generalImport = "generalImport";
    public static final int PROP_ID_generalImport = 14;
    
    /* 允许导出: general_export BOOLEAN */
    public static final String PROP_NAME_generalExport = "generalExport";
    public static final int PROP_ID_generalExport = 15;
    
    /* 允许讨论: general_discussion BOOLEAN */
    public static final String PROP_NAME_generalDiscussion = "generalDiscussion";
    public static final int PROP_ID_generalDiscussion = 16;
    
    /* 允许打印: general_system_printing BOOLEAN */
    public static final String PROP_NAME_generalSystemPrinting = "generalSystemPrinting";
    public static final int PROP_ID_generalSystemPrinting = 17;
    
    /* 允许下载附件: general_attachment_download BOOLEAN */
    public static final String PROP_NAME_generalAttachmentDownload = "generalAttachmentDownload";
    public static final int PROP_ID_generalAttachmentDownload = 18;
    
    /* 允许查看日志: general_logging BOOLEAN */
    public static final String PROP_NAME_generalLogging = "generalLogging";
    public static final int PROP_ID_generalLogging = 19;
    
    /* 排序: sort_index INTEGER */
    public static final String PROP_NAME_sortIndex = "sortIndex";
    public static final int PROP_ID_sortIndex = 20;
    
    /* 描述: description VARCHAR */
    public static final String PROP_NAME_description = "description";
    public static final int PROP_ID_description = 21;
    
    /* 状态: status INTEGER */
    public static final String PROP_NAME_status = "status";
    public static final int PROP_ID_status = 22;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 23;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 25;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 26;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 27;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 28;
    

    private static int _PROP_ID_BOUND = 29;

    
    /* relation: 应用信息 */
    public static final String PROP_NAME_appInfo = "appInfo";
    
    /* relation: 应用成员映射 */
    public static final String PROP_NAME_appMemberMappings = "appMemberMappings";
    
    /* relation: 资源映射 */
    public static final String PROP_NAME_resourceMappings = "resourceMappings";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_roleId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_roleId};

    private static final String[] PROP_ID_TO_NAME = new String[29];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_roleId] = PROP_NAME_roleId;
          PROP_NAME_TO_ID.put(PROP_NAME_roleId, PROP_ID_roleId);
      
          PROP_ID_TO_NAME[PROP_ID_appId] = PROP_NAME_appId;
          PROP_NAME_TO_ID.put(PROP_NAME_appId, PROP_ID_appId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_roleType] = PROP_NAME_roleType;
          PROP_NAME_TO_ID.put(PROP_NAME_roleType, PROP_ID_roleType);
      
          PROP_ID_TO_NAME[PROP_ID_appSettingsEnum] = PROP_NAME_appSettingsEnum;
          PROP_NAME_TO_ID.put(PROP_NAME_appSettingsEnum, PROP_ID_appSettingsEnum);
      
          PROP_ID_TO_NAME[PROP_ID_notify] = PROP_NAME_notify;
          PROP_NAME_TO_ID.put(PROP_NAME_notify, PROP_ID_notify);
      
          PROP_ID_TO_NAME[PROP_ID_isDebug] = PROP_NAME_isDebug;
          PROP_NAME_TO_ID.put(PROP_NAME_isDebug, PROP_ID_isDebug);
      
          PROP_ID_TO_NAME[PROP_ID_hideAppForMembers] = PROP_NAME_hideAppForMembers;
          PROP_NAME_TO_ID.put(PROP_NAME_hideAppForMembers, PROP_ID_hideAppForMembers);
      
          PROP_ID_TO_NAME[PROP_ID_permissionWay] = PROP_NAME_permissionWay;
          PROP_NAME_TO_ID.put(PROP_NAME_permissionWay, PROP_ID_permissionWay);
      
          PROP_ID_TO_NAME[PROP_ID_extendAttrs] = PROP_NAME_extendAttrs;
          PROP_NAME_TO_ID.put(PROP_NAME_extendAttrs, PROP_ID_extendAttrs);
      
          PROP_ID_TO_NAME[PROP_ID_optionalControls] = PROP_NAME_optionalControls;
          PROP_NAME_TO_ID.put(PROP_NAME_optionalControls, PROP_ID_optionalControls);
      
          PROP_ID_TO_NAME[PROP_ID_generalAdd] = PROP_NAME_generalAdd;
          PROP_NAME_TO_ID.put(PROP_NAME_generalAdd, PROP_ID_generalAdd);
      
          PROP_ID_TO_NAME[PROP_ID_generalShare] = PROP_NAME_generalShare;
          PROP_NAME_TO_ID.put(PROP_NAME_generalShare, PROP_ID_generalShare);
      
          PROP_ID_TO_NAME[PROP_ID_generalImport] = PROP_NAME_generalImport;
          PROP_NAME_TO_ID.put(PROP_NAME_generalImport, PROP_ID_generalImport);
      
          PROP_ID_TO_NAME[PROP_ID_generalExport] = PROP_NAME_generalExport;
          PROP_NAME_TO_ID.put(PROP_NAME_generalExport, PROP_ID_generalExport);
      
          PROP_ID_TO_NAME[PROP_ID_generalDiscussion] = PROP_NAME_generalDiscussion;
          PROP_NAME_TO_ID.put(PROP_NAME_generalDiscussion, PROP_ID_generalDiscussion);
      
          PROP_ID_TO_NAME[PROP_ID_generalSystemPrinting] = PROP_NAME_generalSystemPrinting;
          PROP_NAME_TO_ID.put(PROP_NAME_generalSystemPrinting, PROP_ID_generalSystemPrinting);
      
          PROP_ID_TO_NAME[PROP_ID_generalAttachmentDownload] = PROP_NAME_generalAttachmentDownload;
          PROP_NAME_TO_ID.put(PROP_NAME_generalAttachmentDownload, PROP_ID_generalAttachmentDownload);
      
          PROP_ID_TO_NAME[PROP_ID_generalLogging] = PROP_NAME_generalLogging;
          PROP_NAME_TO_ID.put(PROP_NAME_generalLogging, PROP_ID_generalLogging);
      
          PROP_ID_TO_NAME[PROP_ID_sortIndex] = PROP_NAME_sortIndex;
          PROP_NAME_TO_ID.put(PROP_NAME_sortIndex, PROP_ID_sortIndex);
      
          PROP_ID_TO_NAME[PROP_ID_description] = PROP_NAME_description;
          PROP_NAME_TO_ID.put(PROP_NAME_description, PROP_ID_description);
      
          PROP_ID_TO_NAME[PROP_ID_status] = PROP_NAME_status;
          PROP_NAME_TO_ID.put(PROP_NAME_status, PROP_ID_status);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 角色 ID: role_id */
    private java.lang.String _roleId;
    
    /* 应用 ID: app_id */
    private java.lang.String _appId;
    
    /* 名称: name */
    private java.lang.String _name;
    
    /* 角色类型: role_type */
    private java.lang.Integer _roleType;
    
    /* 设置: app_settings_enum */
    private java.lang.Integer _appSettingsEnum;
    
    /* 是否通知: notify */
    private java.lang.Boolean _notify;
    
    /* 是否开启调试: is_debug */
    private java.lang.Boolean _isDebug;
    
    /* 隐藏应用: hide_app_for_members */
    private java.lang.Boolean _hideAppForMembers;
    
    /* 权限定义方式: permission_way */
    private java.lang.Integer _permissionWay;
    
    /* 操作标签: extend_attrs */
    private java.lang.String _extendAttrs;
    
    /* 操作标签: optional_controls */
    private java.lang.String _optionalControls;
    
    /* 允许新增: general_add */
    private java.lang.Boolean _generalAdd;
    
    /* 允许分享: general_share */
    private java.lang.Boolean _generalShare;
    
    /* 允许导入: general_import */
    private java.lang.Boolean _generalImport;
    
    /* 允许导出: general_export */
    private java.lang.Boolean _generalExport;
    
    /* 允许讨论: general_discussion */
    private java.lang.Boolean _generalDiscussion;
    
    /* 允许打印: general_system_printing */
    private java.lang.Boolean _generalSystemPrinting;
    
    /* 允许下载附件: general_attachment_download */
    private java.lang.Boolean _generalAttachmentDownload;
    
    /* 允许查看日志: general_logging */
    private java.lang.Boolean _generalLogging;
    
    /* 排序: sort_index */
    private java.lang.Integer _sortIndex;
    
    /* 描述: description */
    private java.lang.String _description;
    
    /* 状态: status */
    private java.lang.Integer _status;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppRole(){
        // for debug
    }

    protected MlcAppRole newInstance(){
        MlcAppRole entity = new MlcAppRole();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppRole cloneInstance() {
        MlcAppRole entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppRole";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_roleId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_roleId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_roleId:
               return getRoleId();
        
            case PROP_ID_appId:
               return getAppId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_roleType:
               return getRoleType();
        
            case PROP_ID_appSettingsEnum:
               return getAppSettingsEnum();
        
            case PROP_ID_notify:
               return getNotify();
        
            case PROP_ID_isDebug:
               return getIsDebug();
        
            case PROP_ID_hideAppForMembers:
               return getHideAppForMembers();
        
            case PROP_ID_permissionWay:
               return getPermissionWay();
        
            case PROP_ID_extendAttrs:
               return getExtendAttrs();
        
            case PROP_ID_optionalControls:
               return getOptionalControls();
        
            case PROP_ID_generalAdd:
               return getGeneralAdd();
        
            case PROP_ID_generalShare:
               return getGeneralShare();
        
            case PROP_ID_generalImport:
               return getGeneralImport();
        
            case PROP_ID_generalExport:
               return getGeneralExport();
        
            case PROP_ID_generalDiscussion:
               return getGeneralDiscussion();
        
            case PROP_ID_generalSystemPrinting:
               return getGeneralSystemPrinting();
        
            case PROP_ID_generalAttachmentDownload:
               return getGeneralAttachmentDownload();
        
            case PROP_ID_generalLogging:
               return getGeneralLogging();
        
            case PROP_ID_sortIndex:
               return getSortIndex();
        
            case PROP_ID_description:
               return getDescription();
        
            case PROP_ID_status:
               return getStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_roleId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_roleId));
               }
               setRoleId(typedValue);
               break;
            }
        
            case PROP_ID_appId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appId));
               }
               setAppId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_roleType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_roleType));
               }
               setRoleType(typedValue);
               break;
            }
        
            case PROP_ID_appSettingsEnum:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_appSettingsEnum));
               }
               setAppSettingsEnum(typedValue);
               break;
            }
        
            case PROP_ID_notify:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_notify));
               }
               setNotify(typedValue);
               break;
            }
        
            case PROP_ID_isDebug:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isDebug));
               }
               setIsDebug(typedValue);
               break;
            }
        
            case PROP_ID_hideAppForMembers:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_hideAppForMembers));
               }
               setHideAppForMembers(typedValue);
               break;
            }
        
            case PROP_ID_permissionWay:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_permissionWay));
               }
               setPermissionWay(typedValue);
               break;
            }
        
            case PROP_ID_extendAttrs:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_extendAttrs));
               }
               setExtendAttrs(typedValue);
               break;
            }
        
            case PROP_ID_optionalControls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_optionalControls));
               }
               setOptionalControls(typedValue);
               break;
            }
        
            case PROP_ID_generalAdd:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalAdd));
               }
               setGeneralAdd(typedValue);
               break;
            }
        
            case PROP_ID_generalShare:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalShare));
               }
               setGeneralShare(typedValue);
               break;
            }
        
            case PROP_ID_generalImport:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalImport));
               }
               setGeneralImport(typedValue);
               break;
            }
        
            case PROP_ID_generalExport:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalExport));
               }
               setGeneralExport(typedValue);
               break;
            }
        
            case PROP_ID_generalDiscussion:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalDiscussion));
               }
               setGeneralDiscussion(typedValue);
               break;
            }
        
            case PROP_ID_generalSystemPrinting:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalSystemPrinting));
               }
               setGeneralSystemPrinting(typedValue);
               break;
            }
        
            case PROP_ID_generalAttachmentDownload:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalAttachmentDownload));
               }
               setGeneralAttachmentDownload(typedValue);
               break;
            }
        
            case PROP_ID_generalLogging:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_generalLogging));
               }
               setGeneralLogging(typedValue);
               break;
            }
        
            case PROP_ID_sortIndex:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_sortIndex));
               }
               setSortIndex(typedValue);
               break;
            }
        
            case PROP_ID_description:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_description));
               }
               setDescription(typedValue);
               break;
            }
        
            case PROP_ID_status:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_status));
               }
               setStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_roleId:{
               onInitProp(propId);
               this._roleId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_appId:{
               onInitProp(propId);
               this._appId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_roleType:{
               onInitProp(propId);
               this._roleType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_appSettingsEnum:{
               onInitProp(propId);
               this._appSettingsEnum = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_notify:{
               onInitProp(propId);
               this._notify = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isDebug:{
               onInitProp(propId);
               this._isDebug = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_hideAppForMembers:{
               onInitProp(propId);
               this._hideAppForMembers = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_permissionWay:{
               onInitProp(propId);
               this._permissionWay = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_extendAttrs:{
               onInitProp(propId);
               this._extendAttrs = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_optionalControls:{
               onInitProp(propId);
               this._optionalControls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_generalAdd:{
               onInitProp(propId);
               this._generalAdd = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalShare:{
               onInitProp(propId);
               this._generalShare = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalImport:{
               onInitProp(propId);
               this._generalImport = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalExport:{
               onInitProp(propId);
               this._generalExport = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalDiscussion:{
               onInitProp(propId);
               this._generalDiscussion = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalSystemPrinting:{
               onInitProp(propId);
               this._generalSystemPrinting = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalAttachmentDownload:{
               onInitProp(propId);
               this._generalAttachmentDownload = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_generalLogging:{
               onInitProp(propId);
               this._generalLogging = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_sortIndex:{
               onInitProp(propId);
               this._sortIndex = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_description:{
               onInitProp(propId);
               this._description = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_status:{
               onInitProp(propId);
               this._status = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 角色 ID: role_id
     */
    public final java.lang.String getRoleId(){
         onPropGet(PROP_ID_roleId);
         return _roleId;
    }

    /**
     * 角色 ID: role_id
     */
    public final void setRoleId(java.lang.String value){
        if(onPropSet(PROP_ID_roleId,value)){
            this._roleId = value;
            internalClearRefs(PROP_ID_roleId);
            orm_id();
        }
    }
    
    /**
     * 应用 ID: app_id
     */
    public final java.lang.String getAppId(){
         onPropGet(PROP_ID_appId);
         return _appId;
    }

    /**
     * 应用 ID: app_id
     */
    public final void setAppId(java.lang.String value){
        if(onPropSet(PROP_ID_appId,value)){
            this._appId = value;
            internalClearRefs(PROP_ID_appId);
            
        }
    }
    
    /**
     * 名称: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 名称: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 角色类型: role_type
     */
    public final java.lang.Integer getRoleType(){
         onPropGet(PROP_ID_roleType);
         return _roleType;
    }

    /**
     * 角色类型: role_type
     */
    public final void setRoleType(java.lang.Integer value){
        if(onPropSet(PROP_ID_roleType,value)){
            this._roleType = value;
            internalClearRefs(PROP_ID_roleType);
            
        }
    }
    
    /**
     * 设置: app_settings_enum
     */
    public final java.lang.Integer getAppSettingsEnum(){
         onPropGet(PROP_ID_appSettingsEnum);
         return _appSettingsEnum;
    }

    /**
     * 设置: app_settings_enum
     */
    public final void setAppSettingsEnum(java.lang.Integer value){
        if(onPropSet(PROP_ID_appSettingsEnum,value)){
            this._appSettingsEnum = value;
            internalClearRefs(PROP_ID_appSettingsEnum);
            
        }
    }
    
    /**
     * 是否通知: notify
     */
    public final java.lang.Boolean getNotify(){
         onPropGet(PROP_ID_notify);
         return _notify;
    }

    /**
     * 是否通知: notify
     */
    public final void setNotify(java.lang.Boolean value){
        if(onPropSet(PROP_ID_notify,value)){
            this._notify = value;
            internalClearRefs(PROP_ID_notify);
            
        }
    }
    
    /**
     * 是否开启调试: is_debug
     */
    public final java.lang.Boolean getIsDebug(){
         onPropGet(PROP_ID_isDebug);
         return _isDebug;
    }

    /**
     * 是否开启调试: is_debug
     */
    public final void setIsDebug(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isDebug,value)){
            this._isDebug = value;
            internalClearRefs(PROP_ID_isDebug);
            
        }
    }
    
    /**
     * 隐藏应用: hide_app_for_members
     */
    public final java.lang.Boolean getHideAppForMembers(){
         onPropGet(PROP_ID_hideAppForMembers);
         return _hideAppForMembers;
    }

    /**
     * 隐藏应用: hide_app_for_members
     */
    public final void setHideAppForMembers(java.lang.Boolean value){
        if(onPropSet(PROP_ID_hideAppForMembers,value)){
            this._hideAppForMembers = value;
            internalClearRefs(PROP_ID_hideAppForMembers);
            
        }
    }
    
    /**
     * 权限定义方式: permission_way
     */
    public final java.lang.Integer getPermissionWay(){
         onPropGet(PROP_ID_permissionWay);
         return _permissionWay;
    }

    /**
     * 权限定义方式: permission_way
     */
    public final void setPermissionWay(java.lang.Integer value){
        if(onPropSet(PROP_ID_permissionWay,value)){
            this._permissionWay = value;
            internalClearRefs(PROP_ID_permissionWay);
            
        }
    }
    
    /**
     * 操作标签: extend_attrs
     */
    public final java.lang.String getExtendAttrs(){
         onPropGet(PROP_ID_extendAttrs);
         return _extendAttrs;
    }

    /**
     * 操作标签: extend_attrs
     */
    public final void setExtendAttrs(java.lang.String value){
        if(onPropSet(PROP_ID_extendAttrs,value)){
            this._extendAttrs = value;
            internalClearRefs(PROP_ID_extendAttrs);
            
        }
    }
    
    /**
     * 操作标签: optional_controls
     */
    public final java.lang.String getOptionalControls(){
         onPropGet(PROP_ID_optionalControls);
         return _optionalControls;
    }

    /**
     * 操作标签: optional_controls
     */
    public final void setOptionalControls(java.lang.String value){
        if(onPropSet(PROP_ID_optionalControls,value)){
            this._optionalControls = value;
            internalClearRefs(PROP_ID_optionalControls);
            
        }
    }
    
    /**
     * 允许新增: general_add
     */
    public final java.lang.Boolean getGeneralAdd(){
         onPropGet(PROP_ID_generalAdd);
         return _generalAdd;
    }

    /**
     * 允许新增: general_add
     */
    public final void setGeneralAdd(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalAdd,value)){
            this._generalAdd = value;
            internalClearRefs(PROP_ID_generalAdd);
            
        }
    }
    
    /**
     * 允许分享: general_share
     */
    public final java.lang.Boolean getGeneralShare(){
         onPropGet(PROP_ID_generalShare);
         return _generalShare;
    }

    /**
     * 允许分享: general_share
     */
    public final void setGeneralShare(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalShare,value)){
            this._generalShare = value;
            internalClearRefs(PROP_ID_generalShare);
            
        }
    }
    
    /**
     * 允许导入: general_import
     */
    public final java.lang.Boolean getGeneralImport(){
         onPropGet(PROP_ID_generalImport);
         return _generalImport;
    }

    /**
     * 允许导入: general_import
     */
    public final void setGeneralImport(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalImport,value)){
            this._generalImport = value;
            internalClearRefs(PROP_ID_generalImport);
            
        }
    }
    
    /**
     * 允许导出: general_export
     */
    public final java.lang.Boolean getGeneralExport(){
         onPropGet(PROP_ID_generalExport);
         return _generalExport;
    }

    /**
     * 允许导出: general_export
     */
    public final void setGeneralExport(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalExport,value)){
            this._generalExport = value;
            internalClearRefs(PROP_ID_generalExport);
            
        }
    }
    
    /**
     * 允许讨论: general_discussion
     */
    public final java.lang.Boolean getGeneralDiscussion(){
         onPropGet(PROP_ID_generalDiscussion);
         return _generalDiscussion;
    }

    /**
     * 允许讨论: general_discussion
     */
    public final void setGeneralDiscussion(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalDiscussion,value)){
            this._generalDiscussion = value;
            internalClearRefs(PROP_ID_generalDiscussion);
            
        }
    }
    
    /**
     * 允许打印: general_system_printing
     */
    public final java.lang.Boolean getGeneralSystemPrinting(){
         onPropGet(PROP_ID_generalSystemPrinting);
         return _generalSystemPrinting;
    }

    /**
     * 允许打印: general_system_printing
     */
    public final void setGeneralSystemPrinting(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalSystemPrinting,value)){
            this._generalSystemPrinting = value;
            internalClearRefs(PROP_ID_generalSystemPrinting);
            
        }
    }
    
    /**
     * 允许下载附件: general_attachment_download
     */
    public final java.lang.Boolean getGeneralAttachmentDownload(){
         onPropGet(PROP_ID_generalAttachmentDownload);
         return _generalAttachmentDownload;
    }

    /**
     * 允许下载附件: general_attachment_download
     */
    public final void setGeneralAttachmentDownload(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalAttachmentDownload,value)){
            this._generalAttachmentDownload = value;
            internalClearRefs(PROP_ID_generalAttachmentDownload);
            
        }
    }
    
    /**
     * 允许查看日志: general_logging
     */
    public final java.lang.Boolean getGeneralLogging(){
         onPropGet(PROP_ID_generalLogging);
         return _generalLogging;
    }

    /**
     * 允许查看日志: general_logging
     */
    public final void setGeneralLogging(java.lang.Boolean value){
        if(onPropSet(PROP_ID_generalLogging,value)){
            this._generalLogging = value;
            internalClearRefs(PROP_ID_generalLogging);
            
        }
    }
    
    /**
     * 排序: sort_index
     */
    public final java.lang.Integer getSortIndex(){
         onPropGet(PROP_ID_sortIndex);
         return _sortIndex;
    }

    /**
     * 排序: sort_index
     */
    public final void setSortIndex(java.lang.Integer value){
        if(onPropSet(PROP_ID_sortIndex,value)){
            this._sortIndex = value;
            internalClearRefs(PROP_ID_sortIndex);
            
        }
    }
    
    /**
     * 描述: description
     */
    public final java.lang.String getDescription(){
         onPropGet(PROP_ID_description);
         return _description;
    }

    /**
     * 描述: description
     */
    public final void setDescription(java.lang.String value){
        if(onPropSet(PROP_ID_description,value)){
            this._description = value;
            internalClearRefs(PROP_ID_description);
            
        }
    }
    
    /**
     * 状态: status
     */
    public final java.lang.Integer getStatus(){
         onPropGet(PROP_ID_status);
         return _status;
    }

    /**
     * 状态: status
     */
    public final void setStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_status,value)){
            this._status = value;
            internalClearRefs(PROP_ID_status);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 应用信息
     */
    public final com.mlc.application.dao.entity.MlcAppInfo getAppInfo(){
       return (com.mlc.application.dao.entity.MlcAppInfo)internalGetRefEntity(PROP_NAME_appInfo);
    }

    public final void setAppInfo(com.mlc.application.dao.entity.MlcAppInfo refEntity){
   
           if(refEntity == null){
           
                   this.setAppId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appInfo, refEntity,()->{
           
                           this.setAppId(refEntity.getAppId());
                       
           });
           }
       
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppMemberRole> _appMemberMappings = new OrmEntitySet<>(this, PROP_NAME_appMemberMappings,
        com.mlc.application.dao.entity.MlcAppMemberRole.PROP_NAME_appRole, null,com.mlc.application.dao.entity.MlcAppMemberRole.class);

    /**
     * 应用成员映射。 refPropName: appRole, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppMemberRole> getAppMemberMappings(){
       return _appMemberMappings;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppRoleResource> _resourceMappings = new OrmEntitySet<>(this, PROP_NAME_resourceMappings,
        com.mlc.application.dao.entity.MlcAppRoleResource.PROP_NAME_role, null,com.mlc.application.dao.entity.MlcAppRoleResource.class);

    /**
     * 资源映射。 refPropName: role, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppRoleResource> getResourceMappings(){
       return _resourceMappings;
    }
       
        public final List<com.mlc.application.dao.entity.MlcAppMember> getRelatedAppMemberList(){
            return (List<com.mlc.application.dao.entity.MlcAppMember>)io.nop.orm.support.OrmEntityHelper.getRefProps(getAppMemberMappings(),"appMember");
        }

        public final List<String> getRelatedAppMemberList_ids(){
            return io.nop.orm.support.OrmEntityHelper.getRefIds(getAppMemberMappings(),"appMember");
        }

        public void setRelatedAppMemberList_ids(List<String> value){
            io.nop.orm.support.OrmEntityHelper.setRefIds(getAppMemberMappings(),"appMember",value);
        }
    

    public final String getRelatedAppMemberList_label(){
        return io.nop.orm.support.OrmEntityHelper.getLabelForRefProps(getAppMemberMappings(),"appMember");
    }


        public final List<com.mlc.application.dao.entity.MlcAppResource> getRelatedResourceList(){
            return (List<com.mlc.application.dao.entity.MlcAppResource>)io.nop.orm.support.OrmEntityHelper.getRefProps(getResourceMappings(),"resource");
        }

        public final List<String> getRelatedResourceList_ids(){
            return io.nop.orm.support.OrmEntityHelper.getRefIds(getResourceMappings(),"resource");
        }

        public void setRelatedResourceList_ids(List<String> value){
            io.nop.orm.support.OrmEntityHelper.setRefIds(getResourceMappings(),"resource",value);
        }
    

    public final String getRelatedResourceList_label(){
        return io.nop.orm.support.OrmEntityHelper.getLabelForRefProps(getResourceMappings(),"resource");
    }


}
// resume CPD analysis - CPD-ON
