package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppWorksheetRule;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  工作表业务规则: mlc_app_worksheet_rule
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppWorksheetRule extends DynamicOrmEntity{
    
    /* 业务规则 ID: rule_id VARCHAR */
    public static final String PROP_NAME_ruleId = "ruleId";
    public static final int PROP_ID_ruleId = 1;
    
    /* 工作表 ID: worksheet_id VARCHAR */
    public static final String PROP_NAME_worksheetId = "worksheetId";
    public static final int PROP_ID_worksheetId = 2;
    
    /* 工作表名称: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 规则类型: type INTEGER */
    public static final String PROP_NAME_type = "type";
    public static final int PROP_ID_type = 4;
    
    /* 检查类型: check_type INTEGER */
    public static final String PROP_NAME_checkType = "checkType";
    public static final int PROP_ID_checkType = 5;
    
    /* 提示类型: hint_type INTEGER */
    public static final String PROP_NAME_hintType = "hintType";
    public static final int PROP_ID_hintType = 6;
    
    /* 控件集合: control_ids VARCHAR */
    public static final String PROP_NAME_controlIds = "controlIds";
    public static final int PROP_ID_controlIds = 7;
    
    /* 初始过滤: filters VARCHAR */
    public static final String PROP_NAME_filters = "filters";
    public static final int PROP_ID_filters = 8;
    
    /* 规则项: rule_items VARCHAR */
    public static final String PROP_NAME_ruleItems = "ruleItems";
    public static final int PROP_ID_ruleItems = 9;
    
    /* 打开抽屉: open_drawer BOOLEAN */
    public static final String PROP_NAME_openDrawer = "openDrawer";
    public static final int PROP_ID_openDrawer = 10;
    
    /* 已禁用: disabled BOOLEAN */
    public static final String PROP_NAME_disabled = "disabled";
    public static final int PROP_ID_disabled = 11;
    
    /* 排序: order_num INTEGER */
    public static final String PROP_NAME_orderNum = "orderNum";
    public static final int PROP_ID_orderNum = 12;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 13;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 14;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 15;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 16;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 17;
    

    private static int _PROP_ID_BOUND = 18;

    
    /* relation: 所属工作表 */
    public static final String PROP_NAME_worksheet = "worksheet";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_ruleId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_ruleId};

    private static final String[] PROP_ID_TO_NAME = new String[18];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_ruleId] = PROP_NAME_ruleId;
          PROP_NAME_TO_ID.put(PROP_NAME_ruleId, PROP_ID_ruleId);
      
          PROP_ID_TO_NAME[PROP_ID_worksheetId] = PROP_NAME_worksheetId;
          PROP_NAME_TO_ID.put(PROP_NAME_worksheetId, PROP_ID_worksheetId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_type] = PROP_NAME_type;
          PROP_NAME_TO_ID.put(PROP_NAME_type, PROP_ID_type);
      
          PROP_ID_TO_NAME[PROP_ID_checkType] = PROP_NAME_checkType;
          PROP_NAME_TO_ID.put(PROP_NAME_checkType, PROP_ID_checkType);
      
          PROP_ID_TO_NAME[PROP_ID_hintType] = PROP_NAME_hintType;
          PROP_NAME_TO_ID.put(PROP_NAME_hintType, PROP_ID_hintType);
      
          PROP_ID_TO_NAME[PROP_ID_controlIds] = PROP_NAME_controlIds;
          PROP_NAME_TO_ID.put(PROP_NAME_controlIds, PROP_ID_controlIds);
      
          PROP_ID_TO_NAME[PROP_ID_filters] = PROP_NAME_filters;
          PROP_NAME_TO_ID.put(PROP_NAME_filters, PROP_ID_filters);
      
          PROP_ID_TO_NAME[PROP_ID_ruleItems] = PROP_NAME_ruleItems;
          PROP_NAME_TO_ID.put(PROP_NAME_ruleItems, PROP_ID_ruleItems);
      
          PROP_ID_TO_NAME[PROP_ID_openDrawer] = PROP_NAME_openDrawer;
          PROP_NAME_TO_ID.put(PROP_NAME_openDrawer, PROP_ID_openDrawer);
      
          PROP_ID_TO_NAME[PROP_ID_disabled] = PROP_NAME_disabled;
          PROP_NAME_TO_ID.put(PROP_NAME_disabled, PROP_ID_disabled);
      
          PROP_ID_TO_NAME[PROP_ID_orderNum] = PROP_NAME_orderNum;
          PROP_NAME_TO_ID.put(PROP_NAME_orderNum, PROP_ID_orderNum);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 业务规则 ID: rule_id */
    private java.lang.String _ruleId;
    
    /* 工作表 ID: worksheet_id */
    private java.lang.String _worksheetId;
    
    /* 工作表名称: name */
    private java.lang.String _name;
    
    /* 规则类型: type */
    private java.lang.Integer _type;
    
    /* 检查类型: check_type */
    private java.lang.Integer _checkType;
    
    /* 提示类型: hint_type */
    private java.lang.Integer _hintType;
    
    /* 控件集合: control_ids */
    private java.lang.String _controlIds;
    
    /* 初始过滤: filters */
    private java.lang.String _filters;
    
    /* 规则项: rule_items */
    private java.lang.String _ruleItems;
    
    /* 打开抽屉: open_drawer */
    private java.lang.Boolean _openDrawer;
    
    /* 已禁用: disabled */
    private java.lang.Boolean _disabled;
    
    /* 排序: order_num */
    private java.lang.Integer _orderNum;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppWorksheetRule(){
        // for debug
    }

    protected MlcAppWorksheetRule newInstance(){
        MlcAppWorksheetRule entity = new MlcAppWorksheetRule();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppWorksheetRule cloneInstance() {
        MlcAppWorksheetRule entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppWorksheetRule";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_ruleId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_ruleId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_ruleId:
               return getRuleId();
        
            case PROP_ID_worksheetId:
               return getWorksheetId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_type:
               return getType();
        
            case PROP_ID_checkType:
               return getCheckType();
        
            case PROP_ID_hintType:
               return getHintType();
        
            case PROP_ID_controlIds:
               return getControlIds();
        
            case PROP_ID_filters:
               return getFilters();
        
            case PROP_ID_ruleItems:
               return getRuleItems();
        
            case PROP_ID_openDrawer:
               return getOpenDrawer();
        
            case PROP_ID_disabled:
               return getDisabled();
        
            case PROP_ID_orderNum:
               return getOrderNum();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_ruleId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_ruleId));
               }
               setRuleId(typedValue);
               break;
            }
        
            case PROP_ID_worksheetId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_worksheetId));
               }
               setWorksheetId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_type:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_type));
               }
               setType(typedValue);
               break;
            }
        
            case PROP_ID_checkType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_checkType));
               }
               setCheckType(typedValue);
               break;
            }
        
            case PROP_ID_hintType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_hintType));
               }
               setHintType(typedValue);
               break;
            }
        
            case PROP_ID_controlIds:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_controlIds));
               }
               setControlIds(typedValue);
               break;
            }
        
            case PROP_ID_filters:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_filters));
               }
               setFilters(typedValue);
               break;
            }
        
            case PROP_ID_ruleItems:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_ruleItems));
               }
               setRuleItems(typedValue);
               break;
            }
        
            case PROP_ID_openDrawer:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_openDrawer));
               }
               setOpenDrawer(typedValue);
               break;
            }
        
            case PROP_ID_disabled:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_disabled));
               }
               setDisabled(typedValue);
               break;
            }
        
            case PROP_ID_orderNum:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_orderNum));
               }
               setOrderNum(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_ruleId:{
               onInitProp(propId);
               this._ruleId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_worksheetId:{
               onInitProp(propId);
               this._worksheetId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_type:{
               onInitProp(propId);
               this._type = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_checkType:{
               onInitProp(propId);
               this._checkType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_hintType:{
               onInitProp(propId);
               this._hintType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_controlIds:{
               onInitProp(propId);
               this._controlIds = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_filters:{
               onInitProp(propId);
               this._filters = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_ruleItems:{
               onInitProp(propId);
               this._ruleItems = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_openDrawer:{
               onInitProp(propId);
               this._openDrawer = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_disabled:{
               onInitProp(propId);
               this._disabled = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_orderNum:{
               onInitProp(propId);
               this._orderNum = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 业务规则 ID: rule_id
     */
    public final java.lang.String getRuleId(){
         onPropGet(PROP_ID_ruleId);
         return _ruleId;
    }

    /**
     * 业务规则 ID: rule_id
     */
    public final void setRuleId(java.lang.String value){
        if(onPropSet(PROP_ID_ruleId,value)){
            this._ruleId = value;
            internalClearRefs(PROP_ID_ruleId);
            orm_id();
        }
    }
    
    /**
     * 工作表 ID: worksheet_id
     */
    public final java.lang.String getWorksheetId(){
         onPropGet(PROP_ID_worksheetId);
         return _worksheetId;
    }

    /**
     * 工作表 ID: worksheet_id
     */
    public final void setWorksheetId(java.lang.String value){
        if(onPropSet(PROP_ID_worksheetId,value)){
            this._worksheetId = value;
            internalClearRefs(PROP_ID_worksheetId);
            
        }
    }
    
    /**
     * 工作表名称: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 工作表名称: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 规则类型: type
     */
    public final java.lang.Integer getType(){
         onPropGet(PROP_ID_type);
         return _type;
    }

    /**
     * 规则类型: type
     */
    public final void setType(java.lang.Integer value){
        if(onPropSet(PROP_ID_type,value)){
            this._type = value;
            internalClearRefs(PROP_ID_type);
            
        }
    }
    
    /**
     * 检查类型: check_type
     */
    public final java.lang.Integer getCheckType(){
         onPropGet(PROP_ID_checkType);
         return _checkType;
    }

    /**
     * 检查类型: check_type
     */
    public final void setCheckType(java.lang.Integer value){
        if(onPropSet(PROP_ID_checkType,value)){
            this._checkType = value;
            internalClearRefs(PROP_ID_checkType);
            
        }
    }
    
    /**
     * 提示类型: hint_type
     */
    public final java.lang.Integer getHintType(){
         onPropGet(PROP_ID_hintType);
         return _hintType;
    }

    /**
     * 提示类型: hint_type
     */
    public final void setHintType(java.lang.Integer value){
        if(onPropSet(PROP_ID_hintType,value)){
            this._hintType = value;
            internalClearRefs(PROP_ID_hintType);
            
        }
    }
    
    /**
     * 控件集合: control_ids
     */
    public final java.lang.String getControlIds(){
         onPropGet(PROP_ID_controlIds);
         return _controlIds;
    }

    /**
     * 控件集合: control_ids
     */
    public final void setControlIds(java.lang.String value){
        if(onPropSet(PROP_ID_controlIds,value)){
            this._controlIds = value;
            internalClearRefs(PROP_ID_controlIds);
            
        }
    }
    
    /**
     * 初始过滤: filters
     */
    public final java.lang.String getFilters(){
         onPropGet(PROP_ID_filters);
         return _filters;
    }

    /**
     * 初始过滤: filters
     */
    public final void setFilters(java.lang.String value){
        if(onPropSet(PROP_ID_filters,value)){
            this._filters = value;
            internalClearRefs(PROP_ID_filters);
            
        }
    }
    
    /**
     * 规则项: rule_items
     */
    public final java.lang.String getRuleItems(){
         onPropGet(PROP_ID_ruleItems);
         return _ruleItems;
    }

    /**
     * 规则项: rule_items
     */
    public final void setRuleItems(java.lang.String value){
        if(onPropSet(PROP_ID_ruleItems,value)){
            this._ruleItems = value;
            internalClearRefs(PROP_ID_ruleItems);
            
        }
    }
    
    /**
     * 打开抽屉: open_drawer
     */
    public final java.lang.Boolean getOpenDrawer(){
         onPropGet(PROP_ID_openDrawer);
         return _openDrawer;
    }

    /**
     * 打开抽屉: open_drawer
     */
    public final void setOpenDrawer(java.lang.Boolean value){
        if(onPropSet(PROP_ID_openDrawer,value)){
            this._openDrawer = value;
            internalClearRefs(PROP_ID_openDrawer);
            
        }
    }
    
    /**
     * 已禁用: disabled
     */
    public final java.lang.Boolean getDisabled(){
         onPropGet(PROP_ID_disabled);
         return _disabled;
    }

    /**
     * 已禁用: disabled
     */
    public final void setDisabled(java.lang.Boolean value){
        if(onPropSet(PROP_ID_disabled,value)){
            this._disabled = value;
            internalClearRefs(PROP_ID_disabled);
            
        }
    }
    
    /**
     * 排序: order_num
     */
    public final java.lang.Integer getOrderNum(){
         onPropGet(PROP_ID_orderNum);
         return _orderNum;
    }

    /**
     * 排序: order_num
     */
    public final void setOrderNum(java.lang.Integer value){
        if(onPropSet(PROP_ID_orderNum,value)){
            this._orderNum = value;
            internalClearRefs(PROP_ID_orderNum);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 所属工作表
     */
    public final com.mlc.application.dao.entity.MlcAppWorksheet getWorksheet(){
       return (com.mlc.application.dao.entity.MlcAppWorksheet)internalGetRefEntity(PROP_NAME_worksheet);
    }

    public final void setWorksheet(com.mlc.application.dao.entity.MlcAppWorksheet refEntity){
   
           if(refEntity == null){
           
                   this.setWorksheetId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_worksheet, refEntity,()->{
           
                           this.setWorksheetId(refEntity.getWorksheetId());
                       
           });
           }
       
    }
       
}
// resume CPD analysis - CPD-ON
