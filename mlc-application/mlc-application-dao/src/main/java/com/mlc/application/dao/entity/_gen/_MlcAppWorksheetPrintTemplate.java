package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  工作表自定义打印模板: mlc_app_worksheet_print_template
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppWorksheetPrintTemplate extends DynamicOrmEntity{
    
    /* 打印模板 ID: print_template_id VARCHAR */
    public static final String PROP_NAME_printTemplateId = "printTemplateId";
    public static final int PROP_ID_printTemplateId = 1;
    
    /* 工作表 ID: worksheet_id VARCHAR */
    public static final String PROP_NAME_worksheetId = "worksheetId";
    public static final int PROP_ID_worksheetId = 2;
    
    /* 名称: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 来自名称: form_name VARCHAR */
    public static final String PROP_NAME_formName = "formName";
    public static final int PROP_ID_formName = 4;
    
    /* 公司名称: company_name VARCHAR */
    public static final String PROP_NAME_companyName = "companyName";
    public static final int PROP_ID_companyName = 5;
    
    /* 类型: type INTEGER */
    public static final String PROP_NAME_type = "type";
    public static final int PROP_ID_type = 6;
    
    /* 使用范围: range INTEGER */
    public static final String PROP_NAME_range = "range";
    public static final int PROP_ID_range = 7;
    
    /* 控件样式: control_styles VARCHAR */
    public static final String PROP_NAME_controlStyles = "controlStyles";
    public static final int PROP_ID_controlStyles = 8;
    
    /* 关联样式: relation_style VARCHAR */
    public static final String PROP_NAME_relationStyle = "relationStyle";
    public static final int PROP_ID_relationStyle = 9;
    
    /* 拥有者: owner_account VARCHAR */
    public static final String PROP_NAME_ownerAccount = "ownerAccount";
    public static final int PROP_ID_ownerAccount = 10;
    
    /* 打印时间: print_time BOOLEAN */
    public static final String PROP_NAME_printTime = "printTime";
    public static final int PROP_ID_printTime = 11;
    
    /* 二维码: qr_code BOOLEAN */
    public static final String PROP_NAME_qrCode = "qrCode";
    public static final int PROP_ID_qrCode = 12;
    
    /* 打印人: print_account BOOLEAN */
    public static final String PROP_NAME_printAccount = "printAccount";
    public static final int PROP_ID_printAccount = 13;
    
    /* 打印 logo: logo_checked BOOLEAN */
    public static final String PROP_NAME_logoChecked = "logoChecked";
    public static final int PROP_ID_logoChecked = 14;
    
    /* 标题是否打印: title_checked BOOLEAN */
    public static final String PROP_NAME_titleChecked = "titleChecked";
    public static final int PROP_ID_titleChecked = 15;
    
    /* 公司名称否打印: company_name_checked BOOLEAN */
    public static final String PROP_NAME_companyNameChecked = "companyNameChecked";
    public static final int PROP_ID_companyNameChecked = 16;
    
    /* 表单标题: form_name_checked BOOLEAN */
    public static final String PROP_NAME_formNameChecked = "formNameChecked";
    public static final int PROP_ID_formNameChecked = 17;
    
    /* 创建时间是否打印: create_time_checked BOOLEAN */
    public static final String PROP_NAME_createTimeChecked = "createTimeChecked";
    public static final int PROP_ID_createTimeChecked = 18;
    
    /* 创建者是否打印: create_account_checked BOOLEAN */
    public static final String PROP_NAME_createAccountChecked = "createAccountChecked";
    public static final int PROP_ID_createAccountChecked = 19;
    
    /* 更新时间是否打印: update_time_checked BOOLEAN */
    public static final String PROP_NAME_updateTimeChecked = "updateTimeChecked";
    public static final int PROP_ID_updateTimeChecked = 20;
    
    /* 更新人是否打印: update_account_checked BOOLEAN */
    public static final String PROP_NAME_updateAccountChecked = "updateAccountChecked";
    public static final int PROP_ID_updateAccountChecked = 21;
    
    /* 拥有者是否打印: owner_account_checked BOOLEAN */
    public static final String PROP_NAME_ownerAccountChecked = "ownerAccountChecked";
    public static final int PROP_ID_ownerAccountChecked = 22;
    
    /* 空值是否隐藏: show_data BOOLEAN */
    public static final String PROP_NAME_showData = "showData";
    public static final int PROP_ID_showData = 23;
    
    /* 选项字段平铺打印: print_option BOOLEAN */
    public static final String PROP_NAME_printOption = "printOption";
    public static final int PROP_ID_printOption = 24;
    
    /* 分享类型: share_type INTEGER */
    public static final String PROP_NAME_shareType = "shareType";
    public static final int PROP_ID_shareType = 25;
    
    /* 字体: font INTEGER */
    public static final String PROP_NAME_font = "font";
    public static final int PROP_ID_font = 26;
    
    /* 允许下载打印文件: allow_download_permission INTEGER */
    public static final String PROP_NAME_allowDownloadPermission = "allowDownloadPermission";
    public static final int PROP_ID_allowDownloadPermission = 27;
    
    /* 高级设置: advance_settings VARCHAR */
    public static final String PROP_NAME_advanceSettings = "advanceSettings";
    public static final int PROP_ID_advanceSettings = 28;
    
    /* 审批签名位置: approve_position INTEGER */
    public static final String PROP_NAME_approvePosition = "approvePosition";
    public static final int PROP_ID_approvePosition = 29;
    
    /* 审批批准编号: approval_ids VARCHAR */
    public static final String PROP_NAME_approvalIds = "approvalIds";
    public static final int PROP_ID_approvalIds = 30;
    
    /* 视图: views VARCHAR */
    public static final String PROP_NAME_views = "views";
    public static final int PROP_ID_views = 31;
    
    /* 排序: order_number VARCHAR */
    public static final String PROP_NAME_orderNumber = "orderNumber";
    public static final int PROP_ID_orderNumber = 32;
    
    /* 过滤器: filters VARCHAR */
    public static final String PROP_NAME_filters = "filters";
    public static final int PROP_ID_filters = 33;
    
    /* 备注: remark VARCHAR */
    public static final String PROP_NAME_remark = "remark";
    public static final int PROP_ID_remark = 34;
    
    /* 禁用: disabled BOOLEAN */
    public static final String PROP_NAME_disabled = "disabled";
    public static final int PROP_ID_disabled = 35;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 36;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 37;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 38;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 39;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 40;
    

    private static int _PROP_ID_BOUND = 41;

    
    /* relation: 所属工作表 */
    public static final String PROP_NAME_worksheet = "worksheet";
    
    /* component:  */
    public static final String PROP_NAME_advanceSettingsComponent = "advanceSettingsComponent";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_printTemplateId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_printTemplateId};

    private static final String[] PROP_ID_TO_NAME = new String[41];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_printTemplateId] = PROP_NAME_printTemplateId;
          PROP_NAME_TO_ID.put(PROP_NAME_printTemplateId, PROP_ID_printTemplateId);
      
          PROP_ID_TO_NAME[PROP_ID_worksheetId] = PROP_NAME_worksheetId;
          PROP_NAME_TO_ID.put(PROP_NAME_worksheetId, PROP_ID_worksheetId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_formName] = PROP_NAME_formName;
          PROP_NAME_TO_ID.put(PROP_NAME_formName, PROP_ID_formName);
      
          PROP_ID_TO_NAME[PROP_ID_companyName] = PROP_NAME_companyName;
          PROP_NAME_TO_ID.put(PROP_NAME_companyName, PROP_ID_companyName);
      
          PROP_ID_TO_NAME[PROP_ID_type] = PROP_NAME_type;
          PROP_NAME_TO_ID.put(PROP_NAME_type, PROP_ID_type);
      
          PROP_ID_TO_NAME[PROP_ID_range] = PROP_NAME_range;
          PROP_NAME_TO_ID.put(PROP_NAME_range, PROP_ID_range);
      
          PROP_ID_TO_NAME[PROP_ID_controlStyles] = PROP_NAME_controlStyles;
          PROP_NAME_TO_ID.put(PROP_NAME_controlStyles, PROP_ID_controlStyles);
      
          PROP_ID_TO_NAME[PROP_ID_relationStyle] = PROP_NAME_relationStyle;
          PROP_NAME_TO_ID.put(PROP_NAME_relationStyle, PROP_ID_relationStyle);
      
          PROP_ID_TO_NAME[PROP_ID_ownerAccount] = PROP_NAME_ownerAccount;
          PROP_NAME_TO_ID.put(PROP_NAME_ownerAccount, PROP_ID_ownerAccount);
      
          PROP_ID_TO_NAME[PROP_ID_printTime] = PROP_NAME_printTime;
          PROP_NAME_TO_ID.put(PROP_NAME_printTime, PROP_ID_printTime);
      
          PROP_ID_TO_NAME[PROP_ID_qrCode] = PROP_NAME_qrCode;
          PROP_NAME_TO_ID.put(PROP_NAME_qrCode, PROP_ID_qrCode);
      
          PROP_ID_TO_NAME[PROP_ID_printAccount] = PROP_NAME_printAccount;
          PROP_NAME_TO_ID.put(PROP_NAME_printAccount, PROP_ID_printAccount);
      
          PROP_ID_TO_NAME[PROP_ID_logoChecked] = PROP_NAME_logoChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_logoChecked, PROP_ID_logoChecked);
      
          PROP_ID_TO_NAME[PROP_ID_titleChecked] = PROP_NAME_titleChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_titleChecked, PROP_ID_titleChecked);
      
          PROP_ID_TO_NAME[PROP_ID_companyNameChecked] = PROP_NAME_companyNameChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_companyNameChecked, PROP_ID_companyNameChecked);
      
          PROP_ID_TO_NAME[PROP_ID_formNameChecked] = PROP_NAME_formNameChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_formNameChecked, PROP_ID_formNameChecked);
      
          PROP_ID_TO_NAME[PROP_ID_createTimeChecked] = PROP_NAME_createTimeChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_createTimeChecked, PROP_ID_createTimeChecked);
      
          PROP_ID_TO_NAME[PROP_ID_createAccountChecked] = PROP_NAME_createAccountChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_createAccountChecked, PROP_ID_createAccountChecked);
      
          PROP_ID_TO_NAME[PROP_ID_updateTimeChecked] = PROP_NAME_updateTimeChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_updateTimeChecked, PROP_ID_updateTimeChecked);
      
          PROP_ID_TO_NAME[PROP_ID_updateAccountChecked] = PROP_NAME_updateAccountChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_updateAccountChecked, PROP_ID_updateAccountChecked);
      
          PROP_ID_TO_NAME[PROP_ID_ownerAccountChecked] = PROP_NAME_ownerAccountChecked;
          PROP_NAME_TO_ID.put(PROP_NAME_ownerAccountChecked, PROP_ID_ownerAccountChecked);
      
          PROP_ID_TO_NAME[PROP_ID_showData] = PROP_NAME_showData;
          PROP_NAME_TO_ID.put(PROP_NAME_showData, PROP_ID_showData);
      
          PROP_ID_TO_NAME[PROP_ID_printOption] = PROP_NAME_printOption;
          PROP_NAME_TO_ID.put(PROP_NAME_printOption, PROP_ID_printOption);
      
          PROP_ID_TO_NAME[PROP_ID_shareType] = PROP_NAME_shareType;
          PROP_NAME_TO_ID.put(PROP_NAME_shareType, PROP_ID_shareType);
      
          PROP_ID_TO_NAME[PROP_ID_font] = PROP_NAME_font;
          PROP_NAME_TO_ID.put(PROP_NAME_font, PROP_ID_font);
      
          PROP_ID_TO_NAME[PROP_ID_allowDownloadPermission] = PROP_NAME_allowDownloadPermission;
          PROP_NAME_TO_ID.put(PROP_NAME_allowDownloadPermission, PROP_ID_allowDownloadPermission);
      
          PROP_ID_TO_NAME[PROP_ID_advanceSettings] = PROP_NAME_advanceSettings;
          PROP_NAME_TO_ID.put(PROP_NAME_advanceSettings, PROP_ID_advanceSettings);
      
          PROP_ID_TO_NAME[PROP_ID_approvePosition] = PROP_NAME_approvePosition;
          PROP_NAME_TO_ID.put(PROP_NAME_approvePosition, PROP_ID_approvePosition);
      
          PROP_ID_TO_NAME[PROP_ID_approvalIds] = PROP_NAME_approvalIds;
          PROP_NAME_TO_ID.put(PROP_NAME_approvalIds, PROP_ID_approvalIds);
      
          PROP_ID_TO_NAME[PROP_ID_views] = PROP_NAME_views;
          PROP_NAME_TO_ID.put(PROP_NAME_views, PROP_ID_views);
      
          PROP_ID_TO_NAME[PROP_ID_orderNumber] = PROP_NAME_orderNumber;
          PROP_NAME_TO_ID.put(PROP_NAME_orderNumber, PROP_ID_orderNumber);
      
          PROP_ID_TO_NAME[PROP_ID_filters] = PROP_NAME_filters;
          PROP_NAME_TO_ID.put(PROP_NAME_filters, PROP_ID_filters);
      
          PROP_ID_TO_NAME[PROP_ID_remark] = PROP_NAME_remark;
          PROP_NAME_TO_ID.put(PROP_NAME_remark, PROP_ID_remark);
      
          PROP_ID_TO_NAME[PROP_ID_disabled] = PROP_NAME_disabled;
          PROP_NAME_TO_ID.put(PROP_NAME_disabled, PROP_ID_disabled);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 打印模板 ID: print_template_id */
    private java.lang.String _printTemplateId;
    
    /* 工作表 ID: worksheet_id */
    private java.lang.String _worksheetId;
    
    /* 名称: name */
    private java.lang.String _name;
    
    /* 来自名称: form_name */
    private java.lang.String _formName;
    
    /* 公司名称: company_name */
    private java.lang.String _companyName;
    
    /* 类型: type */
    private java.lang.Integer _type;
    
    /* 使用范围: range */
    private java.lang.Integer _range;
    
    /* 控件样式: control_styles */
    private java.lang.String _controlStyles;
    
    /* 关联样式: relation_style */
    private java.lang.String _relationStyle;
    
    /* 拥有者: owner_account */
    private java.lang.String _ownerAccount;
    
    /* 打印时间: print_time */
    private java.lang.Boolean _printTime;
    
    /* 二维码: qr_code */
    private java.lang.Boolean _qrCode;
    
    /* 打印人: print_account */
    private java.lang.Boolean _printAccount;
    
    /* 打印 logo: logo_checked */
    private java.lang.Boolean _logoChecked;
    
    /* 标题是否打印: title_checked */
    private java.lang.Boolean _titleChecked;
    
    /* 公司名称否打印: company_name_checked */
    private java.lang.Boolean _companyNameChecked;
    
    /* 表单标题: form_name_checked */
    private java.lang.Boolean _formNameChecked;
    
    /* 创建时间是否打印: create_time_checked */
    private java.lang.Boolean _createTimeChecked;
    
    /* 创建者是否打印: create_account_checked */
    private java.lang.Boolean _createAccountChecked;
    
    /* 更新时间是否打印: update_time_checked */
    private java.lang.Boolean _updateTimeChecked;
    
    /* 更新人是否打印: update_account_checked */
    private java.lang.Boolean _updateAccountChecked;
    
    /* 拥有者是否打印: owner_account_checked */
    private java.lang.Boolean _ownerAccountChecked;
    
    /* 空值是否隐藏: show_data */
    private java.lang.Boolean _showData;
    
    /* 选项字段平铺打印: print_option */
    private java.lang.Boolean _printOption;
    
    /* 分享类型: share_type */
    private java.lang.Integer _shareType;
    
    /* 字体: font */
    private java.lang.Integer _font;
    
    /* 允许下载打印文件: allow_download_permission */
    private java.lang.Integer _allowDownloadPermission;
    
    /* 高级设置: advance_settings */
    private java.lang.String _advanceSettings;
    
    /* 审批签名位置: approve_position */
    private java.lang.Integer _approvePosition;
    
    /* 审批批准编号: approval_ids */
    private java.lang.String _approvalIds;
    
    /* 视图: views */
    private java.lang.String _views;
    
    /* 排序: order_number */
    private java.lang.String _orderNumber;
    
    /* 过滤器: filters */
    private java.lang.String _filters;
    
    /* 备注: remark */
    private java.lang.String _remark;
    
    /* 禁用: disabled */
    private java.lang.Boolean _disabled;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppWorksheetPrintTemplate(){
        // for debug
    }

    protected MlcAppWorksheetPrintTemplate newInstance(){
        MlcAppWorksheetPrintTemplate entity = new MlcAppWorksheetPrintTemplate();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppWorksheetPrintTemplate cloneInstance() {
        MlcAppWorksheetPrintTemplate entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_printTemplateId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_printTemplateId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_printTemplateId:
               return getPrintTemplateId();
        
            case PROP_ID_worksheetId:
               return getWorksheetId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_formName:
               return getFormName();
        
            case PROP_ID_companyName:
               return getCompanyName();
        
            case PROP_ID_type:
               return getType();
        
            case PROP_ID_range:
               return getRange();
        
            case PROP_ID_controlStyles:
               return getControlStyles();
        
            case PROP_ID_relationStyle:
               return getRelationStyle();
        
            case PROP_ID_ownerAccount:
               return getOwnerAccount();
        
            case PROP_ID_printTime:
               return getPrintTime();
        
            case PROP_ID_qrCode:
               return getQrCode();
        
            case PROP_ID_printAccount:
               return getPrintAccount();
        
            case PROP_ID_logoChecked:
               return getLogoChecked();
        
            case PROP_ID_titleChecked:
               return getTitleChecked();
        
            case PROP_ID_companyNameChecked:
               return getCompanyNameChecked();
        
            case PROP_ID_formNameChecked:
               return getFormNameChecked();
        
            case PROP_ID_createTimeChecked:
               return getCreateTimeChecked();
        
            case PROP_ID_createAccountChecked:
               return getCreateAccountChecked();
        
            case PROP_ID_updateTimeChecked:
               return getUpdateTimeChecked();
        
            case PROP_ID_updateAccountChecked:
               return getUpdateAccountChecked();
        
            case PROP_ID_ownerAccountChecked:
               return getOwnerAccountChecked();
        
            case PROP_ID_showData:
               return getShowData();
        
            case PROP_ID_printOption:
               return getPrintOption();
        
            case PROP_ID_shareType:
               return getShareType();
        
            case PROP_ID_font:
               return getFont();
        
            case PROP_ID_allowDownloadPermission:
               return getAllowDownloadPermission();
        
            case PROP_ID_advanceSettings:
               return getAdvanceSettings();
        
            case PROP_ID_approvePosition:
               return getApprovePosition();
        
            case PROP_ID_approvalIds:
               return getApprovalIds();
        
            case PROP_ID_views:
               return getViews();
        
            case PROP_ID_orderNumber:
               return getOrderNumber();
        
            case PROP_ID_filters:
               return getFilters();
        
            case PROP_ID_remark:
               return getRemark();
        
            case PROP_ID_disabled:
               return getDisabled();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_printTemplateId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_printTemplateId));
               }
               setPrintTemplateId(typedValue);
               break;
            }
        
            case PROP_ID_worksheetId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_worksheetId));
               }
               setWorksheetId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_formName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_formName));
               }
               setFormName(typedValue);
               break;
            }
        
            case PROP_ID_companyName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_companyName));
               }
               setCompanyName(typedValue);
               break;
            }
        
            case PROP_ID_type:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_type));
               }
               setType(typedValue);
               break;
            }
        
            case PROP_ID_range:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_range));
               }
               setRange(typedValue);
               break;
            }
        
            case PROP_ID_controlStyles:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_controlStyles));
               }
               setControlStyles(typedValue);
               break;
            }
        
            case PROP_ID_relationStyle:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_relationStyle));
               }
               setRelationStyle(typedValue);
               break;
            }
        
            case PROP_ID_ownerAccount:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_ownerAccount));
               }
               setOwnerAccount(typedValue);
               break;
            }
        
            case PROP_ID_printTime:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_printTime));
               }
               setPrintTime(typedValue);
               break;
            }
        
            case PROP_ID_qrCode:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_qrCode));
               }
               setQrCode(typedValue);
               break;
            }
        
            case PROP_ID_printAccount:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_printAccount));
               }
               setPrintAccount(typedValue);
               break;
            }
        
            case PROP_ID_logoChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_logoChecked));
               }
               setLogoChecked(typedValue);
               break;
            }
        
            case PROP_ID_titleChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_titleChecked));
               }
               setTitleChecked(typedValue);
               break;
            }
        
            case PROP_ID_companyNameChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_companyNameChecked));
               }
               setCompanyNameChecked(typedValue);
               break;
            }
        
            case PROP_ID_formNameChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_formNameChecked));
               }
               setFormNameChecked(typedValue);
               break;
            }
        
            case PROP_ID_createTimeChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_createTimeChecked));
               }
               setCreateTimeChecked(typedValue);
               break;
            }
        
            case PROP_ID_createAccountChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_createAccountChecked));
               }
               setCreateAccountChecked(typedValue);
               break;
            }
        
            case PROP_ID_updateTimeChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_updateTimeChecked));
               }
               setUpdateTimeChecked(typedValue);
               break;
            }
        
            case PROP_ID_updateAccountChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_updateAccountChecked));
               }
               setUpdateAccountChecked(typedValue);
               break;
            }
        
            case PROP_ID_ownerAccountChecked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_ownerAccountChecked));
               }
               setOwnerAccountChecked(typedValue);
               break;
            }
        
            case PROP_ID_showData:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_showData));
               }
               setShowData(typedValue);
               break;
            }
        
            case PROP_ID_printOption:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_printOption));
               }
               setPrintOption(typedValue);
               break;
            }
        
            case PROP_ID_shareType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_shareType));
               }
               setShareType(typedValue);
               break;
            }
        
            case PROP_ID_font:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_font));
               }
               setFont(typedValue);
               break;
            }
        
            case PROP_ID_allowDownloadPermission:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_allowDownloadPermission));
               }
               setAllowDownloadPermission(typedValue);
               break;
            }
        
            case PROP_ID_advanceSettings:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_advanceSettings));
               }
               setAdvanceSettings(typedValue);
               break;
            }
        
            case PROP_ID_approvePosition:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_approvePosition));
               }
               setApprovePosition(typedValue);
               break;
            }
        
            case PROP_ID_approvalIds:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_approvalIds));
               }
               setApprovalIds(typedValue);
               break;
            }
        
            case PROP_ID_views:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_views));
               }
               setViews(typedValue);
               break;
            }
        
            case PROP_ID_orderNumber:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_orderNumber));
               }
               setOrderNumber(typedValue);
               break;
            }
        
            case PROP_ID_filters:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_filters));
               }
               setFilters(typedValue);
               break;
            }
        
            case PROP_ID_remark:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_remark));
               }
               setRemark(typedValue);
               break;
            }
        
            case PROP_ID_disabled:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_disabled));
               }
               setDisabled(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_printTemplateId:{
               onInitProp(propId);
               this._printTemplateId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_worksheetId:{
               onInitProp(propId);
               this._worksheetId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_formName:{
               onInitProp(propId);
               this._formName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_companyName:{
               onInitProp(propId);
               this._companyName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_type:{
               onInitProp(propId);
               this._type = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_range:{
               onInitProp(propId);
               this._range = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_controlStyles:{
               onInitProp(propId);
               this._controlStyles = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_relationStyle:{
               onInitProp(propId);
               this._relationStyle = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_ownerAccount:{
               onInitProp(propId);
               this._ownerAccount = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_printTime:{
               onInitProp(propId);
               this._printTime = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_qrCode:{
               onInitProp(propId);
               this._qrCode = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_printAccount:{
               onInitProp(propId);
               this._printAccount = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_logoChecked:{
               onInitProp(propId);
               this._logoChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_titleChecked:{
               onInitProp(propId);
               this._titleChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_companyNameChecked:{
               onInitProp(propId);
               this._companyNameChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_formNameChecked:{
               onInitProp(propId);
               this._formNameChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_createTimeChecked:{
               onInitProp(propId);
               this._createTimeChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_createAccountChecked:{
               onInitProp(propId);
               this._createAccountChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_updateTimeChecked:{
               onInitProp(propId);
               this._updateTimeChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_updateAccountChecked:{
               onInitProp(propId);
               this._updateAccountChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_ownerAccountChecked:{
               onInitProp(propId);
               this._ownerAccountChecked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_showData:{
               onInitProp(propId);
               this._showData = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_printOption:{
               onInitProp(propId);
               this._printOption = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_shareType:{
               onInitProp(propId);
               this._shareType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_font:{
               onInitProp(propId);
               this._font = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_allowDownloadPermission:{
               onInitProp(propId);
               this._allowDownloadPermission = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_advanceSettings:{
               onInitProp(propId);
               this._advanceSettings = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_approvePosition:{
               onInitProp(propId);
               this._approvePosition = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_approvalIds:{
               onInitProp(propId);
               this._approvalIds = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_views:{
               onInitProp(propId);
               this._views = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_orderNumber:{
               onInitProp(propId);
               this._orderNumber = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_filters:{
               onInitProp(propId);
               this._filters = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_remark:{
               onInitProp(propId);
               this._remark = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_disabled:{
               onInitProp(propId);
               this._disabled = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 打印模板 ID: print_template_id
     */
    public final java.lang.String getPrintTemplateId(){
         onPropGet(PROP_ID_printTemplateId);
         return _printTemplateId;
    }

    /**
     * 打印模板 ID: print_template_id
     */
    public final void setPrintTemplateId(java.lang.String value){
        if(onPropSet(PROP_ID_printTemplateId,value)){
            this._printTemplateId = value;
            internalClearRefs(PROP_ID_printTemplateId);
            orm_id();
        }
    }
    
    /**
     * 工作表 ID: worksheet_id
     */
    public final java.lang.String getWorksheetId(){
         onPropGet(PROP_ID_worksheetId);
         return _worksheetId;
    }

    /**
     * 工作表 ID: worksheet_id
     */
    public final void setWorksheetId(java.lang.String value){
        if(onPropSet(PROP_ID_worksheetId,value)){
            this._worksheetId = value;
            internalClearRefs(PROP_ID_worksheetId);
            
        }
    }
    
    /**
     * 名称: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 名称: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 来自名称: form_name
     */
    public final java.lang.String getFormName(){
         onPropGet(PROP_ID_formName);
         return _formName;
    }

    /**
     * 来自名称: form_name
     */
    public final void setFormName(java.lang.String value){
        if(onPropSet(PROP_ID_formName,value)){
            this._formName = value;
            internalClearRefs(PROP_ID_formName);
            
        }
    }
    
    /**
     * 公司名称: company_name
     */
    public final java.lang.String getCompanyName(){
         onPropGet(PROP_ID_companyName);
         return _companyName;
    }

    /**
     * 公司名称: company_name
     */
    public final void setCompanyName(java.lang.String value){
        if(onPropSet(PROP_ID_companyName,value)){
            this._companyName = value;
            internalClearRefs(PROP_ID_companyName);
            
        }
    }
    
    /**
     * 类型: type
     */
    public final java.lang.Integer getType(){
         onPropGet(PROP_ID_type);
         return _type;
    }

    /**
     * 类型: type
     */
    public final void setType(java.lang.Integer value){
        if(onPropSet(PROP_ID_type,value)){
            this._type = value;
            internalClearRefs(PROP_ID_type);
            
        }
    }
    
    /**
     * 使用范围: range
     */
    public final java.lang.Integer getRange(){
         onPropGet(PROP_ID_range);
         return _range;
    }

    /**
     * 使用范围: range
     */
    public final void setRange(java.lang.Integer value){
        if(onPropSet(PROP_ID_range,value)){
            this._range = value;
            internalClearRefs(PROP_ID_range);
            
        }
    }
    
    /**
     * 控件样式: control_styles
     */
    public final java.lang.String getControlStyles(){
         onPropGet(PROP_ID_controlStyles);
         return _controlStyles;
    }

    /**
     * 控件样式: control_styles
     */
    public final void setControlStyles(java.lang.String value){
        if(onPropSet(PROP_ID_controlStyles,value)){
            this._controlStyles = value;
            internalClearRefs(PROP_ID_controlStyles);
            
        }
    }
    
    /**
     * 关联样式: relation_style
     */
    public final java.lang.String getRelationStyle(){
         onPropGet(PROP_ID_relationStyle);
         return _relationStyle;
    }

    /**
     * 关联样式: relation_style
     */
    public final void setRelationStyle(java.lang.String value){
        if(onPropSet(PROP_ID_relationStyle,value)){
            this._relationStyle = value;
            internalClearRefs(PROP_ID_relationStyle);
            
        }
    }
    
    /**
     * 拥有者: owner_account
     */
    public final java.lang.String getOwnerAccount(){
         onPropGet(PROP_ID_ownerAccount);
         return _ownerAccount;
    }

    /**
     * 拥有者: owner_account
     */
    public final void setOwnerAccount(java.lang.String value){
        if(onPropSet(PROP_ID_ownerAccount,value)){
            this._ownerAccount = value;
            internalClearRefs(PROP_ID_ownerAccount);
            
        }
    }
    
    /**
     * 打印时间: print_time
     */
    public final java.lang.Boolean getPrintTime(){
         onPropGet(PROP_ID_printTime);
         return _printTime;
    }

    /**
     * 打印时间: print_time
     */
    public final void setPrintTime(java.lang.Boolean value){
        if(onPropSet(PROP_ID_printTime,value)){
            this._printTime = value;
            internalClearRefs(PROP_ID_printTime);
            
        }
    }
    
    /**
     * 二维码: qr_code
     */
    public final java.lang.Boolean getQrCode(){
         onPropGet(PROP_ID_qrCode);
         return _qrCode;
    }

    /**
     * 二维码: qr_code
     */
    public final void setQrCode(java.lang.Boolean value){
        if(onPropSet(PROP_ID_qrCode,value)){
            this._qrCode = value;
            internalClearRefs(PROP_ID_qrCode);
            
        }
    }
    
    /**
     * 打印人: print_account
     */
    public final java.lang.Boolean getPrintAccount(){
         onPropGet(PROP_ID_printAccount);
         return _printAccount;
    }

    /**
     * 打印人: print_account
     */
    public final void setPrintAccount(java.lang.Boolean value){
        if(onPropSet(PROP_ID_printAccount,value)){
            this._printAccount = value;
            internalClearRefs(PROP_ID_printAccount);
            
        }
    }
    
    /**
     * 打印 logo: logo_checked
     */
    public final java.lang.Boolean getLogoChecked(){
         onPropGet(PROP_ID_logoChecked);
         return _logoChecked;
    }

    /**
     * 打印 logo: logo_checked
     */
    public final void setLogoChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_logoChecked,value)){
            this._logoChecked = value;
            internalClearRefs(PROP_ID_logoChecked);
            
        }
    }
    
    /**
     * 标题是否打印: title_checked
     */
    public final java.lang.Boolean getTitleChecked(){
         onPropGet(PROP_ID_titleChecked);
         return _titleChecked;
    }

    /**
     * 标题是否打印: title_checked
     */
    public final void setTitleChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_titleChecked,value)){
            this._titleChecked = value;
            internalClearRefs(PROP_ID_titleChecked);
            
        }
    }
    
    /**
     * 公司名称否打印: company_name_checked
     */
    public final java.lang.Boolean getCompanyNameChecked(){
         onPropGet(PROP_ID_companyNameChecked);
         return _companyNameChecked;
    }

    /**
     * 公司名称否打印: company_name_checked
     */
    public final void setCompanyNameChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_companyNameChecked,value)){
            this._companyNameChecked = value;
            internalClearRefs(PROP_ID_companyNameChecked);
            
        }
    }
    
    /**
     * 表单标题: form_name_checked
     */
    public final java.lang.Boolean getFormNameChecked(){
         onPropGet(PROP_ID_formNameChecked);
         return _formNameChecked;
    }

    /**
     * 表单标题: form_name_checked
     */
    public final void setFormNameChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_formNameChecked,value)){
            this._formNameChecked = value;
            internalClearRefs(PROP_ID_formNameChecked);
            
        }
    }
    
    /**
     * 创建时间是否打印: create_time_checked
     */
    public final java.lang.Boolean getCreateTimeChecked(){
         onPropGet(PROP_ID_createTimeChecked);
         return _createTimeChecked;
    }

    /**
     * 创建时间是否打印: create_time_checked
     */
    public final void setCreateTimeChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_createTimeChecked,value)){
            this._createTimeChecked = value;
            internalClearRefs(PROP_ID_createTimeChecked);
            
        }
    }
    
    /**
     * 创建者是否打印: create_account_checked
     */
    public final java.lang.Boolean getCreateAccountChecked(){
         onPropGet(PROP_ID_createAccountChecked);
         return _createAccountChecked;
    }

    /**
     * 创建者是否打印: create_account_checked
     */
    public final void setCreateAccountChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_createAccountChecked,value)){
            this._createAccountChecked = value;
            internalClearRefs(PROP_ID_createAccountChecked);
            
        }
    }
    
    /**
     * 更新时间是否打印: update_time_checked
     */
    public final java.lang.Boolean getUpdateTimeChecked(){
         onPropGet(PROP_ID_updateTimeChecked);
         return _updateTimeChecked;
    }

    /**
     * 更新时间是否打印: update_time_checked
     */
    public final void setUpdateTimeChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_updateTimeChecked,value)){
            this._updateTimeChecked = value;
            internalClearRefs(PROP_ID_updateTimeChecked);
            
        }
    }
    
    /**
     * 更新人是否打印: update_account_checked
     */
    public final java.lang.Boolean getUpdateAccountChecked(){
         onPropGet(PROP_ID_updateAccountChecked);
         return _updateAccountChecked;
    }

    /**
     * 更新人是否打印: update_account_checked
     */
    public final void setUpdateAccountChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_updateAccountChecked,value)){
            this._updateAccountChecked = value;
            internalClearRefs(PROP_ID_updateAccountChecked);
            
        }
    }
    
    /**
     * 拥有者是否打印: owner_account_checked
     */
    public final java.lang.Boolean getOwnerAccountChecked(){
         onPropGet(PROP_ID_ownerAccountChecked);
         return _ownerAccountChecked;
    }

    /**
     * 拥有者是否打印: owner_account_checked
     */
    public final void setOwnerAccountChecked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_ownerAccountChecked,value)){
            this._ownerAccountChecked = value;
            internalClearRefs(PROP_ID_ownerAccountChecked);
            
        }
    }
    
    /**
     * 空值是否隐藏: show_data
     */
    public final java.lang.Boolean getShowData(){
         onPropGet(PROP_ID_showData);
         return _showData;
    }

    /**
     * 空值是否隐藏: show_data
     */
    public final void setShowData(java.lang.Boolean value){
        if(onPropSet(PROP_ID_showData,value)){
            this._showData = value;
            internalClearRefs(PROP_ID_showData);
            
        }
    }
    
    /**
     * 选项字段平铺打印: print_option
     */
    public final java.lang.Boolean getPrintOption(){
         onPropGet(PROP_ID_printOption);
         return _printOption;
    }

    /**
     * 选项字段平铺打印: print_option
     */
    public final void setPrintOption(java.lang.Boolean value){
        if(onPropSet(PROP_ID_printOption,value)){
            this._printOption = value;
            internalClearRefs(PROP_ID_printOption);
            
        }
    }
    
    /**
     * 分享类型: share_type
     */
    public final java.lang.Integer getShareType(){
         onPropGet(PROP_ID_shareType);
         return _shareType;
    }

    /**
     * 分享类型: share_type
     */
    public final void setShareType(java.lang.Integer value){
        if(onPropSet(PROP_ID_shareType,value)){
            this._shareType = value;
            internalClearRefs(PROP_ID_shareType);
            
        }
    }
    
    /**
     * 字体: font
     */
    public final java.lang.Integer getFont(){
         onPropGet(PROP_ID_font);
         return _font;
    }

    /**
     * 字体: font
     */
    public final void setFont(java.lang.Integer value){
        if(onPropSet(PROP_ID_font,value)){
            this._font = value;
            internalClearRefs(PROP_ID_font);
            
        }
    }
    
    /**
     * 允许下载打印文件: allow_download_permission
     */
    public final java.lang.Integer getAllowDownloadPermission(){
         onPropGet(PROP_ID_allowDownloadPermission);
         return _allowDownloadPermission;
    }

    /**
     * 允许下载打印文件: allow_download_permission
     */
    public final void setAllowDownloadPermission(java.lang.Integer value){
        if(onPropSet(PROP_ID_allowDownloadPermission,value)){
            this._allowDownloadPermission = value;
            internalClearRefs(PROP_ID_allowDownloadPermission);
            
        }
    }
    
    /**
     * 高级设置: advance_settings
     */
    public final java.lang.String getAdvanceSettings(){
         onPropGet(PROP_ID_advanceSettings);
         return _advanceSettings;
    }

    /**
     * 高级设置: advance_settings
     */
    public final void setAdvanceSettings(java.lang.String value){
        if(onPropSet(PROP_ID_advanceSettings,value)){
            this._advanceSettings = value;
            internalClearRefs(PROP_ID_advanceSettings);
            
        }
    }
    
    /**
     * 审批签名位置: approve_position
     */
    public final java.lang.Integer getApprovePosition(){
         onPropGet(PROP_ID_approvePosition);
         return _approvePosition;
    }

    /**
     * 审批签名位置: approve_position
     */
    public final void setApprovePosition(java.lang.Integer value){
        if(onPropSet(PROP_ID_approvePosition,value)){
            this._approvePosition = value;
            internalClearRefs(PROP_ID_approvePosition);
            
        }
    }
    
    /**
     * 审批批准编号: approval_ids
     */
    public final java.lang.String getApprovalIds(){
         onPropGet(PROP_ID_approvalIds);
         return _approvalIds;
    }

    /**
     * 审批批准编号: approval_ids
     */
    public final void setApprovalIds(java.lang.String value){
        if(onPropSet(PROP_ID_approvalIds,value)){
            this._approvalIds = value;
            internalClearRefs(PROP_ID_approvalIds);
            
        }
    }
    
    /**
     * 视图: views
     */
    public final java.lang.String getViews(){
         onPropGet(PROP_ID_views);
         return _views;
    }

    /**
     * 视图: views
     */
    public final void setViews(java.lang.String value){
        if(onPropSet(PROP_ID_views,value)){
            this._views = value;
            internalClearRefs(PROP_ID_views);
            
        }
    }
    
    /**
     * 排序: order_number
     */
    public final java.lang.String getOrderNumber(){
         onPropGet(PROP_ID_orderNumber);
         return _orderNumber;
    }

    /**
     * 排序: order_number
     */
    public final void setOrderNumber(java.lang.String value){
        if(onPropSet(PROP_ID_orderNumber,value)){
            this._orderNumber = value;
            internalClearRefs(PROP_ID_orderNumber);
            
        }
    }
    
    /**
     * 过滤器: filters
     */
    public final java.lang.String getFilters(){
         onPropGet(PROP_ID_filters);
         return _filters;
    }

    /**
     * 过滤器: filters
     */
    public final void setFilters(java.lang.String value){
        if(onPropSet(PROP_ID_filters,value)){
            this._filters = value;
            internalClearRefs(PROP_ID_filters);
            
        }
    }
    
    /**
     * 备注: remark
     */
    public final java.lang.String getRemark(){
         onPropGet(PROP_ID_remark);
         return _remark;
    }

    /**
     * 备注: remark
     */
    public final void setRemark(java.lang.String value){
        if(onPropSet(PROP_ID_remark,value)){
            this._remark = value;
            internalClearRefs(PROP_ID_remark);
            
        }
    }
    
    /**
     * 禁用: disabled
     */
    public final java.lang.Boolean getDisabled(){
         onPropGet(PROP_ID_disabled);
         return _disabled;
    }

    /**
     * 禁用: disabled
     */
    public final void setDisabled(java.lang.Boolean value){
        if(onPropSet(PROP_ID_disabled,value)){
            this._disabled = value;
            internalClearRefs(PROP_ID_disabled);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 所属工作表
     */
    public final com.mlc.application.dao.entity.MlcAppWorksheet getWorksheet(){
       return (com.mlc.application.dao.entity.MlcAppWorksheet)internalGetRefEntity(PROP_NAME_worksheet);
    }

    public final void setWorksheet(com.mlc.application.dao.entity.MlcAppWorksheet refEntity){
   
           if(refEntity == null){
           
                   this.setWorksheetId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_worksheet, refEntity,()->{
           
                           this.setWorksheetId(refEntity.getWorksheetId());
                       
           });
           }
       
    }
       
   private io.nop.orm.component.JsonOrmComponent _advanceSettingsComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_advanceSettingsComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_advanceSettingsComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_advanceSettings);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getAdvanceSettingsComponent(){
      if(_advanceSettingsComponent == null){
          _advanceSettingsComponent = new io.nop.orm.component.JsonOrmComponent();
          _advanceSettingsComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_advanceSettingsComponent);
      }
      return _advanceSettingsComponent;
   }

}
// resume CPD analysis - CPD-ON
