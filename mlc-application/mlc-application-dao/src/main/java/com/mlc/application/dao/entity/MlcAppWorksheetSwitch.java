package com.mlc.application.dao.entity;

import com.mlc.application.dao.entity._gen._MlcAppWorksheetSwitch;
import io.nop.api.core.annotations.biz.BizObjName;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@BizObjName("MlcAppWorksheetSwitch")
public class MlcAppWorksheetSwitch extends _MlcAppWorksheetSwitch {

    private static final Map<Integer, Boolean> NEED_INIT_MAP = new HashMap<>(29);

    private static final int[] TRUE_KEYS = {10, 11, 12, 13, 14, 50, 51, 20, 38, 21, 22, 25, 24, 26, 23, 27, 28, 29, 30, 52, 32, 33, 34, 35, 36, 39, 37};
    private static final int[] FALSE_KEYS = {40, 41};

    static {
        for (int key : TRUE_KEYS) {
            NEED_INIT_MAP.put(key, true);
        }
        for (int key : FALSE_KEYS) {
            NEED_INIT_MAP.put(key, false);
        }
    }

    public static List<MlcAppWorksheetSwitch> initSwitches() {
        List<MlcAppWorksheetSwitch> putList = new ArrayList<>(NEED_INIT_MAP.size());
        NEED_INIT_MAP.forEach((key, value) -> {
            MlcAppWorksheetSwitch switchEntity = new MlcAppWorksheetSwitch();
            switchEntity.setType(key);
            switchEntity.setState(value);
            switchEntity.setRoleType(0);
            putList.add(switchEntity);
        });
        return putList;
    }
}
