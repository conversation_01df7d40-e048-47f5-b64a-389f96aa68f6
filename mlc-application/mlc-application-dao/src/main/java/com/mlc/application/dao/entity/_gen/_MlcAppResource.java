package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppResource;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  应用资源树表: mlc_app_resource
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppResource extends DynamicOrmEntity{
    
    /* 角色工作表字段 ID: resource_id VARCHAR */
    public static final String PROP_NAME_resourceId = "resourceId";
    public static final int PROP_ID_resourceId = 1;
    
    /* 父级ID: parent_id VARCHAR */
    public static final String PROP_NAME_parentId = "parentId";
    public static final int PROP_ID_parentId = 2;
    
    /* 子级ID: children_id VARCHAR */
    public static final String PROP_NAME_childrenId = "childrenId";
    public static final int PROP_ID_childrenId = 3;
    
    /* 资源类型: resource_type VARCHAR */
    public static final String PROP_NAME_resourceType = "resourceType";
    public static final int PROP_ID_resourceType = 4;
    
    /* 权限标识集合: permissions VARCHAR */
    public static final String PROP_NAME_permissions = "permissions";
    public static final int PROP_ID_permissions = 5;
    
    /* 状态: status INTEGER */
    public static final String PROP_NAME_status = "status";
    public static final int PROP_ID_status = 6;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 7;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 8;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 9;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 10;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 11;
    

    private static int _PROP_ID_BOUND = 12;

    
    /* relation: 父级 */
    public static final String PROP_NAME_parent = "parent";
    
    /* relation: 子级 */
    public static final String PROP_NAME_childrenSet = "childrenSet";
    
    /* relation: 角色映射 */
    public static final String PROP_NAME_roleMappings = "roleMappings";
    
    /* component:  */
    public static final String PROP_NAME_permissionsComponent = "permissionsComponent";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_resourceId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_resourceId};

    private static final String[] PROP_ID_TO_NAME = new String[12];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_resourceId] = PROP_NAME_resourceId;
          PROP_NAME_TO_ID.put(PROP_NAME_resourceId, PROP_ID_resourceId);
      
          PROP_ID_TO_NAME[PROP_ID_parentId] = PROP_NAME_parentId;
          PROP_NAME_TO_ID.put(PROP_NAME_parentId, PROP_ID_parentId);
      
          PROP_ID_TO_NAME[PROP_ID_childrenId] = PROP_NAME_childrenId;
          PROP_NAME_TO_ID.put(PROP_NAME_childrenId, PROP_ID_childrenId);
      
          PROP_ID_TO_NAME[PROP_ID_resourceType] = PROP_NAME_resourceType;
          PROP_NAME_TO_ID.put(PROP_NAME_resourceType, PROP_ID_resourceType);
      
          PROP_ID_TO_NAME[PROP_ID_permissions] = PROP_NAME_permissions;
          PROP_NAME_TO_ID.put(PROP_NAME_permissions, PROP_ID_permissions);
      
          PROP_ID_TO_NAME[PROP_ID_status] = PROP_NAME_status;
          PROP_NAME_TO_ID.put(PROP_NAME_status, PROP_ID_status);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 角色工作表字段 ID: resource_id */
    private java.lang.String _resourceId;
    
    /* 父级ID: parent_id */
    private java.lang.String _parentId;
    
    /* 子级ID: children_id */
    private java.lang.String _childrenId;
    
    /* 资源类型: resource_type */
    private java.lang.String _resourceType;
    
    /* 权限标识集合: permissions */
    private java.lang.String _permissions;
    
    /* 状态: status */
    private java.lang.Integer _status;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppResource(){
        // for debug
    }

    protected MlcAppResource newInstance(){
        MlcAppResource entity = new MlcAppResource();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppResource cloneInstance() {
        MlcAppResource entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppResource";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_resourceId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_resourceId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_resourceId:
               return getResourceId();
        
            case PROP_ID_parentId:
               return getParentId();
        
            case PROP_ID_childrenId:
               return getChildrenId();
        
            case PROP_ID_resourceType:
               return getResourceType();
        
            case PROP_ID_permissions:
               return getPermissions();
        
            case PROP_ID_status:
               return getStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_resourceId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_resourceId));
               }
               setResourceId(typedValue);
               break;
            }
        
            case PROP_ID_parentId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_parentId));
               }
               setParentId(typedValue);
               break;
            }
        
            case PROP_ID_childrenId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_childrenId));
               }
               setChildrenId(typedValue);
               break;
            }
        
            case PROP_ID_resourceType:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_resourceType));
               }
               setResourceType(typedValue);
               break;
            }
        
            case PROP_ID_permissions:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_permissions));
               }
               setPermissions(typedValue);
               break;
            }
        
            case PROP_ID_status:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_status));
               }
               setStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_resourceId:{
               onInitProp(propId);
               this._resourceId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_parentId:{
               onInitProp(propId);
               this._parentId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_childrenId:{
               onInitProp(propId);
               this._childrenId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_resourceType:{
               onInitProp(propId);
               this._resourceType = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_permissions:{
               onInitProp(propId);
               this._permissions = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_status:{
               onInitProp(propId);
               this._status = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 角色工作表字段 ID: resource_id
     */
    public final java.lang.String getResourceId(){
         onPropGet(PROP_ID_resourceId);
         return _resourceId;
    }

    /**
     * 角色工作表字段 ID: resource_id
     */
    public final void setResourceId(java.lang.String value){
        if(onPropSet(PROP_ID_resourceId,value)){
            this._resourceId = value;
            internalClearRefs(PROP_ID_resourceId);
            orm_id();
        }
    }
    
    /**
     * 父级ID: parent_id
     */
    public final java.lang.String getParentId(){
         onPropGet(PROP_ID_parentId);
         return _parentId;
    }

    /**
     * 父级ID: parent_id
     */
    public final void setParentId(java.lang.String value){
        if(onPropSet(PROP_ID_parentId,value)){
            this._parentId = value;
            internalClearRefs(PROP_ID_parentId);
            
        }
    }
    
    /**
     * 子级ID: children_id
     */
    public final java.lang.String getChildrenId(){
         onPropGet(PROP_ID_childrenId);
         return _childrenId;
    }

    /**
     * 子级ID: children_id
     */
    public final void setChildrenId(java.lang.String value){
        if(onPropSet(PROP_ID_childrenId,value)){
            this._childrenId = value;
            internalClearRefs(PROP_ID_childrenId);
            
        }
    }
    
    /**
     * 资源类型: resource_type
     */
    public final java.lang.String getResourceType(){
         onPropGet(PROP_ID_resourceType);
         return _resourceType;
    }

    /**
     * 资源类型: resource_type
     */
    public final void setResourceType(java.lang.String value){
        if(onPropSet(PROP_ID_resourceType,value)){
            this._resourceType = value;
            internalClearRefs(PROP_ID_resourceType);
            
        }
    }
    
    /**
     * 权限标识集合: permissions
     */
    public final java.lang.String getPermissions(){
         onPropGet(PROP_ID_permissions);
         return _permissions;
    }

    /**
     * 权限标识集合: permissions
     */
    public final void setPermissions(java.lang.String value){
        if(onPropSet(PROP_ID_permissions,value)){
            this._permissions = value;
            internalClearRefs(PROP_ID_permissions);
            
        }
    }
    
    /**
     * 状态: status
     */
    public final java.lang.Integer getStatus(){
         onPropGet(PROP_ID_status);
         return _status;
    }

    /**
     * 状态: status
     */
    public final void setStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_status,value)){
            this._status = value;
            internalClearRefs(PROP_ID_status);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 父级
     */
    public final com.mlc.application.dao.entity.MlcAppResource getParent(){
       return (com.mlc.application.dao.entity.MlcAppResource)internalGetRefEntity(PROP_NAME_parent);
    }

    public final void setParent(com.mlc.application.dao.entity.MlcAppResource refEntity){
   
           if(refEntity == null){
           
                   this.setParentId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_parent, refEntity,()->{
           
                           this.setParentId(refEntity.getChildrenId());
                       
           });
           }
       
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppResource> _childrenSet = new OrmEntitySet<>(this, PROP_NAME_childrenSet,
        com.mlc.application.dao.entity.MlcAppResource.PROP_NAME_parent, null,com.mlc.application.dao.entity.MlcAppResource.class);

    /**
     * 子级。 refPropName: parent, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppResource> getChildrenSet(){
       return _childrenSet;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppRoleResource> _roleMappings = new OrmEntitySet<>(this, PROP_NAME_roleMappings,
        com.mlc.application.dao.entity.MlcAppRoleResource.PROP_NAME_resource, null,com.mlc.application.dao.entity.MlcAppRoleResource.class);

    /**
     * 角色映射。 refPropName: resource, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppRoleResource> getRoleMappings(){
       return _roleMappings;
    }
       
   private io.nop.orm.component.JsonOrmComponent _permissionsComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_permissionsComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_permissionsComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_permissions);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getPermissionsComponent(){
      if(_permissionsComponent == null){
          _permissionsComponent = new io.nop.orm.component.JsonOrmComponent();
          _permissionsComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_permissionsComponent);
      }
      return _permissionsComponent;
   }

        public final List<com.mlc.application.dao.entity.MlcAppRole> getRelatedRoleList(){
            return (List<com.mlc.application.dao.entity.MlcAppRole>)io.nop.orm.support.OrmEntityHelper.getRefProps(getRoleMappings(),"role");
        }

        public final List<String> getRelatedRoleList_ids(){
            return io.nop.orm.support.OrmEntityHelper.getRefIds(getRoleMappings(),"role");
        }

        public void setRelatedRoleList_ids(List<String> value){
            io.nop.orm.support.OrmEntityHelper.setRefIds(getRoleMappings(),"role",value);
        }
    

    public final String getRelatedRoleList_label(){
        return io.nop.orm.support.OrmEntityHelper.getLabelForRefProps(getRoleMappings(),"role");
    }


}
// resume CPD analysis - CPD-ON
