package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppInfo;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  应用信息: mlc_app_info
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppInfo extends DynamicOrmEntity{
    
    /* 应用ID: app_id VARCHAR */
    public static final String PROP_NAME_appId = "appId";
    public static final int PROP_ID_appId = 1;
    
    /* 模型定义Id: model_definition_id VARCHAR */
    public static final String PROP_NAME_modelDefinitionId = "modelDefinitionId";
    public static final int PROP_ID_modelDefinitionId = 2;
    
    /* 应用名: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 图标: icon VARCHAR */
    public static final String PROP_NAME_icon = "icon";
    public static final int PROP_ID_icon = 4;
    
    /* 图标颜色: icon_color VARCHAR */
    public static final String PROP_NAME_iconColor = "iconColor";
    public static final int PROP_ID_iconColor = 5;
    
    /* 图标路径: icon_url VARCHAR */
    public static final String PROP_NAME_iconUrl = "iconUrl";
    public static final int PROP_ID_iconUrl = 6;
    
    /* 创建类型: create_type INTEGER */
    public static final String PROP_NAME_createType = "createType";
    public static final int PROP_ID_createType = 7;
    
    /* 来源类型: source_type INTEGER */
    public static final String PROP_NAME_sourceType = "sourceType";
    public static final int PROP_ID_sourceType = 8;
    
    /* 导航颜色: nav_color VARCHAR */
    public static final String PROP_NAME_navColor = "navColor";
    public static final int PROP_ID_navColor = 9;
    
    /* 背景色: light_color VARCHAR */
    public static final String PROP_NAME_lightColor = "lightColor";
    public static final int PROP_ID_lightColor = 10;
    
    /* 分组id: group_id VARCHAR */
    public static final String PROP_NAME_groupId = "groupId";
    public static final int PROP_ID_groupId = 11;
    
    /* 锁定状态: is_lock BOOLEAN */
    public static final String PROP_NAME_isLock = "isLock";
    public static final int PROP_ID_isLock = 12;
    
    /* 新建状态: is_new BOOLEAN */
    public static final String PROP_NAME_isNew = "isNew";
    public static final int PROP_ID_isNew = 13;
    
    /* 维护状态: fixed BOOLEAN */
    public static final String PROP_NAME_fixed = "fixed";
    public static final int PROP_ID_fixed = 14;
    
    /* Pc 端显示: pc_display BOOLEAN */
    public static final String PROP_NAME_pcDisplay = "pcDisplay";
    public static final int PROP_ID_pcDisplay = 15;
    
    /* App 端显示: app_display BOOLEAN */
    public static final String PROP_NAME_appDisplay = "appDisplay";
    public static final int PROP_ID_appDisplay = 16;
    
    /* pc 端导航方式: pc_navi_style INTEGER */
    public static final String PROP_NAME_pcNaviStyle = "pcNaviStyle";
    public static final int PROP_ID_pcNaviStyle = 17;
    
    /* 显示图标级别: display_icon VARCHAR */
    public static final String PROP_NAME_displayIcon = "displayIcon";
    public static final int PROP_ID_displayIcon = 18;
    
    /* 记住上次使用: select_app_itme_type INTEGER */
    public static final String PROP_NAME_selectAppItmeType = "selectAppItmeType";
    public static final int PROP_ID_selectAppItmeType = 19;
    
    /* Web 移动端显示: web_mobile_display BOOLEAN */
    public static final String PROP_NAME_webMobileDisplay = "webMobileDisplay";
    public static final int PROP_ID_webMobileDisplay = 20;
    
    /* 移动端导航方式: app_navi_style INTEGER */
    public static final String PROP_NAME_appNaviStyle = "appNaviStyle";
    public static final int PROP_ID_appNaviStyle = 21;
    
    /* 移动端显示模式: grid_display_mode INTEGER */
    public static final String PROP_NAME_gridDisplayMode = "gridDisplayMode";
    public static final int PROP_ID_gridDisplayMode = 22;
    
    /* 移动端分组展开方式: app_navi_display_type INTEGER */
    public static final String PROP_NAME_appNaviDisplayType = "appNaviDisplayType";
    public static final int PROP_ID_appNaviDisplayType = 23;
    
    /* 查看隐藏项: view_hide_navi BOOLEAN */
    public static final String PROP_NAME_viewHideNavi = "viewHideNavi";
    public static final int PROP_ID_viewHideNavi = 24;
    
    /* 星标状态: is_marked BOOLEAN */
    public static final String PROP_NAME_isMarked = "isMarked";
    public static final int PROP_ID_isMarked = 25;
    
    /* 应用说明: description VARCHAR */
    public static final String PROP_NAME_description = "description";
    public static final int PROP_ID_description = 26;
    
    /* 状态: app_status INTEGER */
    public static final String PROP_NAME_appStatus = "appStatus";
    public static final int PROP_ID_appStatus = 27;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 28;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 29;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 30;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 31;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 32;
    

    private static int _PROP_ID_BOUND = 33;

    
    /* relation: 关联应用分组 */
    public static final String PROP_NAME_appSections = "appSections";
    
    /* relation: 关联工作表 */
    public static final String PROP_NAME_workSheetItems = "workSheetItems";
    
    /* relation: 关联应用角色 */
    public static final String PROP_NAME_appMembers = "appMembers";
    
    /* relation: 关联应用角色 */
    public static final String PROP_NAME_appRoles = "appRoles";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_appId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_appId};

    private static final String[] PROP_ID_TO_NAME = new String[33];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_appId] = PROP_NAME_appId;
          PROP_NAME_TO_ID.put(PROP_NAME_appId, PROP_ID_appId);
      
          PROP_ID_TO_NAME[PROP_ID_modelDefinitionId] = PROP_NAME_modelDefinitionId;
          PROP_NAME_TO_ID.put(PROP_NAME_modelDefinitionId, PROP_ID_modelDefinitionId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_icon] = PROP_NAME_icon;
          PROP_NAME_TO_ID.put(PROP_NAME_icon, PROP_ID_icon);
      
          PROP_ID_TO_NAME[PROP_ID_iconColor] = PROP_NAME_iconColor;
          PROP_NAME_TO_ID.put(PROP_NAME_iconColor, PROP_ID_iconColor);
      
          PROP_ID_TO_NAME[PROP_ID_iconUrl] = PROP_NAME_iconUrl;
          PROP_NAME_TO_ID.put(PROP_NAME_iconUrl, PROP_ID_iconUrl);
      
          PROP_ID_TO_NAME[PROP_ID_createType] = PROP_NAME_createType;
          PROP_NAME_TO_ID.put(PROP_NAME_createType, PROP_ID_createType);
      
          PROP_ID_TO_NAME[PROP_ID_sourceType] = PROP_NAME_sourceType;
          PROP_NAME_TO_ID.put(PROP_NAME_sourceType, PROP_ID_sourceType);
      
          PROP_ID_TO_NAME[PROP_ID_navColor] = PROP_NAME_navColor;
          PROP_NAME_TO_ID.put(PROP_NAME_navColor, PROP_ID_navColor);
      
          PROP_ID_TO_NAME[PROP_ID_lightColor] = PROP_NAME_lightColor;
          PROP_NAME_TO_ID.put(PROP_NAME_lightColor, PROP_ID_lightColor);
      
          PROP_ID_TO_NAME[PROP_ID_groupId] = PROP_NAME_groupId;
          PROP_NAME_TO_ID.put(PROP_NAME_groupId, PROP_ID_groupId);
      
          PROP_ID_TO_NAME[PROP_ID_isLock] = PROP_NAME_isLock;
          PROP_NAME_TO_ID.put(PROP_NAME_isLock, PROP_ID_isLock);
      
          PROP_ID_TO_NAME[PROP_ID_isNew] = PROP_NAME_isNew;
          PROP_NAME_TO_ID.put(PROP_NAME_isNew, PROP_ID_isNew);
      
          PROP_ID_TO_NAME[PROP_ID_fixed] = PROP_NAME_fixed;
          PROP_NAME_TO_ID.put(PROP_NAME_fixed, PROP_ID_fixed);
      
          PROP_ID_TO_NAME[PROP_ID_pcDisplay] = PROP_NAME_pcDisplay;
          PROP_NAME_TO_ID.put(PROP_NAME_pcDisplay, PROP_ID_pcDisplay);
      
          PROP_ID_TO_NAME[PROP_ID_appDisplay] = PROP_NAME_appDisplay;
          PROP_NAME_TO_ID.put(PROP_NAME_appDisplay, PROP_ID_appDisplay);
      
          PROP_ID_TO_NAME[PROP_ID_pcNaviStyle] = PROP_NAME_pcNaviStyle;
          PROP_NAME_TO_ID.put(PROP_NAME_pcNaviStyle, PROP_ID_pcNaviStyle);
      
          PROP_ID_TO_NAME[PROP_ID_displayIcon] = PROP_NAME_displayIcon;
          PROP_NAME_TO_ID.put(PROP_NAME_displayIcon, PROP_ID_displayIcon);
      
          PROP_ID_TO_NAME[PROP_ID_selectAppItmeType] = PROP_NAME_selectAppItmeType;
          PROP_NAME_TO_ID.put(PROP_NAME_selectAppItmeType, PROP_ID_selectAppItmeType);
      
          PROP_ID_TO_NAME[PROP_ID_webMobileDisplay] = PROP_NAME_webMobileDisplay;
          PROP_NAME_TO_ID.put(PROP_NAME_webMobileDisplay, PROP_ID_webMobileDisplay);
      
          PROP_ID_TO_NAME[PROP_ID_appNaviStyle] = PROP_NAME_appNaviStyle;
          PROP_NAME_TO_ID.put(PROP_NAME_appNaviStyle, PROP_ID_appNaviStyle);
      
          PROP_ID_TO_NAME[PROP_ID_gridDisplayMode] = PROP_NAME_gridDisplayMode;
          PROP_NAME_TO_ID.put(PROP_NAME_gridDisplayMode, PROP_ID_gridDisplayMode);
      
          PROP_ID_TO_NAME[PROP_ID_appNaviDisplayType] = PROP_NAME_appNaviDisplayType;
          PROP_NAME_TO_ID.put(PROP_NAME_appNaviDisplayType, PROP_ID_appNaviDisplayType);
      
          PROP_ID_TO_NAME[PROP_ID_viewHideNavi] = PROP_NAME_viewHideNavi;
          PROP_NAME_TO_ID.put(PROP_NAME_viewHideNavi, PROP_ID_viewHideNavi);
      
          PROP_ID_TO_NAME[PROP_ID_isMarked] = PROP_NAME_isMarked;
          PROP_NAME_TO_ID.put(PROP_NAME_isMarked, PROP_ID_isMarked);
      
          PROP_ID_TO_NAME[PROP_ID_description] = PROP_NAME_description;
          PROP_NAME_TO_ID.put(PROP_NAME_description, PROP_ID_description);
      
          PROP_ID_TO_NAME[PROP_ID_appStatus] = PROP_NAME_appStatus;
          PROP_NAME_TO_ID.put(PROP_NAME_appStatus, PROP_ID_appStatus);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 应用ID: app_id */
    private java.lang.String _appId;
    
    /* 模型定义Id: model_definition_id */
    private java.lang.String _modelDefinitionId;
    
    /* 应用名: name */
    private java.lang.String _name;
    
    /* 图标: icon */
    private java.lang.String _icon;
    
    /* 图标颜色: icon_color */
    private java.lang.String _iconColor;
    
    /* 图标路径: icon_url */
    private java.lang.String _iconUrl;
    
    /* 创建类型: create_type */
    private java.lang.Integer _createType;
    
    /* 来源类型: source_type */
    private java.lang.Integer _sourceType;
    
    /* 导航颜色: nav_color */
    private java.lang.String _navColor;
    
    /* 背景色: light_color */
    private java.lang.String _lightColor;
    
    /* 分组id: group_id */
    private java.lang.String _groupId;
    
    /* 锁定状态: is_lock */
    private java.lang.Boolean _isLock;
    
    /* 新建状态: is_new */
    private java.lang.Boolean _isNew;
    
    /* 维护状态: fixed */
    private java.lang.Boolean _fixed;
    
    /* Pc 端显示: pc_display */
    private java.lang.Boolean _pcDisplay;
    
    /* App 端显示: app_display */
    private java.lang.Boolean _appDisplay;
    
    /* pc 端导航方式: pc_navi_style */
    private java.lang.Integer _pcNaviStyle;
    
    /* 显示图标级别: display_icon */
    private java.lang.String _displayIcon;
    
    /* 记住上次使用: select_app_itme_type */
    private java.lang.Integer _selectAppItmeType;
    
    /* Web 移动端显示: web_mobile_display */
    private java.lang.Boolean _webMobileDisplay;
    
    /* 移动端导航方式: app_navi_style */
    private java.lang.Integer _appNaviStyle;
    
    /* 移动端显示模式: grid_display_mode */
    private java.lang.Integer _gridDisplayMode;
    
    /* 移动端分组展开方式: app_navi_display_type */
    private java.lang.Integer _appNaviDisplayType;
    
    /* 查看隐藏项: view_hide_navi */
    private java.lang.Boolean _viewHideNavi;
    
    /* 星标状态: is_marked */
    private java.lang.Boolean _isMarked;
    
    /* 应用说明: description */
    private java.lang.String _description;
    
    /* 状态: app_status */
    private java.lang.Integer _appStatus;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppInfo(){
        // for debug
    }

    protected MlcAppInfo newInstance(){
        MlcAppInfo entity = new MlcAppInfo();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppInfo cloneInstance() {
        MlcAppInfo entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppInfo";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_appId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_appId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_appId:
               return getAppId();
        
            case PROP_ID_modelDefinitionId:
               return getModelDefinitionId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_icon:
               return getIcon();
        
            case PROP_ID_iconColor:
               return getIconColor();
        
            case PROP_ID_iconUrl:
               return getIconUrl();
        
            case PROP_ID_createType:
               return getCreateType();
        
            case PROP_ID_sourceType:
               return getSourceType();
        
            case PROP_ID_navColor:
               return getNavColor();
        
            case PROP_ID_lightColor:
               return getLightColor();
        
            case PROP_ID_groupId:
               return getGroupId();
        
            case PROP_ID_isLock:
               return getIsLock();
        
            case PROP_ID_isNew:
               return getIsNew();
        
            case PROP_ID_fixed:
               return getFixed();
        
            case PROP_ID_pcDisplay:
               return getPcDisplay();
        
            case PROP_ID_appDisplay:
               return getAppDisplay();
        
            case PROP_ID_pcNaviStyle:
               return getPcNaviStyle();
        
            case PROP_ID_displayIcon:
               return getDisplayIcon();
        
            case PROP_ID_selectAppItmeType:
               return getSelectAppItmeType();
        
            case PROP_ID_webMobileDisplay:
               return getWebMobileDisplay();
        
            case PROP_ID_appNaviStyle:
               return getAppNaviStyle();
        
            case PROP_ID_gridDisplayMode:
               return getGridDisplayMode();
        
            case PROP_ID_appNaviDisplayType:
               return getAppNaviDisplayType();
        
            case PROP_ID_viewHideNavi:
               return getViewHideNavi();
        
            case PROP_ID_isMarked:
               return getIsMarked();
        
            case PROP_ID_description:
               return getDescription();
        
            case PROP_ID_appStatus:
               return getAppStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_appId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appId));
               }
               setAppId(typedValue);
               break;
            }
        
            case PROP_ID_modelDefinitionId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_modelDefinitionId));
               }
               setModelDefinitionId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_icon:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_icon));
               }
               setIcon(typedValue);
               break;
            }
        
            case PROP_ID_iconColor:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_iconColor));
               }
               setIconColor(typedValue);
               break;
            }
        
            case PROP_ID_iconUrl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_iconUrl));
               }
               setIconUrl(typedValue);
               break;
            }
        
            case PROP_ID_createType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_createType));
               }
               setCreateType(typedValue);
               break;
            }
        
            case PROP_ID_sourceType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_sourceType));
               }
               setSourceType(typedValue);
               break;
            }
        
            case PROP_ID_navColor:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_navColor));
               }
               setNavColor(typedValue);
               break;
            }
        
            case PROP_ID_lightColor:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_lightColor));
               }
               setLightColor(typedValue);
               break;
            }
        
            case PROP_ID_groupId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_groupId));
               }
               setGroupId(typedValue);
               break;
            }
        
            case PROP_ID_isLock:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isLock));
               }
               setIsLock(typedValue);
               break;
            }
        
            case PROP_ID_isNew:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isNew));
               }
               setIsNew(typedValue);
               break;
            }
        
            case PROP_ID_fixed:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_fixed));
               }
               setFixed(typedValue);
               break;
            }
        
            case PROP_ID_pcDisplay:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_pcDisplay));
               }
               setPcDisplay(typedValue);
               break;
            }
        
            case PROP_ID_appDisplay:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_appDisplay));
               }
               setAppDisplay(typedValue);
               break;
            }
        
            case PROP_ID_pcNaviStyle:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_pcNaviStyle));
               }
               setPcNaviStyle(typedValue);
               break;
            }
        
            case PROP_ID_displayIcon:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_displayIcon));
               }
               setDisplayIcon(typedValue);
               break;
            }
        
            case PROP_ID_selectAppItmeType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_selectAppItmeType));
               }
               setSelectAppItmeType(typedValue);
               break;
            }
        
            case PROP_ID_webMobileDisplay:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_webMobileDisplay));
               }
               setWebMobileDisplay(typedValue);
               break;
            }
        
            case PROP_ID_appNaviStyle:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_appNaviStyle));
               }
               setAppNaviStyle(typedValue);
               break;
            }
        
            case PROP_ID_gridDisplayMode:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_gridDisplayMode));
               }
               setGridDisplayMode(typedValue);
               break;
            }
        
            case PROP_ID_appNaviDisplayType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_appNaviDisplayType));
               }
               setAppNaviDisplayType(typedValue);
               break;
            }
        
            case PROP_ID_viewHideNavi:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_viewHideNavi));
               }
               setViewHideNavi(typedValue);
               break;
            }
        
            case PROP_ID_isMarked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isMarked));
               }
               setIsMarked(typedValue);
               break;
            }
        
            case PROP_ID_description:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_description));
               }
               setDescription(typedValue);
               break;
            }
        
            case PROP_ID_appStatus:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_appStatus));
               }
               setAppStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_appId:{
               onInitProp(propId);
               this._appId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_modelDefinitionId:{
               onInitProp(propId);
               this._modelDefinitionId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_icon:{
               onInitProp(propId);
               this._icon = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_iconColor:{
               onInitProp(propId);
               this._iconColor = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_iconUrl:{
               onInitProp(propId);
               this._iconUrl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createType:{
               onInitProp(propId);
               this._createType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_sourceType:{
               onInitProp(propId);
               this._sourceType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_navColor:{
               onInitProp(propId);
               this._navColor = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_lightColor:{
               onInitProp(propId);
               this._lightColor = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_groupId:{
               onInitProp(propId);
               this._groupId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_isLock:{
               onInitProp(propId);
               this._isLock = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isNew:{
               onInitProp(propId);
               this._isNew = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_fixed:{
               onInitProp(propId);
               this._fixed = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_pcDisplay:{
               onInitProp(propId);
               this._pcDisplay = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_appDisplay:{
               onInitProp(propId);
               this._appDisplay = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_pcNaviStyle:{
               onInitProp(propId);
               this._pcNaviStyle = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_displayIcon:{
               onInitProp(propId);
               this._displayIcon = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_selectAppItmeType:{
               onInitProp(propId);
               this._selectAppItmeType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_webMobileDisplay:{
               onInitProp(propId);
               this._webMobileDisplay = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_appNaviStyle:{
               onInitProp(propId);
               this._appNaviStyle = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_gridDisplayMode:{
               onInitProp(propId);
               this._gridDisplayMode = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_appNaviDisplayType:{
               onInitProp(propId);
               this._appNaviDisplayType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_viewHideNavi:{
               onInitProp(propId);
               this._viewHideNavi = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isMarked:{
               onInitProp(propId);
               this._isMarked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_description:{
               onInitProp(propId);
               this._description = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_appStatus:{
               onInitProp(propId);
               this._appStatus = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 应用ID: app_id
     */
    public final java.lang.String getAppId(){
         onPropGet(PROP_ID_appId);
         return _appId;
    }

    /**
     * 应用ID: app_id
     */
    public final void setAppId(java.lang.String value){
        if(onPropSet(PROP_ID_appId,value)){
            this._appId = value;
            internalClearRefs(PROP_ID_appId);
            orm_id();
        }
    }
    
    /**
     * 模型定义Id: model_definition_id
     */
    public final java.lang.String getModelDefinitionId(){
         onPropGet(PROP_ID_modelDefinitionId);
         return _modelDefinitionId;
    }

    /**
     * 模型定义Id: model_definition_id
     */
    public final void setModelDefinitionId(java.lang.String value){
        if(onPropSet(PROP_ID_modelDefinitionId,value)){
            this._modelDefinitionId = value;
            internalClearRefs(PROP_ID_modelDefinitionId);
            
        }
    }
    
    /**
     * 应用名: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 应用名: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 图标: icon
     */
    public final java.lang.String getIcon(){
         onPropGet(PROP_ID_icon);
         return _icon;
    }

    /**
     * 图标: icon
     */
    public final void setIcon(java.lang.String value){
        if(onPropSet(PROP_ID_icon,value)){
            this._icon = value;
            internalClearRefs(PROP_ID_icon);
            
        }
    }
    
    /**
     * 图标颜色: icon_color
     */
    public final java.lang.String getIconColor(){
         onPropGet(PROP_ID_iconColor);
         return _iconColor;
    }

    /**
     * 图标颜色: icon_color
     */
    public final void setIconColor(java.lang.String value){
        if(onPropSet(PROP_ID_iconColor,value)){
            this._iconColor = value;
            internalClearRefs(PROP_ID_iconColor);
            
        }
    }
    
    /**
     * 图标路径: icon_url
     */
    public final java.lang.String getIconUrl(){
         onPropGet(PROP_ID_iconUrl);
         return _iconUrl;
    }

    /**
     * 图标路径: icon_url
     */
    public final void setIconUrl(java.lang.String value){
        if(onPropSet(PROP_ID_iconUrl,value)){
            this._iconUrl = value;
            internalClearRefs(PROP_ID_iconUrl);
            
        }
    }
    
    /**
     * 创建类型: create_type
     */
    public final java.lang.Integer getCreateType(){
         onPropGet(PROP_ID_createType);
         return _createType;
    }

    /**
     * 创建类型: create_type
     */
    public final void setCreateType(java.lang.Integer value){
        if(onPropSet(PROP_ID_createType,value)){
            this._createType = value;
            internalClearRefs(PROP_ID_createType);
            
        }
    }
    
    /**
     * 来源类型: source_type
     */
    public final java.lang.Integer getSourceType(){
         onPropGet(PROP_ID_sourceType);
         return _sourceType;
    }

    /**
     * 来源类型: source_type
     */
    public final void setSourceType(java.lang.Integer value){
        if(onPropSet(PROP_ID_sourceType,value)){
            this._sourceType = value;
            internalClearRefs(PROP_ID_sourceType);
            
        }
    }
    
    /**
     * 导航颜色: nav_color
     */
    public final java.lang.String getNavColor(){
         onPropGet(PROP_ID_navColor);
         return _navColor;
    }

    /**
     * 导航颜色: nav_color
     */
    public final void setNavColor(java.lang.String value){
        if(onPropSet(PROP_ID_navColor,value)){
            this._navColor = value;
            internalClearRefs(PROP_ID_navColor);
            
        }
    }
    
    /**
     * 背景色: light_color
     */
    public final java.lang.String getLightColor(){
         onPropGet(PROP_ID_lightColor);
         return _lightColor;
    }

    /**
     * 背景色: light_color
     */
    public final void setLightColor(java.lang.String value){
        if(onPropSet(PROP_ID_lightColor,value)){
            this._lightColor = value;
            internalClearRefs(PROP_ID_lightColor);
            
        }
    }
    
    /**
     * 分组id: group_id
     */
    public final java.lang.String getGroupId(){
         onPropGet(PROP_ID_groupId);
         return _groupId;
    }

    /**
     * 分组id: group_id
     */
    public final void setGroupId(java.lang.String value){
        if(onPropSet(PROP_ID_groupId,value)){
            this._groupId = value;
            internalClearRefs(PROP_ID_groupId);
            
        }
    }
    
    /**
     * 锁定状态: is_lock
     */
    public final java.lang.Boolean getIsLock(){
         onPropGet(PROP_ID_isLock);
         return _isLock;
    }

    /**
     * 锁定状态: is_lock
     */
    public final void setIsLock(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isLock,value)){
            this._isLock = value;
            internalClearRefs(PROP_ID_isLock);
            
        }
    }
    
    /**
     * 新建状态: is_new
     */
    public final java.lang.Boolean getIsNew(){
         onPropGet(PROP_ID_isNew);
         return _isNew;
    }

    /**
     * 新建状态: is_new
     */
    public final void setIsNew(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isNew,value)){
            this._isNew = value;
            internalClearRefs(PROP_ID_isNew);
            
        }
    }
    
    /**
     * 维护状态: fixed
     */
    public final java.lang.Boolean getFixed(){
         onPropGet(PROP_ID_fixed);
         return _fixed;
    }

    /**
     * 维护状态: fixed
     */
    public final void setFixed(java.lang.Boolean value){
        if(onPropSet(PROP_ID_fixed,value)){
            this._fixed = value;
            internalClearRefs(PROP_ID_fixed);
            
        }
    }
    
    /**
     * Pc 端显示: pc_display
     */
    public final java.lang.Boolean getPcDisplay(){
         onPropGet(PROP_ID_pcDisplay);
         return _pcDisplay;
    }

    /**
     * Pc 端显示: pc_display
     */
    public final void setPcDisplay(java.lang.Boolean value){
        if(onPropSet(PROP_ID_pcDisplay,value)){
            this._pcDisplay = value;
            internalClearRefs(PROP_ID_pcDisplay);
            
        }
    }
    
    /**
     * App 端显示: app_display
     */
    public final java.lang.Boolean getAppDisplay(){
         onPropGet(PROP_ID_appDisplay);
         return _appDisplay;
    }

    /**
     * App 端显示: app_display
     */
    public final void setAppDisplay(java.lang.Boolean value){
        if(onPropSet(PROP_ID_appDisplay,value)){
            this._appDisplay = value;
            internalClearRefs(PROP_ID_appDisplay);
            
        }
    }
    
    /**
     * pc 端导航方式: pc_navi_style
     */
    public final java.lang.Integer getPcNaviStyle(){
         onPropGet(PROP_ID_pcNaviStyle);
         return _pcNaviStyle;
    }

    /**
     * pc 端导航方式: pc_navi_style
     */
    public final void setPcNaviStyle(java.lang.Integer value){
        if(onPropSet(PROP_ID_pcNaviStyle,value)){
            this._pcNaviStyle = value;
            internalClearRefs(PROP_ID_pcNaviStyle);
            
        }
    }
    
    /**
     * 显示图标级别: display_icon
     */
    public final java.lang.String getDisplayIcon(){
         onPropGet(PROP_ID_displayIcon);
         return _displayIcon;
    }

    /**
     * 显示图标级别: display_icon
     */
    public final void setDisplayIcon(java.lang.String value){
        if(onPropSet(PROP_ID_displayIcon,value)){
            this._displayIcon = value;
            internalClearRefs(PROP_ID_displayIcon);
            
        }
    }
    
    /**
     * 记住上次使用: select_app_itme_type
     */
    public final java.lang.Integer getSelectAppItmeType(){
         onPropGet(PROP_ID_selectAppItmeType);
         return _selectAppItmeType;
    }

    /**
     * 记住上次使用: select_app_itme_type
     */
    public final void setSelectAppItmeType(java.lang.Integer value){
        if(onPropSet(PROP_ID_selectAppItmeType,value)){
            this._selectAppItmeType = value;
            internalClearRefs(PROP_ID_selectAppItmeType);
            
        }
    }
    
    /**
     * Web 移动端显示: web_mobile_display
     */
    public final java.lang.Boolean getWebMobileDisplay(){
         onPropGet(PROP_ID_webMobileDisplay);
         return _webMobileDisplay;
    }

    /**
     * Web 移动端显示: web_mobile_display
     */
    public final void setWebMobileDisplay(java.lang.Boolean value){
        if(onPropSet(PROP_ID_webMobileDisplay,value)){
            this._webMobileDisplay = value;
            internalClearRefs(PROP_ID_webMobileDisplay);
            
        }
    }
    
    /**
     * 移动端导航方式: app_navi_style
     */
    public final java.lang.Integer getAppNaviStyle(){
         onPropGet(PROP_ID_appNaviStyle);
         return _appNaviStyle;
    }

    /**
     * 移动端导航方式: app_navi_style
     */
    public final void setAppNaviStyle(java.lang.Integer value){
        if(onPropSet(PROP_ID_appNaviStyle,value)){
            this._appNaviStyle = value;
            internalClearRefs(PROP_ID_appNaviStyle);
            
        }
    }
    
    /**
     * 移动端显示模式: grid_display_mode
     */
    public final java.lang.Integer getGridDisplayMode(){
         onPropGet(PROP_ID_gridDisplayMode);
         return _gridDisplayMode;
    }

    /**
     * 移动端显示模式: grid_display_mode
     */
    public final void setGridDisplayMode(java.lang.Integer value){
        if(onPropSet(PROP_ID_gridDisplayMode,value)){
            this._gridDisplayMode = value;
            internalClearRefs(PROP_ID_gridDisplayMode);
            
        }
    }
    
    /**
     * 移动端分组展开方式: app_navi_display_type
     */
    public final java.lang.Integer getAppNaviDisplayType(){
         onPropGet(PROP_ID_appNaviDisplayType);
         return _appNaviDisplayType;
    }

    /**
     * 移动端分组展开方式: app_navi_display_type
     */
    public final void setAppNaviDisplayType(java.lang.Integer value){
        if(onPropSet(PROP_ID_appNaviDisplayType,value)){
            this._appNaviDisplayType = value;
            internalClearRefs(PROP_ID_appNaviDisplayType);
            
        }
    }
    
    /**
     * 查看隐藏项: view_hide_navi
     */
    public final java.lang.Boolean getViewHideNavi(){
         onPropGet(PROP_ID_viewHideNavi);
         return _viewHideNavi;
    }

    /**
     * 查看隐藏项: view_hide_navi
     */
    public final void setViewHideNavi(java.lang.Boolean value){
        if(onPropSet(PROP_ID_viewHideNavi,value)){
            this._viewHideNavi = value;
            internalClearRefs(PROP_ID_viewHideNavi);
            
        }
    }
    
    /**
     * 星标状态: is_marked
     */
    public final java.lang.Boolean getIsMarked(){
         onPropGet(PROP_ID_isMarked);
         return _isMarked;
    }

    /**
     * 星标状态: is_marked
     */
    public final void setIsMarked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isMarked,value)){
            this._isMarked = value;
            internalClearRefs(PROP_ID_isMarked);
            
        }
    }
    
    /**
     * 应用说明: description
     */
    public final java.lang.String getDescription(){
         onPropGet(PROP_ID_description);
         return _description;
    }

    /**
     * 应用说明: description
     */
    public final void setDescription(java.lang.String value){
        if(onPropSet(PROP_ID_description,value)){
            this._description = value;
            internalClearRefs(PROP_ID_description);
            
        }
    }
    
    /**
     * 状态: app_status
     */
    public final java.lang.Integer getAppStatus(){
         onPropGet(PROP_ID_appStatus);
         return _appStatus;
    }

    /**
     * 状态: app_status
     */
    public final void setAppStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_appStatus,value)){
            this._appStatus = value;
            internalClearRefs(PROP_ID_appStatus);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppSection> _appSections = new OrmEntitySet<>(this, PROP_NAME_appSections,
        com.mlc.application.dao.entity.MlcAppSection.PROP_NAME_appInfo, null,com.mlc.application.dao.entity.MlcAppSection.class);

    /**
     * 关联应用分组。 refPropName: appInfo, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppSection> getAppSections(){
       return _appSections;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheet> _workSheetItems = new OrmEntitySet<>(this, PROP_NAME_workSheetItems,
        com.mlc.application.dao.entity.MlcAppWorksheet.PROP_NAME_appInfo, null,com.mlc.application.dao.entity.MlcAppWorksheet.class);

    /**
     * 关联工作表。 refPropName: appInfo, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheet> getWorkSheetItems(){
       return _workSheetItems;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppMember> _appMembers = new OrmEntitySet<>(this, PROP_NAME_appMembers,
        com.mlc.application.dao.entity.MlcAppMember.PROP_NAME_appInfo, null,com.mlc.application.dao.entity.MlcAppMember.class);

    /**
     * 关联应用角色。 refPropName: appInfo, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppMember> getAppMembers(){
       return _appMembers;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppRole> _appRoles = new OrmEntitySet<>(this, PROP_NAME_appRoles,
        com.mlc.application.dao.entity.MlcAppRole.PROP_NAME_appInfo, null,com.mlc.application.dao.entity.MlcAppRole.class);

    /**
     * 关联应用角色。 refPropName: appInfo, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppRole> getAppRoles(){
       return _appRoles;
    }
       
}
// resume CPD analysis - CPD-ON
