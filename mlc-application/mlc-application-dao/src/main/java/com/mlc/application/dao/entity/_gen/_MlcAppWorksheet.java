package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppWorksheet;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  工作表: mlc_app_worksheet
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppWorksheet extends DynamicOrmEntity{
    
    /* 工作表ID: worksheet_id VARCHAR */
    public static final String PROP_NAME_worksheetId = "worksheetId";
    public static final int PROP_ID_worksheetId = 1;
    
    /* 应用 ID: app_id VARCHAR */
    public static final String PROP_NAME_appId = "appId";
    public static final int PROP_ID_appId = 2;
    
    /* 应用分组 ID: app_section_id VARCHAR */
    public static final String PROP_NAME_appSectionId = "appSectionId";
    public static final int PROP_ID_appSectionId = 3;
    
    /* 模型对象 ID: model_object_entity_id VARCHAR */
    public static final String PROP_NAME_modelObjectEntityId = "modelObjectEntityId";
    public static final int PROP_ID_modelObjectEntityId = 4;
    
    /* 模型对象名称: model_object_entity_name VARCHAR */
    public static final String PROP_NAME_modelObjectEntityName = "modelObjectEntityName";
    public static final int PROP_ID_modelObjectEntityName = 5;
    
    /* 工作表名称: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 6;
    
    /* 工作表别名: alias VARCHAR */
    public static final String PROP_NAME_alias = "alias";
    public static final int PROP_ID_alias = 7;
    
    /* 记录名称: entity_name VARCHAR */
    public static final String PROP_NAME_entityName = "entityName";
    public static final int PROP_ID_entityName = 8;
    
    /* 图标: icon VARCHAR */
    public static final String PROP_NAME_icon = "icon";
    public static final int PROP_ID_icon = 9;
    
    /* 图标颜色: icon_color VARCHAR */
    public static final String PROP_NAME_iconColor = "iconColor";
    public static final int PROP_ID_iconColor = 10;
    
    /* 图标路径: icon_url VARCHAR */
    public static final String PROP_NAME_iconUrl = "iconUrl";
    public static final int PROP_ID_iconUrl = 11;
    
    /* 数量: count INTEGER */
    public static final String PROP_NAME_count = "count";
    public static final int PROP_ID_count = 12;
    
    /* 分享状态: visible_type INTEGER */
    public static final String PROP_NAME_visibleType = "visibleType";
    public static final int PROP_ID_visibleType = 13;
    
    /* 开启审批: open_approval BOOLEAN */
    public static final String PROP_NAME_openApproval = "openApproval";
    public static final int PROP_ID_openApproval = 14;
    
    /* 工作流子表切换: workflow_child_table_switch BOOLEAN */
    public static final String PROP_NAME_workflowChildTableSwitch = "workflowChildTableSwitch";
    public static final int PROP_ID_workflowChildTableSwitch = 15;
    
    /* 侧边栏状态: navigate_hide BOOLEAN */
    public static final String PROP_NAME_navigateHide = "navigateHide";
    public static final int PROP_ID_navigateHide = 16;
    
    /* 是否配置工作表查询: is_worksheet_query BOOLEAN */
    public static final String PROP_NAME_isWorksheetQuery = "isWorksheetQuery";
    public static final int PROP_ID_isWorksheetQuery = 17;
    
    /* 收藏状态: is_marked BOOLEAN */
    public static final String PROP_NAME_isMarked = "isMarked";
    public static final int PROP_ID_isMarked = 18;
    
    /* 控件: controls VARCHAR */
    public static final String PROP_NAME_controls = "controls";
    public static final int PROP_ID_controls = 19;
    
    /* 高级设置: advanced_setting VARCHAR */
    public static final String PROP_NAME_advancedSetting = "advancedSetting";
    public static final int PROP_ID_advancedSetting = 20;
    
    /* 提交设置: submit_setting VARCHAR */
    public static final String PROP_NAME_submitSetting = "submitSetting";
    public static final int PROP_ID_submitSetting = 21;
    
    /* 开发者备注: developer_notes VARCHAR */
    public static final String PROP_NAME_developerNotes = "developerNotes";
    public static final int PROP_ID_developerNotes = 22;
    
    /* 摘要: resume VARCHAR */
    public static final String PROP_NAME_resume = "resume";
    public static final int PROP_ID_resume = 23;
    
    /* 详细说明: dec VARCHAR */
    public static final String PROP_NAME_dec = "dec";
    public static final int PROP_ID_dec = 24;
    
    /* 状态: status INTEGER */
    public static final String PROP_NAME_status = "status";
    public static final int PROP_ID_status = 25;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 26;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 27;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 28;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 29;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 30;
    

    private static int _PROP_ID_BOUND = 31;

    
    /* relation: 所属应用分组 */
    public static final String PROP_NAME_appSection = "appSection";
    
    /* relation: 应用信息 */
    public static final String PROP_NAME_appInfo = "appInfo";
    
    /* relation: 关联工作表视图 */
    public static final String PROP_NAME_worksheetViews = "worksheetViews";
    
    /* relation: 关联工作表规则 */
    public static final String PROP_NAME_worksheetRuleItems = "worksheetRuleItems";
    
    /* relation: 关联工作表按钮 */
    public static final String PROP_NAME_worksheetBtns = "worksheetBtns";
    
    /* relation: 关联工作表打印模板 */
    public static final String PROP_NAME_worksheetPrintTemplates = "worksheetPrintTemplates";
    
    /* relation: 关联工作表开发 */
    public static final String PROP_NAME_worksheetSwitchs = "worksheetSwitchs";
    
    /* component:  */
    public static final String PROP_NAME_advancedSettingComponent = "advancedSettingComponent";
    
    /* component:  */
    public static final String PROP_NAME_submitSettingComponent = "submitSettingComponent";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_worksheetId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_worksheetId};

    private static final String[] PROP_ID_TO_NAME = new String[31];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_worksheetId] = PROP_NAME_worksheetId;
          PROP_NAME_TO_ID.put(PROP_NAME_worksheetId, PROP_ID_worksheetId);
      
          PROP_ID_TO_NAME[PROP_ID_appId] = PROP_NAME_appId;
          PROP_NAME_TO_ID.put(PROP_NAME_appId, PROP_ID_appId);
      
          PROP_ID_TO_NAME[PROP_ID_appSectionId] = PROP_NAME_appSectionId;
          PROP_NAME_TO_ID.put(PROP_NAME_appSectionId, PROP_ID_appSectionId);
      
          PROP_ID_TO_NAME[PROP_ID_modelObjectEntityId] = PROP_NAME_modelObjectEntityId;
          PROP_NAME_TO_ID.put(PROP_NAME_modelObjectEntityId, PROP_ID_modelObjectEntityId);
      
          PROP_ID_TO_NAME[PROP_ID_modelObjectEntityName] = PROP_NAME_modelObjectEntityName;
          PROP_NAME_TO_ID.put(PROP_NAME_modelObjectEntityName, PROP_ID_modelObjectEntityName);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_alias] = PROP_NAME_alias;
          PROP_NAME_TO_ID.put(PROP_NAME_alias, PROP_ID_alias);
      
          PROP_ID_TO_NAME[PROP_ID_entityName] = PROP_NAME_entityName;
          PROP_NAME_TO_ID.put(PROP_NAME_entityName, PROP_ID_entityName);
      
          PROP_ID_TO_NAME[PROP_ID_icon] = PROP_NAME_icon;
          PROP_NAME_TO_ID.put(PROP_NAME_icon, PROP_ID_icon);
      
          PROP_ID_TO_NAME[PROP_ID_iconColor] = PROP_NAME_iconColor;
          PROP_NAME_TO_ID.put(PROP_NAME_iconColor, PROP_ID_iconColor);
      
          PROP_ID_TO_NAME[PROP_ID_iconUrl] = PROP_NAME_iconUrl;
          PROP_NAME_TO_ID.put(PROP_NAME_iconUrl, PROP_ID_iconUrl);
      
          PROP_ID_TO_NAME[PROP_ID_count] = PROP_NAME_count;
          PROP_NAME_TO_ID.put(PROP_NAME_count, PROP_ID_count);
      
          PROP_ID_TO_NAME[PROP_ID_visibleType] = PROP_NAME_visibleType;
          PROP_NAME_TO_ID.put(PROP_NAME_visibleType, PROP_ID_visibleType);
      
          PROP_ID_TO_NAME[PROP_ID_openApproval] = PROP_NAME_openApproval;
          PROP_NAME_TO_ID.put(PROP_NAME_openApproval, PROP_ID_openApproval);
      
          PROP_ID_TO_NAME[PROP_ID_workflowChildTableSwitch] = PROP_NAME_workflowChildTableSwitch;
          PROP_NAME_TO_ID.put(PROP_NAME_workflowChildTableSwitch, PROP_ID_workflowChildTableSwitch);
      
          PROP_ID_TO_NAME[PROP_ID_navigateHide] = PROP_NAME_navigateHide;
          PROP_NAME_TO_ID.put(PROP_NAME_navigateHide, PROP_ID_navigateHide);
      
          PROP_ID_TO_NAME[PROP_ID_isWorksheetQuery] = PROP_NAME_isWorksheetQuery;
          PROP_NAME_TO_ID.put(PROP_NAME_isWorksheetQuery, PROP_ID_isWorksheetQuery);
      
          PROP_ID_TO_NAME[PROP_ID_isMarked] = PROP_NAME_isMarked;
          PROP_NAME_TO_ID.put(PROP_NAME_isMarked, PROP_ID_isMarked);
      
          PROP_ID_TO_NAME[PROP_ID_controls] = PROP_NAME_controls;
          PROP_NAME_TO_ID.put(PROP_NAME_controls, PROP_ID_controls);
      
          PROP_ID_TO_NAME[PROP_ID_advancedSetting] = PROP_NAME_advancedSetting;
          PROP_NAME_TO_ID.put(PROP_NAME_advancedSetting, PROP_ID_advancedSetting);
      
          PROP_ID_TO_NAME[PROP_ID_submitSetting] = PROP_NAME_submitSetting;
          PROP_NAME_TO_ID.put(PROP_NAME_submitSetting, PROP_ID_submitSetting);
      
          PROP_ID_TO_NAME[PROP_ID_developerNotes] = PROP_NAME_developerNotes;
          PROP_NAME_TO_ID.put(PROP_NAME_developerNotes, PROP_ID_developerNotes);
      
          PROP_ID_TO_NAME[PROP_ID_resume] = PROP_NAME_resume;
          PROP_NAME_TO_ID.put(PROP_NAME_resume, PROP_ID_resume);
      
          PROP_ID_TO_NAME[PROP_ID_dec] = PROP_NAME_dec;
          PROP_NAME_TO_ID.put(PROP_NAME_dec, PROP_ID_dec);
      
          PROP_ID_TO_NAME[PROP_ID_status] = PROP_NAME_status;
          PROP_NAME_TO_ID.put(PROP_NAME_status, PROP_ID_status);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 工作表ID: worksheet_id */
    private java.lang.String _worksheetId;
    
    /* 应用 ID: app_id */
    private java.lang.String _appId;
    
    /* 应用分组 ID: app_section_id */
    private java.lang.String _appSectionId;
    
    /* 模型对象 ID: model_object_entity_id */
    private java.lang.String _modelObjectEntityId;
    
    /* 模型对象名称: model_object_entity_name */
    private java.lang.String _modelObjectEntityName;
    
    /* 工作表名称: name */
    private java.lang.String _name;
    
    /* 工作表别名: alias */
    private java.lang.String _alias;
    
    /* 记录名称: entity_name */
    private java.lang.String _entityName;
    
    /* 图标: icon */
    private java.lang.String _icon;
    
    /* 图标颜色: icon_color */
    private java.lang.String _iconColor;
    
    /* 图标路径: icon_url */
    private java.lang.String _iconUrl;
    
    /* 数量: count */
    private java.lang.Integer _count;
    
    /* 分享状态: visible_type */
    private java.lang.Integer _visibleType;
    
    /* 开启审批: open_approval */
    private java.lang.Boolean _openApproval;
    
    /* 工作流子表切换: workflow_child_table_switch */
    private java.lang.Boolean _workflowChildTableSwitch;
    
    /* 侧边栏状态: navigate_hide */
    private java.lang.Boolean _navigateHide;
    
    /* 是否配置工作表查询: is_worksheet_query */
    private java.lang.Boolean _isWorksheetQuery;
    
    /* 收藏状态: is_marked */
    private java.lang.Boolean _isMarked;
    
    /* 控件: controls */
    private java.lang.String _controls;
    
    /* 高级设置: advanced_setting */
    private java.lang.String _advancedSetting;
    
    /* 提交设置: submit_setting */
    private java.lang.String _submitSetting;
    
    /* 开发者备注: developer_notes */
    private java.lang.String _developerNotes;
    
    /* 摘要: resume */
    private java.lang.String _resume;
    
    /* 详细说明: dec */
    private java.lang.String _dec;
    
    /* 状态: status */
    private java.lang.Integer _status;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppWorksheet(){
        // for debug
    }

    protected MlcAppWorksheet newInstance(){
        MlcAppWorksheet entity = new MlcAppWorksheet();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppWorksheet cloneInstance() {
        MlcAppWorksheet entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppWorksheet";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_worksheetId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_worksheetId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_worksheetId:
               return getWorksheetId();
        
            case PROP_ID_appId:
               return getAppId();
        
            case PROP_ID_appSectionId:
               return getAppSectionId();
        
            case PROP_ID_modelObjectEntityId:
               return getModelObjectEntityId();
        
            case PROP_ID_modelObjectEntityName:
               return getModelObjectEntityName();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_alias:
               return getAlias();
        
            case PROP_ID_entityName:
               return getEntityName();
        
            case PROP_ID_icon:
               return getIcon();
        
            case PROP_ID_iconColor:
               return getIconColor();
        
            case PROP_ID_iconUrl:
               return getIconUrl();
        
            case PROP_ID_count:
               return getCount();
        
            case PROP_ID_visibleType:
               return getVisibleType();
        
            case PROP_ID_openApproval:
               return getOpenApproval();
        
            case PROP_ID_workflowChildTableSwitch:
               return getWorkflowChildTableSwitch();
        
            case PROP_ID_navigateHide:
               return getNavigateHide();
        
            case PROP_ID_isWorksheetQuery:
               return getIsWorksheetQuery();
        
            case PROP_ID_isMarked:
               return getIsMarked();
        
            case PROP_ID_controls:
               return getControls();
        
            case PROP_ID_advancedSetting:
               return getAdvancedSetting();
        
            case PROP_ID_submitSetting:
               return getSubmitSetting();
        
            case PROP_ID_developerNotes:
               return getDeveloperNotes();
        
            case PROP_ID_resume:
               return getResume();
        
            case PROP_ID_dec:
               return getDec();
        
            case PROP_ID_status:
               return getStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_worksheetId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_worksheetId));
               }
               setWorksheetId(typedValue);
               break;
            }
        
            case PROP_ID_appId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appId));
               }
               setAppId(typedValue);
               break;
            }
        
            case PROP_ID_appSectionId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appSectionId));
               }
               setAppSectionId(typedValue);
               break;
            }
        
            case PROP_ID_modelObjectEntityId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_modelObjectEntityId));
               }
               setModelObjectEntityId(typedValue);
               break;
            }
        
            case PROP_ID_modelObjectEntityName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_modelObjectEntityName));
               }
               setModelObjectEntityName(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_alias:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_alias));
               }
               setAlias(typedValue);
               break;
            }
        
            case PROP_ID_entityName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_entityName));
               }
               setEntityName(typedValue);
               break;
            }
        
            case PROP_ID_icon:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_icon));
               }
               setIcon(typedValue);
               break;
            }
        
            case PROP_ID_iconColor:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_iconColor));
               }
               setIconColor(typedValue);
               break;
            }
        
            case PROP_ID_iconUrl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_iconUrl));
               }
               setIconUrl(typedValue);
               break;
            }
        
            case PROP_ID_count:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_count));
               }
               setCount(typedValue);
               break;
            }
        
            case PROP_ID_visibleType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_visibleType));
               }
               setVisibleType(typedValue);
               break;
            }
        
            case PROP_ID_openApproval:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_openApproval));
               }
               setOpenApproval(typedValue);
               break;
            }
        
            case PROP_ID_workflowChildTableSwitch:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_workflowChildTableSwitch));
               }
               setWorkflowChildTableSwitch(typedValue);
               break;
            }
        
            case PROP_ID_navigateHide:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_navigateHide));
               }
               setNavigateHide(typedValue);
               break;
            }
        
            case PROP_ID_isWorksheetQuery:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isWorksheetQuery));
               }
               setIsWorksheetQuery(typedValue);
               break;
            }
        
            case PROP_ID_isMarked:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isMarked));
               }
               setIsMarked(typedValue);
               break;
            }
        
            case PROP_ID_controls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_controls));
               }
               setControls(typedValue);
               break;
            }
        
            case PROP_ID_advancedSetting:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_advancedSetting));
               }
               setAdvancedSetting(typedValue);
               break;
            }
        
            case PROP_ID_submitSetting:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_submitSetting));
               }
               setSubmitSetting(typedValue);
               break;
            }
        
            case PROP_ID_developerNotes:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_developerNotes));
               }
               setDeveloperNotes(typedValue);
               break;
            }
        
            case PROP_ID_resume:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_resume));
               }
               setResume(typedValue);
               break;
            }
        
            case PROP_ID_dec:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_dec));
               }
               setDec(typedValue);
               break;
            }
        
            case PROP_ID_status:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_status));
               }
               setStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_worksheetId:{
               onInitProp(propId);
               this._worksheetId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_appId:{
               onInitProp(propId);
               this._appId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_appSectionId:{
               onInitProp(propId);
               this._appSectionId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_modelObjectEntityId:{
               onInitProp(propId);
               this._modelObjectEntityId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_modelObjectEntityName:{
               onInitProp(propId);
               this._modelObjectEntityName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_alias:{
               onInitProp(propId);
               this._alias = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_entityName:{
               onInitProp(propId);
               this._entityName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_icon:{
               onInitProp(propId);
               this._icon = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_iconColor:{
               onInitProp(propId);
               this._iconColor = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_iconUrl:{
               onInitProp(propId);
               this._iconUrl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_count:{
               onInitProp(propId);
               this._count = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_visibleType:{
               onInitProp(propId);
               this._visibleType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_openApproval:{
               onInitProp(propId);
               this._openApproval = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_workflowChildTableSwitch:{
               onInitProp(propId);
               this._workflowChildTableSwitch = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_navigateHide:{
               onInitProp(propId);
               this._navigateHide = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isWorksheetQuery:{
               onInitProp(propId);
               this._isWorksheetQuery = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isMarked:{
               onInitProp(propId);
               this._isMarked = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_controls:{
               onInitProp(propId);
               this._controls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_advancedSetting:{
               onInitProp(propId);
               this._advancedSetting = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_submitSetting:{
               onInitProp(propId);
               this._submitSetting = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_developerNotes:{
               onInitProp(propId);
               this._developerNotes = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_resume:{
               onInitProp(propId);
               this._resume = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_dec:{
               onInitProp(propId);
               this._dec = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_status:{
               onInitProp(propId);
               this._status = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 工作表ID: worksheet_id
     */
    public final java.lang.String getWorksheetId(){
         onPropGet(PROP_ID_worksheetId);
         return _worksheetId;
    }

    /**
     * 工作表ID: worksheet_id
     */
    public final void setWorksheetId(java.lang.String value){
        if(onPropSet(PROP_ID_worksheetId,value)){
            this._worksheetId = value;
            internalClearRefs(PROP_ID_worksheetId);
            orm_id();
        }
    }
    
    /**
     * 应用 ID: app_id
     */
    public final java.lang.String getAppId(){
         onPropGet(PROP_ID_appId);
         return _appId;
    }

    /**
     * 应用 ID: app_id
     */
    public final void setAppId(java.lang.String value){
        if(onPropSet(PROP_ID_appId,value)){
            this._appId = value;
            internalClearRefs(PROP_ID_appId);
            
        }
    }
    
    /**
     * 应用分组 ID: app_section_id
     */
    public final java.lang.String getAppSectionId(){
         onPropGet(PROP_ID_appSectionId);
         return _appSectionId;
    }

    /**
     * 应用分组 ID: app_section_id
     */
    public final void setAppSectionId(java.lang.String value){
        if(onPropSet(PROP_ID_appSectionId,value)){
            this._appSectionId = value;
            internalClearRefs(PROP_ID_appSectionId);
            
        }
    }
    
    /**
     * 模型对象 ID: model_object_entity_id
     */
    public final java.lang.String getModelObjectEntityId(){
         onPropGet(PROP_ID_modelObjectEntityId);
         return _modelObjectEntityId;
    }

    /**
     * 模型对象 ID: model_object_entity_id
     */
    public final void setModelObjectEntityId(java.lang.String value){
        if(onPropSet(PROP_ID_modelObjectEntityId,value)){
            this._modelObjectEntityId = value;
            internalClearRefs(PROP_ID_modelObjectEntityId);
            
        }
    }
    
    /**
     * 模型对象名称: model_object_entity_name
     */
    public final java.lang.String getModelObjectEntityName(){
         onPropGet(PROP_ID_modelObjectEntityName);
         return _modelObjectEntityName;
    }

    /**
     * 模型对象名称: model_object_entity_name
     */
    public final void setModelObjectEntityName(java.lang.String value){
        if(onPropSet(PROP_ID_modelObjectEntityName,value)){
            this._modelObjectEntityName = value;
            internalClearRefs(PROP_ID_modelObjectEntityName);
            
        }
    }
    
    /**
     * 工作表名称: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 工作表名称: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 工作表别名: alias
     */
    public final java.lang.String getAlias(){
         onPropGet(PROP_ID_alias);
         return _alias;
    }

    /**
     * 工作表别名: alias
     */
    public final void setAlias(java.lang.String value){
        if(onPropSet(PROP_ID_alias,value)){
            this._alias = value;
            internalClearRefs(PROP_ID_alias);
            
        }
    }
    
    /**
     * 记录名称: entity_name
     */
    public final java.lang.String getEntityName(){
         onPropGet(PROP_ID_entityName);
         return _entityName;
    }

    /**
     * 记录名称: entity_name
     */
    public final void setEntityName(java.lang.String value){
        if(onPropSet(PROP_ID_entityName,value)){
            this._entityName = value;
            internalClearRefs(PROP_ID_entityName);
            
        }
    }
    
    /**
     * 图标: icon
     */
    public final java.lang.String getIcon(){
         onPropGet(PROP_ID_icon);
         return _icon;
    }

    /**
     * 图标: icon
     */
    public final void setIcon(java.lang.String value){
        if(onPropSet(PROP_ID_icon,value)){
            this._icon = value;
            internalClearRefs(PROP_ID_icon);
            
        }
    }
    
    /**
     * 图标颜色: icon_color
     */
    public final java.lang.String getIconColor(){
         onPropGet(PROP_ID_iconColor);
         return _iconColor;
    }

    /**
     * 图标颜色: icon_color
     */
    public final void setIconColor(java.lang.String value){
        if(onPropSet(PROP_ID_iconColor,value)){
            this._iconColor = value;
            internalClearRefs(PROP_ID_iconColor);
            
        }
    }
    
    /**
     * 图标路径: icon_url
     */
    public final java.lang.String getIconUrl(){
         onPropGet(PROP_ID_iconUrl);
         return _iconUrl;
    }

    /**
     * 图标路径: icon_url
     */
    public final void setIconUrl(java.lang.String value){
        if(onPropSet(PROP_ID_iconUrl,value)){
            this._iconUrl = value;
            internalClearRefs(PROP_ID_iconUrl);
            
        }
    }
    
    /**
     * 数量: count
     */
    public final java.lang.Integer getCount(){
         onPropGet(PROP_ID_count);
         return _count;
    }

    /**
     * 数量: count
     */
    public final void setCount(java.lang.Integer value){
        if(onPropSet(PROP_ID_count,value)){
            this._count = value;
            internalClearRefs(PROP_ID_count);
            
        }
    }
    
    /**
     * 分享状态: visible_type
     */
    public final java.lang.Integer getVisibleType(){
         onPropGet(PROP_ID_visibleType);
         return _visibleType;
    }

    /**
     * 分享状态: visible_type
     */
    public final void setVisibleType(java.lang.Integer value){
        if(onPropSet(PROP_ID_visibleType,value)){
            this._visibleType = value;
            internalClearRefs(PROP_ID_visibleType);
            
        }
    }
    
    /**
     * 开启审批: open_approval
     */
    public final java.lang.Boolean getOpenApproval(){
         onPropGet(PROP_ID_openApproval);
         return _openApproval;
    }

    /**
     * 开启审批: open_approval
     */
    public final void setOpenApproval(java.lang.Boolean value){
        if(onPropSet(PROP_ID_openApproval,value)){
            this._openApproval = value;
            internalClearRefs(PROP_ID_openApproval);
            
        }
    }
    
    /**
     * 工作流子表切换: workflow_child_table_switch
     */
    public final java.lang.Boolean getWorkflowChildTableSwitch(){
         onPropGet(PROP_ID_workflowChildTableSwitch);
         return _workflowChildTableSwitch;
    }

    /**
     * 工作流子表切换: workflow_child_table_switch
     */
    public final void setWorkflowChildTableSwitch(java.lang.Boolean value){
        if(onPropSet(PROP_ID_workflowChildTableSwitch,value)){
            this._workflowChildTableSwitch = value;
            internalClearRefs(PROP_ID_workflowChildTableSwitch);
            
        }
    }
    
    /**
     * 侧边栏状态: navigate_hide
     */
    public final java.lang.Boolean getNavigateHide(){
         onPropGet(PROP_ID_navigateHide);
         return _navigateHide;
    }

    /**
     * 侧边栏状态: navigate_hide
     */
    public final void setNavigateHide(java.lang.Boolean value){
        if(onPropSet(PROP_ID_navigateHide,value)){
            this._navigateHide = value;
            internalClearRefs(PROP_ID_navigateHide);
            
        }
    }
    
    /**
     * 是否配置工作表查询: is_worksheet_query
     */
    public final java.lang.Boolean getIsWorksheetQuery(){
         onPropGet(PROP_ID_isWorksheetQuery);
         return _isWorksheetQuery;
    }

    /**
     * 是否配置工作表查询: is_worksheet_query
     */
    public final void setIsWorksheetQuery(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isWorksheetQuery,value)){
            this._isWorksheetQuery = value;
            internalClearRefs(PROP_ID_isWorksheetQuery);
            
        }
    }
    
    /**
     * 收藏状态: is_marked
     */
    public final java.lang.Boolean getIsMarked(){
         onPropGet(PROP_ID_isMarked);
         return _isMarked;
    }

    /**
     * 收藏状态: is_marked
     */
    public final void setIsMarked(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isMarked,value)){
            this._isMarked = value;
            internalClearRefs(PROP_ID_isMarked);
            
        }
    }
    
    /**
     * 控件: controls
     */
    public final java.lang.String getControls(){
         onPropGet(PROP_ID_controls);
         return _controls;
    }

    /**
     * 控件: controls
     */
    public final void setControls(java.lang.String value){
        if(onPropSet(PROP_ID_controls,value)){
            this._controls = value;
            internalClearRefs(PROP_ID_controls);
            
        }
    }
    
    /**
     * 高级设置: advanced_setting
     */
    public final java.lang.String getAdvancedSetting(){
         onPropGet(PROP_ID_advancedSetting);
         return _advancedSetting;
    }

    /**
     * 高级设置: advanced_setting
     */
    public final void setAdvancedSetting(java.lang.String value){
        if(onPropSet(PROP_ID_advancedSetting,value)){
            this._advancedSetting = value;
            internalClearRefs(PROP_ID_advancedSetting);
            
        }
    }
    
    /**
     * 提交设置: submit_setting
     */
    public final java.lang.String getSubmitSetting(){
         onPropGet(PROP_ID_submitSetting);
         return _submitSetting;
    }

    /**
     * 提交设置: submit_setting
     */
    public final void setSubmitSetting(java.lang.String value){
        if(onPropSet(PROP_ID_submitSetting,value)){
            this._submitSetting = value;
            internalClearRefs(PROP_ID_submitSetting);
            
        }
    }
    
    /**
     * 开发者备注: developer_notes
     */
    public final java.lang.String getDeveloperNotes(){
         onPropGet(PROP_ID_developerNotes);
         return _developerNotes;
    }

    /**
     * 开发者备注: developer_notes
     */
    public final void setDeveloperNotes(java.lang.String value){
        if(onPropSet(PROP_ID_developerNotes,value)){
            this._developerNotes = value;
            internalClearRefs(PROP_ID_developerNotes);
            
        }
    }
    
    /**
     * 摘要: resume
     */
    public final java.lang.String getResume(){
         onPropGet(PROP_ID_resume);
         return _resume;
    }

    /**
     * 摘要: resume
     */
    public final void setResume(java.lang.String value){
        if(onPropSet(PROP_ID_resume,value)){
            this._resume = value;
            internalClearRefs(PROP_ID_resume);
            
        }
    }
    
    /**
     * 详细说明: dec
     */
    public final java.lang.String getDec(){
         onPropGet(PROP_ID_dec);
         return _dec;
    }

    /**
     * 详细说明: dec
     */
    public final void setDec(java.lang.String value){
        if(onPropSet(PROP_ID_dec,value)){
            this._dec = value;
            internalClearRefs(PROP_ID_dec);
            
        }
    }
    
    /**
     * 状态: status
     */
    public final java.lang.Integer getStatus(){
         onPropGet(PROP_ID_status);
         return _status;
    }

    /**
     * 状态: status
     */
    public final void setStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_status,value)){
            this._status = value;
            internalClearRefs(PROP_ID_status);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 所属应用分组
     */
    public final com.mlc.application.dao.entity.MlcAppSection getAppSection(){
       return (com.mlc.application.dao.entity.MlcAppSection)internalGetRefEntity(PROP_NAME_appSection);
    }

    public final void setAppSection(com.mlc.application.dao.entity.MlcAppSection refEntity){
   
           if(refEntity == null){
           
                   this.setAppSectionId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appSection, refEntity,()->{
           
                           this.setAppSectionId(refEntity.getAppSectionId());
                       
           });
           }
       
    }
       
    /**
     * 应用信息
     */
    public final com.mlc.application.dao.entity.MlcAppInfo getAppInfo(){
       return (com.mlc.application.dao.entity.MlcAppInfo)internalGetRefEntity(PROP_NAME_appInfo);
    }

    public final void setAppInfo(com.mlc.application.dao.entity.MlcAppInfo refEntity){
   
           if(refEntity == null){
           
                   this.setAppId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appInfo, refEntity,()->{
           
                           this.setAppId(refEntity.getAppId());
                       
           });
           }
       
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetView> _worksheetViews = new OrmEntitySet<>(this, PROP_NAME_worksheetViews,
        com.mlc.application.dao.entity.MlcAppWorksheetView.PROP_NAME_worksheet, null,com.mlc.application.dao.entity.MlcAppWorksheetView.class);

    /**
     * 关联工作表视图。 refPropName: worksheet, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetView> getWorksheetViews(){
       return _worksheetViews;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetRule> _worksheetRuleItems = new OrmEntitySet<>(this, PROP_NAME_worksheetRuleItems,
        com.mlc.application.dao.entity.MlcAppWorksheetRule.PROP_NAME_worksheet, null,com.mlc.application.dao.entity.MlcAppWorksheetRule.class);

    /**
     * 关联工作表规则。 refPropName: worksheet, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetRule> getWorksheetRuleItems(){
       return _worksheetRuleItems;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetButton> _worksheetBtns = new OrmEntitySet<>(this, PROP_NAME_worksheetBtns,
        com.mlc.application.dao.entity.MlcAppWorksheetButton.PROP_NAME_worksheet, null,com.mlc.application.dao.entity.MlcAppWorksheetButton.class);

    /**
     * 关联工作表按钮。 refPropName: worksheet, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetButton> getWorksheetBtns(){
       return _worksheetBtns;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate> _worksheetPrintTemplates = new OrmEntitySet<>(this, PROP_NAME_worksheetPrintTemplates,
        com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate.PROP_NAME_worksheet, null,com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate.class);

    /**
     * 关联工作表打印模板。 refPropName: worksheet, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate> getWorksheetPrintTemplates(){
       return _worksheetPrintTemplates;
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetSwitch> _worksheetSwitchs = new OrmEntitySet<>(this, PROP_NAME_worksheetSwitchs,
        com.mlc.application.dao.entity.MlcAppWorksheetSwitch.PROP_NAME_worksheet, null,com.mlc.application.dao.entity.MlcAppWorksheetSwitch.class);

    /**
     * 关联工作表开发。 refPropName: worksheet, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppWorksheetSwitch> getWorksheetSwitchs(){
       return _worksheetSwitchs;
    }
       
   private io.nop.orm.component.JsonOrmComponent _advancedSettingComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_advancedSettingComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_advancedSettingComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_advancedSetting);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getAdvancedSettingComponent(){
      if(_advancedSettingComponent == null){
          _advancedSettingComponent = new io.nop.orm.component.JsonOrmComponent();
          _advancedSettingComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_advancedSettingComponent);
      }
      return _advancedSettingComponent;
   }

   private io.nop.orm.component.JsonOrmComponent _submitSettingComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_submitSettingComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_submitSettingComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_submitSetting);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getSubmitSettingComponent(){
      if(_submitSettingComponent == null){
          _submitSettingComponent = new io.nop.orm.component.JsonOrmComponent();
          _submitSettingComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_submitSettingComponent);
      }
      return _submitSettingComponent;
   }

}
// resume CPD analysis - CPD-ON
