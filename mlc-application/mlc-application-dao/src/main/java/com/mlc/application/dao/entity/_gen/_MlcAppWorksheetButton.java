package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppWorksheetButton;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  工作表自定义按钮: mlc_app_worksheet_button
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppWorksheetButton extends DynamicOrmEntity{
    
    /* 按钮ID: btn_id VARCHAR */
    public static final String PROP_NAME_btnId = "btnId";
    public static final int PROP_ID_btnId = 1;
    
    /* 工作表ID: worksheet_id VARCHAR */
    public static final String PROP_NAME_worksheetId = "worksheetId";
    public static final int PROP_ID_worksheetId = 2;
    
    /* 名称: name VARCHAR */
    public static final String PROP_NAME_name = "name";
    public static final int PROP_ID_name = 3;
    
    /* 是否全部视图: is_all_view INTEGER */
    public static final String PROP_NAME_isAllView = "isAllView";
    public static final int PROP_ID_isAllView = 4;
    
    /* 显示视图: display_views VARCHAR */
    public static final String PROP_NAME_displayViews = "displayViews";
    public static final int PROP_ID_displayViews = 5;
    
    /* 点击类型: click_type INTEGER */
    public static final String PROP_NAME_clickType = "clickType";
    public static final int PROP_ID_clickType = 6;
    
    /* 确认消息: confirm_msg VARCHAR */
    public static final String PROP_NAME_confirmMsg = "confirmMsg";
    public static final int PROP_ID_confirmMsg = 7;
    
    /* 确认名称: sure_name VARCHAR */
    public static final String PROP_NAME_sureName = "sureName";
    public static final int PROP_ID_sureName = 8;
    
    /* 取消名称: cancel_name VARCHAR */
    public static final String PROP_NAME_cancelName = "cancelName";
    public static final int PROP_ID_cancelName = 9;
    
    /* 填写对象类型: write_type INTEGER */
    public static final String PROP_NAME_writeType = "writeType";
    public static final int PROP_ID_writeType = 10;
    
    /* 填写内容: write_object INTEGER */
    public static final String PROP_NAME_writeObject = "writeObject";
    public static final int PROP_ID_writeObject = 11;
    
    /* 填写控件: write_controls VARCHAR */
    public static final String PROP_NAME_writeControls = "writeControls";
    public static final int PROP_ID_writeControls = 12;
    
    /* 关联记录ID: relation_control VARCHAR */
    public static final String PROP_NAME_relationControl = "relationControl";
    public static final int PROP_ID_relationControl = 13;
    
    /* 新建关联记录ID: add_relation_control_id VARCHAR */
    public static final String PROP_NAME_addRelationControlId = "addRelationControlId";
    public static final int PROP_ID_addRelationControlId = 14;
    
    /* 继续执行工作流: workflow_type INTEGER */
    public static final String PROP_NAME_workflowType = "workflowType";
    public static final int PROP_ID_workflowType = 15;
    
    /* 工作流ID: workflow_id VARCHAR */
    public static final String PROP_NAME_workflowId = "workflowId";
    public static final int PROP_ID_workflowId = 16;
    
    /* 过滤器: filters VARCHAR */
    public static final String PROP_NAME_filters = "filters";
    public static final int PROP_ID_filters = 17;
    
    /* 颜色: color VARCHAR */
    public static final String PROP_NAME_color = "color";
    public static final int PROP_ID_color = 18;
    
    /* 图标: icon VARCHAR */
    public static final String PROP_NAME_icon = "icon";
    public static final int PROP_ID_icon = 19;
    
    /* 图标URL: icon_url VARCHAR */
    public static final String PROP_NAME_iconUrl = "iconUrl";
    public static final int PROP_ID_iconUrl = 20;
    
    /* 描述: desc VARCHAR */
    public static final String PROP_NAME_desc = "desc";
    public static final int PROP_ID_desc = 21;
    
    /* 高级设置: advanced_setting VARCHAR */
    public static final String PROP_NAME_advancedSetting = "advancedSetting";
    public static final int PROP_ID_advancedSetting = 22;
    
    /* 启用二次确认: enable_confirm BOOLEAN */
    public static final String PROP_NAME_enableConfirm = "enableConfirm";
    public static final int PROP_ID_enableConfirm = 23;
    
    /* 校验密码: verify_pwd BOOLEAN */
    public static final String PROP_NAME_verifyPwd = "verifyPwd";
    public static final int PROP_ID_verifyPwd = 24;
    
    /* 是否多条数据源: is_batch BOOLEAN */
    public static final String PROP_NAME_isBatch = "isBatch";
    public static final int PROP_ID_isBatch = 25;
    
    /* 启用按钮类型: show_type INTEGER */
    public static final String PROP_NAME_showType = "showType";
    public static final int PROP_ID_showType = 26;
    
    /* 添加关联控件: add_relation_control VARCHAR */
    public static final String PROP_NAME_addRelationControl = "addRelationControl";
    public static final int PROP_ID_addRelationControl = 27;
    
    /* 属性: edit_attrs VARCHAR */
    public static final String PROP_NAME_editAttrs = "editAttrs";
    public static final int PROP_ID_editAttrs = 28;
    
    /* 禁用: disabled BOOLEAN */
    public static final String PROP_NAME_disabled = "disabled";
    public static final int PROP_ID_disabled = 29;
    
    /* 状态: status INTEGER */
    public static final String PROP_NAME_status = "status";
    public static final int PROP_ID_status = 30;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 31;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 32;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 33;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 34;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 35;
    

    private static int _PROP_ID_BOUND = 36;

    
    /* relation: 所属工作表 */
    public static final String PROP_NAME_worksheet = "worksheet";
    
    /* component:  */
    public static final String PROP_NAME_advancedSettingComponent = "advancedSettingComponent";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_btnId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_btnId};

    private static final String[] PROP_ID_TO_NAME = new String[36];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_btnId] = PROP_NAME_btnId;
          PROP_NAME_TO_ID.put(PROP_NAME_btnId, PROP_ID_btnId);
      
          PROP_ID_TO_NAME[PROP_ID_worksheetId] = PROP_NAME_worksheetId;
          PROP_NAME_TO_ID.put(PROP_NAME_worksheetId, PROP_ID_worksheetId);
      
          PROP_ID_TO_NAME[PROP_ID_name] = PROP_NAME_name;
          PROP_NAME_TO_ID.put(PROP_NAME_name, PROP_ID_name);
      
          PROP_ID_TO_NAME[PROP_ID_isAllView] = PROP_NAME_isAllView;
          PROP_NAME_TO_ID.put(PROP_NAME_isAllView, PROP_ID_isAllView);
      
          PROP_ID_TO_NAME[PROP_ID_displayViews] = PROP_NAME_displayViews;
          PROP_NAME_TO_ID.put(PROP_NAME_displayViews, PROP_ID_displayViews);
      
          PROP_ID_TO_NAME[PROP_ID_clickType] = PROP_NAME_clickType;
          PROP_NAME_TO_ID.put(PROP_NAME_clickType, PROP_ID_clickType);
      
          PROP_ID_TO_NAME[PROP_ID_confirmMsg] = PROP_NAME_confirmMsg;
          PROP_NAME_TO_ID.put(PROP_NAME_confirmMsg, PROP_ID_confirmMsg);
      
          PROP_ID_TO_NAME[PROP_ID_sureName] = PROP_NAME_sureName;
          PROP_NAME_TO_ID.put(PROP_NAME_sureName, PROP_ID_sureName);
      
          PROP_ID_TO_NAME[PROP_ID_cancelName] = PROP_NAME_cancelName;
          PROP_NAME_TO_ID.put(PROP_NAME_cancelName, PROP_ID_cancelName);
      
          PROP_ID_TO_NAME[PROP_ID_writeType] = PROP_NAME_writeType;
          PROP_NAME_TO_ID.put(PROP_NAME_writeType, PROP_ID_writeType);
      
          PROP_ID_TO_NAME[PROP_ID_writeObject] = PROP_NAME_writeObject;
          PROP_NAME_TO_ID.put(PROP_NAME_writeObject, PROP_ID_writeObject);
      
          PROP_ID_TO_NAME[PROP_ID_writeControls] = PROP_NAME_writeControls;
          PROP_NAME_TO_ID.put(PROP_NAME_writeControls, PROP_ID_writeControls);
      
          PROP_ID_TO_NAME[PROP_ID_relationControl] = PROP_NAME_relationControl;
          PROP_NAME_TO_ID.put(PROP_NAME_relationControl, PROP_ID_relationControl);
      
          PROP_ID_TO_NAME[PROP_ID_addRelationControlId] = PROP_NAME_addRelationControlId;
          PROP_NAME_TO_ID.put(PROP_NAME_addRelationControlId, PROP_ID_addRelationControlId);
      
          PROP_ID_TO_NAME[PROP_ID_workflowType] = PROP_NAME_workflowType;
          PROP_NAME_TO_ID.put(PROP_NAME_workflowType, PROP_ID_workflowType);
      
          PROP_ID_TO_NAME[PROP_ID_workflowId] = PROP_NAME_workflowId;
          PROP_NAME_TO_ID.put(PROP_NAME_workflowId, PROP_ID_workflowId);
      
          PROP_ID_TO_NAME[PROP_ID_filters] = PROP_NAME_filters;
          PROP_NAME_TO_ID.put(PROP_NAME_filters, PROP_ID_filters);
      
          PROP_ID_TO_NAME[PROP_ID_color] = PROP_NAME_color;
          PROP_NAME_TO_ID.put(PROP_NAME_color, PROP_ID_color);
      
          PROP_ID_TO_NAME[PROP_ID_icon] = PROP_NAME_icon;
          PROP_NAME_TO_ID.put(PROP_NAME_icon, PROP_ID_icon);
      
          PROP_ID_TO_NAME[PROP_ID_iconUrl] = PROP_NAME_iconUrl;
          PROP_NAME_TO_ID.put(PROP_NAME_iconUrl, PROP_ID_iconUrl);
      
          PROP_ID_TO_NAME[PROP_ID_desc] = PROP_NAME_desc;
          PROP_NAME_TO_ID.put(PROP_NAME_desc, PROP_ID_desc);
      
          PROP_ID_TO_NAME[PROP_ID_advancedSetting] = PROP_NAME_advancedSetting;
          PROP_NAME_TO_ID.put(PROP_NAME_advancedSetting, PROP_ID_advancedSetting);
      
          PROP_ID_TO_NAME[PROP_ID_enableConfirm] = PROP_NAME_enableConfirm;
          PROP_NAME_TO_ID.put(PROP_NAME_enableConfirm, PROP_ID_enableConfirm);
      
          PROP_ID_TO_NAME[PROP_ID_verifyPwd] = PROP_NAME_verifyPwd;
          PROP_NAME_TO_ID.put(PROP_NAME_verifyPwd, PROP_ID_verifyPwd);
      
          PROP_ID_TO_NAME[PROP_ID_isBatch] = PROP_NAME_isBatch;
          PROP_NAME_TO_ID.put(PROP_NAME_isBatch, PROP_ID_isBatch);
      
          PROP_ID_TO_NAME[PROP_ID_showType] = PROP_NAME_showType;
          PROP_NAME_TO_ID.put(PROP_NAME_showType, PROP_ID_showType);
      
          PROP_ID_TO_NAME[PROP_ID_addRelationControl] = PROP_NAME_addRelationControl;
          PROP_NAME_TO_ID.put(PROP_NAME_addRelationControl, PROP_ID_addRelationControl);
      
          PROP_ID_TO_NAME[PROP_ID_editAttrs] = PROP_NAME_editAttrs;
          PROP_NAME_TO_ID.put(PROP_NAME_editAttrs, PROP_ID_editAttrs);
      
          PROP_ID_TO_NAME[PROP_ID_disabled] = PROP_NAME_disabled;
          PROP_NAME_TO_ID.put(PROP_NAME_disabled, PROP_ID_disabled);
      
          PROP_ID_TO_NAME[PROP_ID_status] = PROP_NAME_status;
          PROP_NAME_TO_ID.put(PROP_NAME_status, PROP_ID_status);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 按钮ID: btn_id */
    private java.lang.String _btnId;
    
    /* 工作表ID: worksheet_id */
    private java.lang.String _worksheetId;
    
    /* 名称: name */
    private java.lang.String _name;
    
    /* 是否全部视图: is_all_view */
    private java.lang.Integer _isAllView;
    
    /* 显示视图: display_views */
    private java.lang.String _displayViews;
    
    /* 点击类型: click_type */
    private java.lang.Integer _clickType;
    
    /* 确认消息: confirm_msg */
    private java.lang.String _confirmMsg;
    
    /* 确认名称: sure_name */
    private java.lang.String _sureName;
    
    /* 取消名称: cancel_name */
    private java.lang.String _cancelName;
    
    /* 填写对象类型: write_type */
    private java.lang.Integer _writeType;
    
    /* 填写内容: write_object */
    private java.lang.Integer _writeObject;
    
    /* 填写控件: write_controls */
    private java.lang.String _writeControls;
    
    /* 关联记录ID: relation_control */
    private java.lang.String _relationControl;
    
    /* 新建关联记录ID: add_relation_control_id */
    private java.lang.String _addRelationControlId;
    
    /* 继续执行工作流: workflow_type */
    private java.lang.Integer _workflowType;
    
    /* 工作流ID: workflow_id */
    private java.lang.String _workflowId;
    
    /* 过滤器: filters */
    private java.lang.String _filters;
    
    /* 颜色: color */
    private java.lang.String _color;
    
    /* 图标: icon */
    private java.lang.String _icon;
    
    /* 图标URL: icon_url */
    private java.lang.String _iconUrl;
    
    /* 描述: desc */
    private java.lang.String _desc;
    
    /* 高级设置: advanced_setting */
    private java.lang.String _advancedSetting;
    
    /* 启用二次确认: enable_confirm */
    private java.lang.Boolean _enableConfirm;
    
    /* 校验密码: verify_pwd */
    private java.lang.Boolean _verifyPwd;
    
    /* 是否多条数据源: is_batch */
    private java.lang.Boolean _isBatch;
    
    /* 启用按钮类型: show_type */
    private java.lang.Integer _showType;
    
    /* 添加关联控件: add_relation_control */
    private java.lang.String _addRelationControl;
    
    /* 属性: edit_attrs */
    private java.lang.String _editAttrs;
    
    /* 禁用: disabled */
    private java.lang.Boolean _disabled;
    
    /* 状态: status */
    private java.lang.Integer _status;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppWorksheetButton(){
        // for debug
    }

    protected MlcAppWorksheetButton newInstance(){
        MlcAppWorksheetButton entity = new MlcAppWorksheetButton();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppWorksheetButton cloneInstance() {
        MlcAppWorksheetButton entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppWorksheetButton";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_btnId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_btnId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_btnId:
               return getBtnId();
        
            case PROP_ID_worksheetId:
               return getWorksheetId();
        
            case PROP_ID_name:
               return getName();
        
            case PROP_ID_isAllView:
               return getIsAllView();
        
            case PROP_ID_displayViews:
               return getDisplayViews();
        
            case PROP_ID_clickType:
               return getClickType();
        
            case PROP_ID_confirmMsg:
               return getConfirmMsg();
        
            case PROP_ID_sureName:
               return getSureName();
        
            case PROP_ID_cancelName:
               return getCancelName();
        
            case PROP_ID_writeType:
               return getWriteType();
        
            case PROP_ID_writeObject:
               return getWriteObject();
        
            case PROP_ID_writeControls:
               return getWriteControls();
        
            case PROP_ID_relationControl:
               return getRelationControl();
        
            case PROP_ID_addRelationControlId:
               return getAddRelationControlId();
        
            case PROP_ID_workflowType:
               return getWorkflowType();
        
            case PROP_ID_workflowId:
               return getWorkflowId();
        
            case PROP_ID_filters:
               return getFilters();
        
            case PROP_ID_color:
               return getColor();
        
            case PROP_ID_icon:
               return getIcon();
        
            case PROP_ID_iconUrl:
               return getIconUrl();
        
            case PROP_ID_desc:
               return getDesc();
        
            case PROP_ID_advancedSetting:
               return getAdvancedSetting();
        
            case PROP_ID_enableConfirm:
               return getEnableConfirm();
        
            case PROP_ID_verifyPwd:
               return getVerifyPwd();
        
            case PROP_ID_isBatch:
               return getIsBatch();
        
            case PROP_ID_showType:
               return getShowType();
        
            case PROP_ID_addRelationControl:
               return getAddRelationControl();
        
            case PROP_ID_editAttrs:
               return getEditAttrs();
        
            case PROP_ID_disabled:
               return getDisabled();
        
            case PROP_ID_status:
               return getStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_btnId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_btnId));
               }
               setBtnId(typedValue);
               break;
            }
        
            case PROP_ID_worksheetId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_worksheetId));
               }
               setWorksheetId(typedValue);
               break;
            }
        
            case PROP_ID_name:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_name));
               }
               setName(typedValue);
               break;
            }
        
            case PROP_ID_isAllView:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_isAllView));
               }
               setIsAllView(typedValue);
               break;
            }
        
            case PROP_ID_displayViews:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_displayViews));
               }
               setDisplayViews(typedValue);
               break;
            }
        
            case PROP_ID_clickType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_clickType));
               }
               setClickType(typedValue);
               break;
            }
        
            case PROP_ID_confirmMsg:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_confirmMsg));
               }
               setConfirmMsg(typedValue);
               break;
            }
        
            case PROP_ID_sureName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_sureName));
               }
               setSureName(typedValue);
               break;
            }
        
            case PROP_ID_cancelName:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_cancelName));
               }
               setCancelName(typedValue);
               break;
            }
        
            case PROP_ID_writeType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_writeType));
               }
               setWriteType(typedValue);
               break;
            }
        
            case PROP_ID_writeObject:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_writeObject));
               }
               setWriteObject(typedValue);
               break;
            }
        
            case PROP_ID_writeControls:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_writeControls));
               }
               setWriteControls(typedValue);
               break;
            }
        
            case PROP_ID_relationControl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_relationControl));
               }
               setRelationControl(typedValue);
               break;
            }
        
            case PROP_ID_addRelationControlId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_addRelationControlId));
               }
               setAddRelationControlId(typedValue);
               break;
            }
        
            case PROP_ID_workflowType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_workflowType));
               }
               setWorkflowType(typedValue);
               break;
            }
        
            case PROP_ID_workflowId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_workflowId));
               }
               setWorkflowId(typedValue);
               break;
            }
        
            case PROP_ID_filters:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_filters));
               }
               setFilters(typedValue);
               break;
            }
        
            case PROP_ID_color:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_color));
               }
               setColor(typedValue);
               break;
            }
        
            case PROP_ID_icon:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_icon));
               }
               setIcon(typedValue);
               break;
            }
        
            case PROP_ID_iconUrl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_iconUrl));
               }
               setIconUrl(typedValue);
               break;
            }
        
            case PROP_ID_desc:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_desc));
               }
               setDesc(typedValue);
               break;
            }
        
            case PROP_ID_advancedSetting:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_advancedSetting));
               }
               setAdvancedSetting(typedValue);
               break;
            }
        
            case PROP_ID_enableConfirm:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_enableConfirm));
               }
               setEnableConfirm(typedValue);
               break;
            }
        
            case PROP_ID_verifyPwd:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_verifyPwd));
               }
               setVerifyPwd(typedValue);
               break;
            }
        
            case PROP_ID_isBatch:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isBatch));
               }
               setIsBatch(typedValue);
               break;
            }
        
            case PROP_ID_showType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_showType));
               }
               setShowType(typedValue);
               break;
            }
        
            case PROP_ID_addRelationControl:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_addRelationControl));
               }
               setAddRelationControl(typedValue);
               break;
            }
        
            case PROP_ID_editAttrs:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_editAttrs));
               }
               setEditAttrs(typedValue);
               break;
            }
        
            case PROP_ID_disabled:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_disabled));
               }
               setDisabled(typedValue);
               break;
            }
        
            case PROP_ID_status:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_status));
               }
               setStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_btnId:{
               onInitProp(propId);
               this._btnId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_worksheetId:{
               onInitProp(propId);
               this._worksheetId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_name:{
               onInitProp(propId);
               this._name = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_isAllView:{
               onInitProp(propId);
               this._isAllView = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_displayViews:{
               onInitProp(propId);
               this._displayViews = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_clickType:{
               onInitProp(propId);
               this._clickType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_confirmMsg:{
               onInitProp(propId);
               this._confirmMsg = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_sureName:{
               onInitProp(propId);
               this._sureName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_cancelName:{
               onInitProp(propId);
               this._cancelName = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_writeType:{
               onInitProp(propId);
               this._writeType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_writeObject:{
               onInitProp(propId);
               this._writeObject = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_writeControls:{
               onInitProp(propId);
               this._writeControls = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_relationControl:{
               onInitProp(propId);
               this._relationControl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_addRelationControlId:{
               onInitProp(propId);
               this._addRelationControlId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_workflowType:{
               onInitProp(propId);
               this._workflowType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_workflowId:{
               onInitProp(propId);
               this._workflowId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_filters:{
               onInitProp(propId);
               this._filters = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_color:{
               onInitProp(propId);
               this._color = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_icon:{
               onInitProp(propId);
               this._icon = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_iconUrl:{
               onInitProp(propId);
               this._iconUrl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_desc:{
               onInitProp(propId);
               this._desc = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_advancedSetting:{
               onInitProp(propId);
               this._advancedSetting = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_enableConfirm:{
               onInitProp(propId);
               this._enableConfirm = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_verifyPwd:{
               onInitProp(propId);
               this._verifyPwd = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isBatch:{
               onInitProp(propId);
               this._isBatch = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_showType:{
               onInitProp(propId);
               this._showType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_addRelationControl:{
               onInitProp(propId);
               this._addRelationControl = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_editAttrs:{
               onInitProp(propId);
               this._editAttrs = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_disabled:{
               onInitProp(propId);
               this._disabled = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_status:{
               onInitProp(propId);
               this._status = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 按钮ID: btn_id
     */
    public final java.lang.String getBtnId(){
         onPropGet(PROP_ID_btnId);
         return _btnId;
    }

    /**
     * 按钮ID: btn_id
     */
    public final void setBtnId(java.lang.String value){
        if(onPropSet(PROP_ID_btnId,value)){
            this._btnId = value;
            internalClearRefs(PROP_ID_btnId);
            orm_id();
        }
    }
    
    /**
     * 工作表ID: worksheet_id
     */
    public final java.lang.String getWorksheetId(){
         onPropGet(PROP_ID_worksheetId);
         return _worksheetId;
    }

    /**
     * 工作表ID: worksheet_id
     */
    public final void setWorksheetId(java.lang.String value){
        if(onPropSet(PROP_ID_worksheetId,value)){
            this._worksheetId = value;
            internalClearRefs(PROP_ID_worksheetId);
            
        }
    }
    
    /**
     * 名称: name
     */
    public final java.lang.String getName(){
         onPropGet(PROP_ID_name);
         return _name;
    }

    /**
     * 名称: name
     */
    public final void setName(java.lang.String value){
        if(onPropSet(PROP_ID_name,value)){
            this._name = value;
            internalClearRefs(PROP_ID_name);
            
        }
    }
    
    /**
     * 是否全部视图: is_all_view
     */
    public final java.lang.Integer getIsAllView(){
         onPropGet(PROP_ID_isAllView);
         return _isAllView;
    }

    /**
     * 是否全部视图: is_all_view
     */
    public final void setIsAllView(java.lang.Integer value){
        if(onPropSet(PROP_ID_isAllView,value)){
            this._isAllView = value;
            internalClearRefs(PROP_ID_isAllView);
            
        }
    }
    
    /**
     * 显示视图: display_views
     */
    public final java.lang.String getDisplayViews(){
         onPropGet(PROP_ID_displayViews);
         return _displayViews;
    }

    /**
     * 显示视图: display_views
     */
    public final void setDisplayViews(java.lang.String value){
        if(onPropSet(PROP_ID_displayViews,value)){
            this._displayViews = value;
            internalClearRefs(PROP_ID_displayViews);
            
        }
    }
    
    /**
     * 点击类型: click_type
     */
    public final java.lang.Integer getClickType(){
         onPropGet(PROP_ID_clickType);
         return _clickType;
    }

    /**
     * 点击类型: click_type
     */
    public final void setClickType(java.lang.Integer value){
        if(onPropSet(PROP_ID_clickType,value)){
            this._clickType = value;
            internalClearRefs(PROP_ID_clickType);
            
        }
    }
    
    /**
     * 确认消息: confirm_msg
     */
    public final java.lang.String getConfirmMsg(){
         onPropGet(PROP_ID_confirmMsg);
         return _confirmMsg;
    }

    /**
     * 确认消息: confirm_msg
     */
    public final void setConfirmMsg(java.lang.String value){
        if(onPropSet(PROP_ID_confirmMsg,value)){
            this._confirmMsg = value;
            internalClearRefs(PROP_ID_confirmMsg);
            
        }
    }
    
    /**
     * 确认名称: sure_name
     */
    public final java.lang.String getSureName(){
         onPropGet(PROP_ID_sureName);
         return _sureName;
    }

    /**
     * 确认名称: sure_name
     */
    public final void setSureName(java.lang.String value){
        if(onPropSet(PROP_ID_sureName,value)){
            this._sureName = value;
            internalClearRefs(PROP_ID_sureName);
            
        }
    }
    
    /**
     * 取消名称: cancel_name
     */
    public final java.lang.String getCancelName(){
         onPropGet(PROP_ID_cancelName);
         return _cancelName;
    }

    /**
     * 取消名称: cancel_name
     */
    public final void setCancelName(java.lang.String value){
        if(onPropSet(PROP_ID_cancelName,value)){
            this._cancelName = value;
            internalClearRefs(PROP_ID_cancelName);
            
        }
    }
    
    /**
     * 填写对象类型: write_type
     */
    public final java.lang.Integer getWriteType(){
         onPropGet(PROP_ID_writeType);
         return _writeType;
    }

    /**
     * 填写对象类型: write_type
     */
    public final void setWriteType(java.lang.Integer value){
        if(onPropSet(PROP_ID_writeType,value)){
            this._writeType = value;
            internalClearRefs(PROP_ID_writeType);
            
        }
    }
    
    /**
     * 填写内容: write_object
     */
    public final java.lang.Integer getWriteObject(){
         onPropGet(PROP_ID_writeObject);
         return _writeObject;
    }

    /**
     * 填写内容: write_object
     */
    public final void setWriteObject(java.lang.Integer value){
        if(onPropSet(PROP_ID_writeObject,value)){
            this._writeObject = value;
            internalClearRefs(PROP_ID_writeObject);
            
        }
    }
    
    /**
     * 填写控件: write_controls
     */
    public final java.lang.String getWriteControls(){
         onPropGet(PROP_ID_writeControls);
         return _writeControls;
    }

    /**
     * 填写控件: write_controls
     */
    public final void setWriteControls(java.lang.String value){
        if(onPropSet(PROP_ID_writeControls,value)){
            this._writeControls = value;
            internalClearRefs(PROP_ID_writeControls);
            
        }
    }
    
    /**
     * 关联记录ID: relation_control
     */
    public final java.lang.String getRelationControl(){
         onPropGet(PROP_ID_relationControl);
         return _relationControl;
    }

    /**
     * 关联记录ID: relation_control
     */
    public final void setRelationControl(java.lang.String value){
        if(onPropSet(PROP_ID_relationControl,value)){
            this._relationControl = value;
            internalClearRefs(PROP_ID_relationControl);
            
        }
    }
    
    /**
     * 新建关联记录ID: add_relation_control_id
     */
    public final java.lang.String getAddRelationControlId(){
         onPropGet(PROP_ID_addRelationControlId);
         return _addRelationControlId;
    }

    /**
     * 新建关联记录ID: add_relation_control_id
     */
    public final void setAddRelationControlId(java.lang.String value){
        if(onPropSet(PROP_ID_addRelationControlId,value)){
            this._addRelationControlId = value;
            internalClearRefs(PROP_ID_addRelationControlId);
            
        }
    }
    
    /**
     * 继续执行工作流: workflow_type
     */
    public final java.lang.Integer getWorkflowType(){
         onPropGet(PROP_ID_workflowType);
         return _workflowType;
    }

    /**
     * 继续执行工作流: workflow_type
     */
    public final void setWorkflowType(java.lang.Integer value){
        if(onPropSet(PROP_ID_workflowType,value)){
            this._workflowType = value;
            internalClearRefs(PROP_ID_workflowType);
            
        }
    }
    
    /**
     * 工作流ID: workflow_id
     */
    public final java.lang.String getWorkflowId(){
         onPropGet(PROP_ID_workflowId);
         return _workflowId;
    }

    /**
     * 工作流ID: workflow_id
     */
    public final void setWorkflowId(java.lang.String value){
        if(onPropSet(PROP_ID_workflowId,value)){
            this._workflowId = value;
            internalClearRefs(PROP_ID_workflowId);
            
        }
    }
    
    /**
     * 过滤器: filters
     */
    public final java.lang.String getFilters(){
         onPropGet(PROP_ID_filters);
         return _filters;
    }

    /**
     * 过滤器: filters
     */
    public final void setFilters(java.lang.String value){
        if(onPropSet(PROP_ID_filters,value)){
            this._filters = value;
            internalClearRefs(PROP_ID_filters);
            
        }
    }
    
    /**
     * 颜色: color
     */
    public final java.lang.String getColor(){
         onPropGet(PROP_ID_color);
         return _color;
    }

    /**
     * 颜色: color
     */
    public final void setColor(java.lang.String value){
        if(onPropSet(PROP_ID_color,value)){
            this._color = value;
            internalClearRefs(PROP_ID_color);
            
        }
    }
    
    /**
     * 图标: icon
     */
    public final java.lang.String getIcon(){
         onPropGet(PROP_ID_icon);
         return _icon;
    }

    /**
     * 图标: icon
     */
    public final void setIcon(java.lang.String value){
        if(onPropSet(PROP_ID_icon,value)){
            this._icon = value;
            internalClearRefs(PROP_ID_icon);
            
        }
    }
    
    /**
     * 图标URL: icon_url
     */
    public final java.lang.String getIconUrl(){
         onPropGet(PROP_ID_iconUrl);
         return _iconUrl;
    }

    /**
     * 图标URL: icon_url
     */
    public final void setIconUrl(java.lang.String value){
        if(onPropSet(PROP_ID_iconUrl,value)){
            this._iconUrl = value;
            internalClearRefs(PROP_ID_iconUrl);
            
        }
    }
    
    /**
     * 描述: desc
     */
    public final java.lang.String getDesc(){
         onPropGet(PROP_ID_desc);
         return _desc;
    }

    /**
     * 描述: desc
     */
    public final void setDesc(java.lang.String value){
        if(onPropSet(PROP_ID_desc,value)){
            this._desc = value;
            internalClearRefs(PROP_ID_desc);
            
        }
    }
    
    /**
     * 高级设置: advanced_setting
     */
    public final java.lang.String getAdvancedSetting(){
         onPropGet(PROP_ID_advancedSetting);
         return _advancedSetting;
    }

    /**
     * 高级设置: advanced_setting
     */
    public final void setAdvancedSetting(java.lang.String value){
        if(onPropSet(PROP_ID_advancedSetting,value)){
            this._advancedSetting = value;
            internalClearRefs(PROP_ID_advancedSetting);
            
        }
    }
    
    /**
     * 启用二次确认: enable_confirm
     */
    public final java.lang.Boolean getEnableConfirm(){
         onPropGet(PROP_ID_enableConfirm);
         return _enableConfirm;
    }

    /**
     * 启用二次确认: enable_confirm
     */
    public final void setEnableConfirm(java.lang.Boolean value){
        if(onPropSet(PROP_ID_enableConfirm,value)){
            this._enableConfirm = value;
            internalClearRefs(PROP_ID_enableConfirm);
            
        }
    }
    
    /**
     * 校验密码: verify_pwd
     */
    public final java.lang.Boolean getVerifyPwd(){
         onPropGet(PROP_ID_verifyPwd);
         return _verifyPwd;
    }

    /**
     * 校验密码: verify_pwd
     */
    public final void setVerifyPwd(java.lang.Boolean value){
        if(onPropSet(PROP_ID_verifyPwd,value)){
            this._verifyPwd = value;
            internalClearRefs(PROP_ID_verifyPwd);
            
        }
    }
    
    /**
     * 是否多条数据源: is_batch
     */
    public final java.lang.Boolean getIsBatch(){
         onPropGet(PROP_ID_isBatch);
         return _isBatch;
    }

    /**
     * 是否多条数据源: is_batch
     */
    public final void setIsBatch(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isBatch,value)){
            this._isBatch = value;
            internalClearRefs(PROP_ID_isBatch);
            
        }
    }
    
    /**
     * 启用按钮类型: show_type
     */
    public final java.lang.Integer getShowType(){
         onPropGet(PROP_ID_showType);
         return _showType;
    }

    /**
     * 启用按钮类型: show_type
     */
    public final void setShowType(java.lang.Integer value){
        if(onPropSet(PROP_ID_showType,value)){
            this._showType = value;
            internalClearRefs(PROP_ID_showType);
            
        }
    }
    
    /**
     * 添加关联控件: add_relation_control
     */
    public final java.lang.String getAddRelationControl(){
         onPropGet(PROP_ID_addRelationControl);
         return _addRelationControl;
    }

    /**
     * 添加关联控件: add_relation_control
     */
    public final void setAddRelationControl(java.lang.String value){
        if(onPropSet(PROP_ID_addRelationControl,value)){
            this._addRelationControl = value;
            internalClearRefs(PROP_ID_addRelationControl);
            
        }
    }
    
    /**
     * 属性: edit_attrs
     */
    public final java.lang.String getEditAttrs(){
         onPropGet(PROP_ID_editAttrs);
         return _editAttrs;
    }

    /**
     * 属性: edit_attrs
     */
    public final void setEditAttrs(java.lang.String value){
        if(onPropSet(PROP_ID_editAttrs,value)){
            this._editAttrs = value;
            internalClearRefs(PROP_ID_editAttrs);
            
        }
    }
    
    /**
     * 禁用: disabled
     */
    public final java.lang.Boolean getDisabled(){
         onPropGet(PROP_ID_disabled);
         return _disabled;
    }

    /**
     * 禁用: disabled
     */
    public final void setDisabled(java.lang.Boolean value){
        if(onPropSet(PROP_ID_disabled,value)){
            this._disabled = value;
            internalClearRefs(PROP_ID_disabled);
            
        }
    }
    
    /**
     * 状态: status
     */
    public final java.lang.Integer getStatus(){
         onPropGet(PROP_ID_status);
         return _status;
    }

    /**
     * 状态: status
     */
    public final void setStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_status,value)){
            this._status = value;
            internalClearRefs(PROP_ID_status);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 所属工作表
     */
    public final com.mlc.application.dao.entity.MlcAppWorksheet getWorksheet(){
       return (com.mlc.application.dao.entity.MlcAppWorksheet)internalGetRefEntity(PROP_NAME_worksheet);
    }

    public final void setWorksheet(com.mlc.application.dao.entity.MlcAppWorksheet refEntity){
   
           if(refEntity == null){
           
                   this.setWorksheetId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_worksheet, refEntity,()->{
           
                           this.setWorksheetId(refEntity.getWorksheetId());
                       
           });
           }
       
    }
       
   private io.nop.orm.component.JsonOrmComponent _advancedSettingComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_advancedSettingComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_advancedSettingComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_advancedSetting);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getAdvancedSettingComponent(){
      if(_advancedSettingComponent == null){
          _advancedSettingComponent = new io.nop.orm.component.JsonOrmComponent();
          _advancedSettingComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_advancedSettingComponent);
      }
      return _advancedSettingComponent;
   }

}
// resume CPD analysis - CPD-ON
