package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppRoleResource;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  应用角色资源表: mlc_app_role_resource
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppRoleResource extends DynamicOrmEntity{
    
    /* 主键ID: sid VARCHAR */
    public static final String PROP_NAME_sid = "sid";
    public static final int PROP_ID_sid = 1;
    
    /* 角色ID: role_id VARCHAR */
    public static final String PROP_NAME_roleId = "roleId";
    public static final int PROP_ID_roleId = 2;
    
    /* 资源ID: resource_id VARCHAR */
    public static final String PROP_NAME_resourceId = "resourceId";
    public static final int PROP_ID_resourceId = 3;
    
    /* 权限操作类型: operation VARCHAR */
    public static final String PROP_NAME_operation = "operation";
    public static final int PROP_ID_operation = 4;
    
    /* 变更的权限标签: delta_permissions VARCHAR */
    public static final String PROP_NAME_deltaPermissions = "deltaPermissions";
    public static final int PROP_ID_deltaPermissions = 5;
    
    /* 冲突时优先级: priority INTEGER */
    public static final String PROP_NAME_priority = "priority";
    public static final int PROP_ID_priority = 6;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 7;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 8;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 9;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 10;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 11;
    

    private static int _PROP_ID_BOUND = 12;

    
    /* relation: 角色 */
    public static final String PROP_NAME_role = "role";
    
    /* relation: 资源 */
    public static final String PROP_NAME_resource = "resource";
    
    /* component:  */
    public static final String PROP_NAME_deltaPermissionsComponent = "deltaPermissionsComponent";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_sid);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_sid};

    private static final String[] PROP_ID_TO_NAME = new String[12];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_sid] = PROP_NAME_sid;
          PROP_NAME_TO_ID.put(PROP_NAME_sid, PROP_ID_sid);
      
          PROP_ID_TO_NAME[PROP_ID_roleId] = PROP_NAME_roleId;
          PROP_NAME_TO_ID.put(PROP_NAME_roleId, PROP_ID_roleId);
      
          PROP_ID_TO_NAME[PROP_ID_resourceId] = PROP_NAME_resourceId;
          PROP_NAME_TO_ID.put(PROP_NAME_resourceId, PROP_ID_resourceId);
      
          PROP_ID_TO_NAME[PROP_ID_operation] = PROP_NAME_operation;
          PROP_NAME_TO_ID.put(PROP_NAME_operation, PROP_ID_operation);
      
          PROP_ID_TO_NAME[PROP_ID_deltaPermissions] = PROP_NAME_deltaPermissions;
          PROP_NAME_TO_ID.put(PROP_NAME_deltaPermissions, PROP_ID_deltaPermissions);
      
          PROP_ID_TO_NAME[PROP_ID_priority] = PROP_NAME_priority;
          PROP_NAME_TO_ID.put(PROP_NAME_priority, PROP_ID_priority);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 主键ID: sid */
    private java.lang.String _sid;
    
    /* 角色ID: role_id */
    private java.lang.String _roleId;
    
    /* 资源ID: resource_id */
    private java.lang.String _resourceId;
    
    /* 权限操作类型: operation */
    private java.lang.String _operation;
    
    /* 变更的权限标签: delta_permissions */
    private java.lang.String _deltaPermissions;
    
    /* 冲突时优先级: priority */
    private java.lang.Integer _priority;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppRoleResource(){
        // for debug
    }

    protected MlcAppRoleResource newInstance(){
        MlcAppRoleResource entity = new MlcAppRoleResource();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppRoleResource cloneInstance() {
        MlcAppRoleResource entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppRoleResource";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_sid);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_sid;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_sid:
               return getSid();
        
            case PROP_ID_roleId:
               return getRoleId();
        
            case PROP_ID_resourceId:
               return getResourceId();
        
            case PROP_ID_operation:
               return getOperation();
        
            case PROP_ID_deltaPermissions:
               return getDeltaPermissions();
        
            case PROP_ID_priority:
               return getPriority();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_sid:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_sid));
               }
               setSid(typedValue);
               break;
            }
        
            case PROP_ID_roleId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_roleId));
               }
               setRoleId(typedValue);
               break;
            }
        
            case PROP_ID_resourceId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_resourceId));
               }
               setResourceId(typedValue);
               break;
            }
        
            case PROP_ID_operation:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_operation));
               }
               setOperation(typedValue);
               break;
            }
        
            case PROP_ID_deltaPermissions:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_deltaPermissions));
               }
               setDeltaPermissions(typedValue);
               break;
            }
        
            case PROP_ID_priority:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_priority));
               }
               setPriority(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_sid:{
               onInitProp(propId);
               this._sid = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_roleId:{
               onInitProp(propId);
               this._roleId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_resourceId:{
               onInitProp(propId);
               this._resourceId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_operation:{
               onInitProp(propId);
               this._operation = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_deltaPermissions:{
               onInitProp(propId);
               this._deltaPermissions = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_priority:{
               onInitProp(propId);
               this._priority = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 主键ID: sid
     */
    public final java.lang.String getSid(){
         onPropGet(PROP_ID_sid);
         return _sid;
    }

    /**
     * 主键ID: sid
     */
    public final void setSid(java.lang.String value){
        if(onPropSet(PROP_ID_sid,value)){
            this._sid = value;
            internalClearRefs(PROP_ID_sid);
            orm_id();
        }
    }
    
    /**
     * 角色ID: role_id
     */
    public final java.lang.String getRoleId(){
         onPropGet(PROP_ID_roleId);
         return _roleId;
    }

    /**
     * 角色ID: role_id
     */
    public final void setRoleId(java.lang.String value){
        if(onPropSet(PROP_ID_roleId,value)){
            this._roleId = value;
            internalClearRefs(PROP_ID_roleId);
            
        }
    }
    
    /**
     * 资源ID: resource_id
     */
    public final java.lang.String getResourceId(){
         onPropGet(PROP_ID_resourceId);
         return _resourceId;
    }

    /**
     * 资源ID: resource_id
     */
    public final void setResourceId(java.lang.String value){
        if(onPropSet(PROP_ID_resourceId,value)){
            this._resourceId = value;
            internalClearRefs(PROP_ID_resourceId);
            
        }
    }
    
    /**
     * 权限操作类型: operation
     */
    public final java.lang.String getOperation(){
         onPropGet(PROP_ID_operation);
         return _operation;
    }

    /**
     * 权限操作类型: operation
     */
    public final void setOperation(java.lang.String value){
        if(onPropSet(PROP_ID_operation,value)){
            this._operation = value;
            internalClearRefs(PROP_ID_operation);
            
        }
    }
    
    /**
     * 变更的权限标签: delta_permissions
     */
    public final java.lang.String getDeltaPermissions(){
         onPropGet(PROP_ID_deltaPermissions);
         return _deltaPermissions;
    }

    /**
     * 变更的权限标签: delta_permissions
     */
    public final void setDeltaPermissions(java.lang.String value){
        if(onPropSet(PROP_ID_deltaPermissions,value)){
            this._deltaPermissions = value;
            internalClearRefs(PROP_ID_deltaPermissions);
            
        }
    }
    
    /**
     * 冲突时优先级: priority
     */
    public final java.lang.Integer getPriority(){
         onPropGet(PROP_ID_priority);
         return _priority;
    }

    /**
     * 冲突时优先级: priority
     */
    public final void setPriority(java.lang.Integer value){
        if(onPropSet(PROP_ID_priority,value)){
            this._priority = value;
            internalClearRefs(PROP_ID_priority);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 角色
     */
    public final com.mlc.application.dao.entity.MlcAppRole getRole(){
       return (com.mlc.application.dao.entity.MlcAppRole)internalGetRefEntity(PROP_NAME_role);
    }

    public final void setRole(com.mlc.application.dao.entity.MlcAppRole refEntity){
   
           if(refEntity == null){
           
                   this.setRoleId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_role, refEntity,()->{
           
                           this.setRoleId(refEntity.getRoleId());
                       
           });
           }
       
    }
       
    /**
     * 资源
     */
    public final com.mlc.application.dao.entity.MlcAppResource getResource(){
       return (com.mlc.application.dao.entity.MlcAppResource)internalGetRefEntity(PROP_NAME_resource);
    }

    public final void setResource(com.mlc.application.dao.entity.MlcAppResource refEntity){
   
           if(refEntity == null){
           
                   this.setResourceId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_resource, refEntity,()->{
           
                           this.setResourceId(refEntity.getResourceId());
                       
           });
           }
       
    }
       
   private io.nop.orm.component.JsonOrmComponent _deltaPermissionsComponent;

   private static Map<String,Integer> COMPONENT_PROP_ID_MAP_deltaPermissionsComponent = new HashMap<>();
   static{
      
         COMPONENT_PROP_ID_MAP_deltaPermissionsComponent.put(io.nop.orm.component.JsonOrmComponent.PROP_NAME__jsonText,PROP_ID_deltaPermissions);
      
   }

   public final io.nop.orm.component.JsonOrmComponent getDeltaPermissionsComponent(){
      if(_deltaPermissionsComponent == null){
          _deltaPermissionsComponent = new io.nop.orm.component.JsonOrmComponent();
          _deltaPermissionsComponent.bindToEntity(this, COMPONENT_PROP_ID_MAP_deltaPermissionsComponent);
      }
      return _deltaPermissionsComponent;
   }

}
// resume CPD analysis - CPD-ON
