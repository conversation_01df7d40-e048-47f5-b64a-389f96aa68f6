package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppMember;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  应用成员: mlc_app_member
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppMember extends DynamicOrmEntity{
    
    /* 成员ID: member_id VARCHAR */
    public static final String PROP_NAME_memberId = "memberId";
    public static final int PROP_ID_memberId = 1;
    
    /* 应用 ID: app_id VARCHAR */
    public static final String PROP_NAME_appId = "appId";
    public static final int PROP_ID_appId = 2;
    
    /* 成员关联组织用户: member_org_user_id VARCHAR */
    public static final String PROP_NAME_memberOrgUserId = "memberOrgUserId";
    public static final int PROP_ID_memberOrgUserId = 3;
    
    /* 角色类型: member_type INTEGER */
    public static final String PROP_NAME_memberType = "memberType";
    public static final int PROP_ID_memberType = 4;
    
    /* 状态: status INTEGER */
    public static final String PROP_NAME_status = "status";
    public static final int PROP_ID_status = 5;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 6;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 7;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 8;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 9;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 10;
    

    private static int _PROP_ID_BOUND = 11;

    
    /* relation: 应用信息 */
    public static final String PROP_NAME_appInfo = "appInfo";
    
    /* relation: 应用角色映射 */
    public static final String PROP_NAME_appRoleMappings = "appRoleMappings";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_memberId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_memberId};

    private static final String[] PROP_ID_TO_NAME = new String[11];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_memberId] = PROP_NAME_memberId;
          PROP_NAME_TO_ID.put(PROP_NAME_memberId, PROP_ID_memberId);
      
          PROP_ID_TO_NAME[PROP_ID_appId] = PROP_NAME_appId;
          PROP_NAME_TO_ID.put(PROP_NAME_appId, PROP_ID_appId);
      
          PROP_ID_TO_NAME[PROP_ID_memberOrgUserId] = PROP_NAME_memberOrgUserId;
          PROP_NAME_TO_ID.put(PROP_NAME_memberOrgUserId, PROP_ID_memberOrgUserId);
      
          PROP_ID_TO_NAME[PROP_ID_memberType] = PROP_NAME_memberType;
          PROP_NAME_TO_ID.put(PROP_NAME_memberType, PROP_ID_memberType);
      
          PROP_ID_TO_NAME[PROP_ID_status] = PROP_NAME_status;
          PROP_NAME_TO_ID.put(PROP_NAME_status, PROP_ID_status);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 成员ID: member_id */
    private java.lang.String _memberId;
    
    /* 应用 ID: app_id */
    private java.lang.String _appId;
    
    /* 成员关联组织用户: member_org_user_id */
    private java.lang.String _memberOrgUserId;
    
    /* 角色类型: member_type */
    private java.lang.Integer _memberType;
    
    /* 状态: status */
    private java.lang.Integer _status;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppMember(){
        // for debug
    }

    protected MlcAppMember newInstance(){
        MlcAppMember entity = new MlcAppMember();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppMember cloneInstance() {
        MlcAppMember entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppMember";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_memberId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_memberId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_memberId:
               return getMemberId();
        
            case PROP_ID_appId:
               return getAppId();
        
            case PROP_ID_memberOrgUserId:
               return getMemberOrgUserId();
        
            case PROP_ID_memberType:
               return getMemberType();
        
            case PROP_ID_status:
               return getStatus();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_memberId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_memberId));
               }
               setMemberId(typedValue);
               break;
            }
        
            case PROP_ID_appId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_appId));
               }
               setAppId(typedValue);
               break;
            }
        
            case PROP_ID_memberOrgUserId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_memberOrgUserId));
               }
               setMemberOrgUserId(typedValue);
               break;
            }
        
            case PROP_ID_memberType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_memberType));
               }
               setMemberType(typedValue);
               break;
            }
        
            case PROP_ID_status:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_status));
               }
               setStatus(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_memberId:{
               onInitProp(propId);
               this._memberId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_appId:{
               onInitProp(propId);
               this._appId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_memberOrgUserId:{
               onInitProp(propId);
               this._memberOrgUserId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_memberType:{
               onInitProp(propId);
               this._memberType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_status:{
               onInitProp(propId);
               this._status = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 成员ID: member_id
     */
    public final java.lang.String getMemberId(){
         onPropGet(PROP_ID_memberId);
         return _memberId;
    }

    /**
     * 成员ID: member_id
     */
    public final void setMemberId(java.lang.String value){
        if(onPropSet(PROP_ID_memberId,value)){
            this._memberId = value;
            internalClearRefs(PROP_ID_memberId);
            orm_id();
        }
    }
    
    /**
     * 应用 ID: app_id
     */
    public final java.lang.String getAppId(){
         onPropGet(PROP_ID_appId);
         return _appId;
    }

    /**
     * 应用 ID: app_id
     */
    public final void setAppId(java.lang.String value){
        if(onPropSet(PROP_ID_appId,value)){
            this._appId = value;
            internalClearRefs(PROP_ID_appId);
            
        }
    }
    
    /**
     * 成员关联组织用户: member_org_user_id
     */
    public final java.lang.String getMemberOrgUserId(){
         onPropGet(PROP_ID_memberOrgUserId);
         return _memberOrgUserId;
    }

    /**
     * 成员关联组织用户: member_org_user_id
     */
    public final void setMemberOrgUserId(java.lang.String value){
        if(onPropSet(PROP_ID_memberOrgUserId,value)){
            this._memberOrgUserId = value;
            internalClearRefs(PROP_ID_memberOrgUserId);
            
        }
    }
    
    /**
     * 角色类型: member_type
     */
    public final java.lang.Integer getMemberType(){
         onPropGet(PROP_ID_memberType);
         return _memberType;
    }

    /**
     * 角色类型: member_type
     */
    public final void setMemberType(java.lang.Integer value){
        if(onPropSet(PROP_ID_memberType,value)){
            this._memberType = value;
            internalClearRefs(PROP_ID_memberType);
            
        }
    }
    
    /**
     * 状态: status
     */
    public final java.lang.Integer getStatus(){
         onPropGet(PROP_ID_status);
         return _status;
    }

    /**
     * 状态: status
     */
    public final void setStatus(java.lang.Integer value){
        if(onPropSet(PROP_ID_status,value)){
            this._status = value;
            internalClearRefs(PROP_ID_status);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 应用信息
     */
    public final com.mlc.application.dao.entity.MlcAppInfo getAppInfo(){
       return (com.mlc.application.dao.entity.MlcAppInfo)internalGetRefEntity(PROP_NAME_appInfo);
    }

    public final void setAppInfo(com.mlc.application.dao.entity.MlcAppInfo refEntity){
   
           if(refEntity == null){
           
                   this.setAppId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appInfo, refEntity,()->{
           
                           this.setAppId(refEntity.getAppId());
                       
           });
           }
       
    }
       
    private final OrmEntitySet<com.mlc.application.dao.entity.MlcAppMemberRole> _appRoleMappings = new OrmEntitySet<>(this, PROP_NAME_appRoleMappings,
        com.mlc.application.dao.entity.MlcAppMemberRole.PROP_NAME_appMember, null,com.mlc.application.dao.entity.MlcAppMemberRole.class);

    /**
     * 应用角色映射。 refPropName: appMember, keyProp: {rel.keyProp}
     */
    public final IOrmEntitySet<com.mlc.application.dao.entity.MlcAppMemberRole> getAppRoleMappings(){
       return _appRoleMappings;
    }
       
        public final List<com.mlc.application.dao.entity.MlcAppRole> getRelatedAppRoleList(){
            return (List<com.mlc.application.dao.entity.MlcAppRole>)io.nop.orm.support.OrmEntityHelper.getRefProps(getAppRoleMappings(),"appRole");
        }

        public final List<String> getRelatedAppRoleList_ids(){
            return io.nop.orm.support.OrmEntityHelper.getRefIds(getAppRoleMappings(),"appRole");
        }

        public void setRelatedAppRoleList_ids(List<String> value){
            io.nop.orm.support.OrmEntityHelper.setRefIds(getAppRoleMappings(),"appRole",value);
        }
    

    public final String getRelatedAppRoleList_label(){
        return io.nop.orm.support.OrmEntityHelper.getLabelForRefProps(getAppRoleMappings(),"appRole");
    }


}
// resume CPD analysis - CPD-ON
