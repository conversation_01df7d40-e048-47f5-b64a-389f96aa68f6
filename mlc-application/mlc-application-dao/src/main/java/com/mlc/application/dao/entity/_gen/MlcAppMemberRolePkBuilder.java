package com.mlc.application.dao.entity._gen;

import io.nop.orm.support.OrmCompositePk;
import com.mlc.application.dao.entity.MlcAppMemberRole;

/**
 * 用于生成复合主键的帮助类
 */
@SuppressWarnings({"PMD.UnnecessaryFullyQualifiedName"})
public class MlcAppMemberRolePkBuilder{
    private Object[] values = new Object[2];

   
    public MlcAppMemberRolePkBuilder setMemberId(java.lang.String value){
        this.values[0] = value;
        return this;
    }
   
    public MlcAppMemberRolePkBuilder setRoleId(java.lang.String value){
        this.values[1] = value;
        return this;
    }
   

    public OrmCompositePk build(){
        return OrmCompositePk.buildNotNull(MlcAppMemberRole.PK_PROP_NAMES,values);
    }
}
