package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppWorksheetSwitch;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  工作表开关: mlc_app_worksheet_switch
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppWorksheetSwitch extends DynamicOrmEntity{
    
    /* 按钮ID: switch_id VARCHAR */
    public static final String PROP_NAME_switchId = "switchId";
    public static final int PROP_ID_switchId = 1;
    
    /* 工作表ID: worksheet_id VARCHAR */
    public static final String PROP_NAME_worksheetId = "worksheetId";
    public static final int PROP_ID_worksheetId = 2;
    
    /* 开关类型: type INTEGER */
    public static final String PROP_NAME_type = "type";
    public static final int PROP_ID_type = 3;
    
    /* 角色类型: role_type INTEGER */
    public static final String PROP_NAME_roleType = "roleType";
    public static final int PROP_ID_roleType = 4;
    
    /* 状态: state BOOLEAN */
    public static final String PROP_NAME_state = "state";
    public static final int PROP_ID_state = 5;
    
    /* 视图: view VARCHAR */
    public static final String PROP_NAME_view = "view";
    public static final int PROP_ID_view = 6;
    
    /* 视图: view_ids VARCHAR */
    public static final String PROP_NAME_viewIds = "viewIds";
    public static final int PROP_ID_viewIds = 7;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 8;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 9;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 10;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 11;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 12;
    

    private static int _PROP_ID_BOUND = 13;

    
    /* relation: 所属工作表 */
    public static final String PROP_NAME_worksheet = "worksheet";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_switchId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_switchId};

    private static final String[] PROP_ID_TO_NAME = new String[13];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_switchId] = PROP_NAME_switchId;
          PROP_NAME_TO_ID.put(PROP_NAME_switchId, PROP_ID_switchId);
      
          PROP_ID_TO_NAME[PROP_ID_worksheetId] = PROP_NAME_worksheetId;
          PROP_NAME_TO_ID.put(PROP_NAME_worksheetId, PROP_ID_worksheetId);
      
          PROP_ID_TO_NAME[PROP_ID_type] = PROP_NAME_type;
          PROP_NAME_TO_ID.put(PROP_NAME_type, PROP_ID_type);
      
          PROP_ID_TO_NAME[PROP_ID_roleType] = PROP_NAME_roleType;
          PROP_NAME_TO_ID.put(PROP_NAME_roleType, PROP_ID_roleType);
      
          PROP_ID_TO_NAME[PROP_ID_state] = PROP_NAME_state;
          PROP_NAME_TO_ID.put(PROP_NAME_state, PROP_ID_state);
      
          PROP_ID_TO_NAME[PROP_ID_view] = PROP_NAME_view;
          PROP_NAME_TO_ID.put(PROP_NAME_view, PROP_ID_view);
      
          PROP_ID_TO_NAME[PROP_ID_viewIds] = PROP_NAME_viewIds;
          PROP_NAME_TO_ID.put(PROP_NAME_viewIds, PROP_ID_viewIds);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 按钮ID: switch_id */
    private java.lang.String _switchId;
    
    /* 工作表ID: worksheet_id */
    private java.lang.String _worksheetId;
    
    /* 开关类型: type */
    private java.lang.Integer _type;
    
    /* 角色类型: role_type */
    private java.lang.Integer _roleType;
    
    /* 状态: state */
    private java.lang.Boolean _state;
    
    /* 视图: view */
    private java.lang.String _view;
    
    /* 视图: view_ids */
    private java.lang.String _viewIds;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppWorksheetSwitch(){
        // for debug
    }

    protected MlcAppWorksheetSwitch newInstance(){
        MlcAppWorksheetSwitch entity = new MlcAppWorksheetSwitch();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppWorksheetSwitch cloneInstance() {
        MlcAppWorksheetSwitch entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppWorksheetSwitch";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildSimpleId(PROP_ID_switchId);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_switchId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_switchId:
               return getSwitchId();
        
            case PROP_ID_worksheetId:
               return getWorksheetId();
        
            case PROP_ID_type:
               return getType();
        
            case PROP_ID_roleType:
               return getRoleType();
        
            case PROP_ID_state:
               return getState();
        
            case PROP_ID_view:
               return getView();
        
            case PROP_ID_viewIds:
               return getViewIds();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_switchId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_switchId));
               }
               setSwitchId(typedValue);
               break;
            }
        
            case PROP_ID_worksheetId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_worksheetId));
               }
               setWorksheetId(typedValue);
               break;
            }
        
            case PROP_ID_type:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_type));
               }
               setType(typedValue);
               break;
            }
        
            case PROP_ID_roleType:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_roleType));
               }
               setRoleType(typedValue);
               break;
            }
        
            case PROP_ID_state:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_state));
               }
               setState(typedValue);
               break;
            }
        
            case PROP_ID_view:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_view));
               }
               setView(typedValue);
               break;
            }
        
            case PROP_ID_viewIds:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_viewIds));
               }
               setViewIds(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_switchId:{
               onInitProp(propId);
               this._switchId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_worksheetId:{
               onInitProp(propId);
               this._worksheetId = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_type:{
               onInitProp(propId);
               this._type = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_roleType:{
               onInitProp(propId);
               this._roleType = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_state:{
               onInitProp(propId);
               this._state = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_view:{
               onInitProp(propId);
               this._view = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_viewIds:{
               onInitProp(propId);
               this._viewIds = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 按钮ID: switch_id
     */
    public final java.lang.String getSwitchId(){
         onPropGet(PROP_ID_switchId);
         return _switchId;
    }

    /**
     * 按钮ID: switch_id
     */
    public final void setSwitchId(java.lang.String value){
        if(onPropSet(PROP_ID_switchId,value)){
            this._switchId = value;
            internalClearRefs(PROP_ID_switchId);
            orm_id();
        }
    }
    
    /**
     * 工作表ID: worksheet_id
     */
    public final java.lang.String getWorksheetId(){
         onPropGet(PROP_ID_worksheetId);
         return _worksheetId;
    }

    /**
     * 工作表ID: worksheet_id
     */
    public final void setWorksheetId(java.lang.String value){
        if(onPropSet(PROP_ID_worksheetId,value)){
            this._worksheetId = value;
            internalClearRefs(PROP_ID_worksheetId);
            
        }
    }
    
    /**
     * 开关类型: type
     */
    public final java.lang.Integer getType(){
         onPropGet(PROP_ID_type);
         return _type;
    }

    /**
     * 开关类型: type
     */
    public final void setType(java.lang.Integer value){
        if(onPropSet(PROP_ID_type,value)){
            this._type = value;
            internalClearRefs(PROP_ID_type);
            
        }
    }
    
    /**
     * 角色类型: role_type
     */
    public final java.lang.Integer getRoleType(){
         onPropGet(PROP_ID_roleType);
         return _roleType;
    }

    /**
     * 角色类型: role_type
     */
    public final void setRoleType(java.lang.Integer value){
        if(onPropSet(PROP_ID_roleType,value)){
            this._roleType = value;
            internalClearRefs(PROP_ID_roleType);
            
        }
    }
    
    /**
     * 状态: state
     */
    public final java.lang.Boolean getState(){
         onPropGet(PROP_ID_state);
         return _state;
    }

    /**
     * 状态: state
     */
    public final void setState(java.lang.Boolean value){
        if(onPropSet(PROP_ID_state,value)){
            this._state = value;
            internalClearRefs(PROP_ID_state);
            
        }
    }
    
    /**
     * 视图: view
     */
    public final java.lang.String getView(){
         onPropGet(PROP_ID_view);
         return _view;
    }

    /**
     * 视图: view
     */
    public final void setView(java.lang.String value){
        if(onPropSet(PROP_ID_view,value)){
            this._view = value;
            internalClearRefs(PROP_ID_view);
            
        }
    }
    
    /**
     * 视图: view_ids
     */
    public final java.lang.String getViewIds(){
         onPropGet(PROP_ID_viewIds);
         return _viewIds;
    }

    /**
     * 视图: view_ids
     */
    public final void setViewIds(java.lang.String value){
        if(onPropSet(PROP_ID_viewIds,value)){
            this._viewIds = value;
            internalClearRefs(PROP_ID_viewIds);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 所属工作表
     */
    public final com.mlc.application.dao.entity.MlcAppWorksheet getWorksheet(){
       return (com.mlc.application.dao.entity.MlcAppWorksheet)internalGetRefEntity(PROP_NAME_worksheet);
    }

    public final void setWorksheet(com.mlc.application.dao.entity.MlcAppWorksheet refEntity){
   
           if(refEntity == null){
           
                   this.setWorksheetId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_worksheet, refEntity,()->{
           
                           this.setWorksheetId(refEntity.getWorksheetId());
                       
           });
           }
       
    }
       
}
// resume CPD analysis - CPD-ON
