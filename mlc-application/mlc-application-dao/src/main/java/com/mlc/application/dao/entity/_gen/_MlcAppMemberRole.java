package com.mlc.application.dao.entity._gen;

import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.orm.support.OrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.orm.IOrmEntitySet; //NOPMD - suppressed UnusedImports - Auto Gen Code
import io.nop.api.core.convert.ConvertHelper;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;

import com.mlc.application.dao.entity.MlcAppMemberRole;

// tell cpd to start ignoring code - CPD-OFF
/**
 *  成员角色中间表: mlc_app_member_role
 */
@SuppressWarnings({"PMD.UselessOverridingMethod","PMD.UnusedLocalVariable","java:S3008","java:S1602","java:S1128","java:S1161",
        "PMD.UnnecessaryFullyQualifiedName","PMD.EmptyControlStatement","java:S116","java:S115","java:S101","java:S3776"})
public class _MlcAppMemberRole extends DynamicOrmEntity{
    
    /* 成员ID: member_id VARCHAR */
    public static final String PROP_NAME_memberId = "memberId";
    public static final int PROP_ID_memberId = 1;
    
    /* 角色ID: role_id VARCHAR */
    public static final String PROP_NAME_roleId = "roleId";
    public static final int PROP_ID_roleId = 2;
    
    /* 是否拥有者: is_owner BOOLEAN */
    public static final String PROP_NAME_isOwner = "isOwner";
    public static final int PROP_ID_isOwner = 3;
    
    /* 是否管理员: is_manager BOOLEAN */
    public static final String PROP_NAME_isManager = "isManager";
    public static final int PROP_ID_isManager = 4;
    
    /* 是否角色负责人: is_role_charger BOOLEAN */
    public static final String PROP_NAME_isRoleCharger = "isRoleCharger";
    public static final int PROP_ID_isRoleCharger = 5;
    
    /* 数据版本: version INTEGER */
    public static final String PROP_NAME_version = "version";
    public static final int PROP_ID_version = 6;
    
    /* 创建人: create_by VARCHAR */
    public static final String PROP_NAME_createBy = "createBy";
    public static final int PROP_ID_createBy = 7;
    
    /* 创建时间: created_at TIMESTAMP */
    public static final String PROP_NAME_createdAt = "createdAt";
    public static final int PROP_ID_createdAt = 8;
    
    /* 修改人: update_by VARCHAR */
    public static final String PROP_NAME_updateBy = "updateBy";
    public static final int PROP_ID_updateBy = 9;
    
    /* 修改时间: updated_at TIMESTAMP */
    public static final String PROP_NAME_updatedAt = "updatedAt";
    public static final int PROP_ID_updatedAt = 10;
    

    private static int _PROP_ID_BOUND = 11;

    
    /* relation: 应用成员 */
    public static final String PROP_NAME_appMember = "appMember";
    
    /* relation: 应用角色 */
    public static final String PROP_NAME_appRole = "appRole";
    

    protected static final List<String> PK_PROP_NAMES = Arrays.asList(PROP_NAME_memberId,PROP_NAME_roleId);
    protected static final int[] PK_PROP_IDS = new int[]{PROP_ID_memberId,PROP_ID_roleId};

    private static final String[] PROP_ID_TO_NAME = new String[11];
    private static final Map<String,Integer> PROP_NAME_TO_ID = new HashMap<>();
    static{
      
          PROP_ID_TO_NAME[PROP_ID_memberId] = PROP_NAME_memberId;
          PROP_NAME_TO_ID.put(PROP_NAME_memberId, PROP_ID_memberId);
      
          PROP_ID_TO_NAME[PROP_ID_roleId] = PROP_NAME_roleId;
          PROP_NAME_TO_ID.put(PROP_NAME_roleId, PROP_ID_roleId);
      
          PROP_ID_TO_NAME[PROP_ID_isOwner] = PROP_NAME_isOwner;
          PROP_NAME_TO_ID.put(PROP_NAME_isOwner, PROP_ID_isOwner);
      
          PROP_ID_TO_NAME[PROP_ID_isManager] = PROP_NAME_isManager;
          PROP_NAME_TO_ID.put(PROP_NAME_isManager, PROP_ID_isManager);
      
          PROP_ID_TO_NAME[PROP_ID_isRoleCharger] = PROP_NAME_isRoleCharger;
          PROP_NAME_TO_ID.put(PROP_NAME_isRoleCharger, PROP_ID_isRoleCharger);
      
          PROP_ID_TO_NAME[PROP_ID_version] = PROP_NAME_version;
          PROP_NAME_TO_ID.put(PROP_NAME_version, PROP_ID_version);
      
          PROP_ID_TO_NAME[PROP_ID_createBy] = PROP_NAME_createBy;
          PROP_NAME_TO_ID.put(PROP_NAME_createBy, PROP_ID_createBy);
      
          PROP_ID_TO_NAME[PROP_ID_createdAt] = PROP_NAME_createdAt;
          PROP_NAME_TO_ID.put(PROP_NAME_createdAt, PROP_ID_createdAt);
      
          PROP_ID_TO_NAME[PROP_ID_updateBy] = PROP_NAME_updateBy;
          PROP_NAME_TO_ID.put(PROP_NAME_updateBy, PROP_ID_updateBy);
      
          PROP_ID_TO_NAME[PROP_ID_updatedAt] = PROP_NAME_updatedAt;
          PROP_NAME_TO_ID.put(PROP_NAME_updatedAt, PROP_ID_updatedAt);
      
    }

    
    /* 成员ID: member_id */
    private java.lang.String _memberId;
    
    /* 角色ID: role_id */
    private java.lang.String _roleId;
    
    /* 是否拥有者: is_owner */
    private java.lang.Boolean _isOwner;
    
    /* 是否管理员: is_manager */
    private java.lang.Boolean _isManager;
    
    /* 是否角色负责人: is_role_charger */
    private java.lang.Boolean _isRoleCharger;
    
    /* 数据版本: version */
    private java.lang.Integer _version;
    
    /* 创建人: create_by */
    private java.lang.String _createBy;
    
    /* 创建时间: created_at */
    private java.sql.Timestamp _createdAt;
    
    /* 修改人: update_by */
    private java.lang.String _updateBy;
    
    /* 修改时间: updated_at */
    private java.sql.Timestamp _updatedAt;
    

    public _MlcAppMemberRole(){
        // for debug
    }

    protected MlcAppMemberRole newInstance(){
        MlcAppMemberRole entity = new MlcAppMemberRole();
        entity.orm_attach(orm_enhancer());
        entity.orm_entityModel(orm_entityModel());
        return entity;
    }

    @Override
    public MlcAppMemberRole cloneInstance() {
        MlcAppMemberRole entity = newInstance();
        orm_forEachInitedProp((value, propId) -> {
            entity.orm_propValue(propId,value);
        });
        return entity;
    }

    @Override
    public String orm_entityName() {
      // 如果存在实体模型对象，则以模型对象上的设置为准
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getName();
      return "com.mlc.application.dao.entity.MlcAppMemberRole";
    }

    @Override
    public int orm_propIdBound(){
      IEntityModel entityModel = orm_entityModel();
      if(entityModel != null)
          return entityModel.getPropIdBound();
      return _PROP_ID_BOUND;
    }

    @Override
    public Object orm_id() {
    
        return buildCompositeId(PK_PROP_NAMES,PK_PROP_IDS);
     
    }

    @Override
    public boolean orm_isPrimary(int propId) {
        
            return propId == PROP_ID_memberId || propId == PROP_ID_roleId;
          
    }

    @Override
    public String orm_propName(int propId) {
        if(propId >= PROP_ID_TO_NAME.length)
            return super.orm_propName(propId);
        String propName = PROP_ID_TO_NAME[propId];
        if(propName == null)
           return super.orm_propName(propId);
        return propName;
    }

    @Override
    public int orm_propId(String propName) {
        Integer propId = PROP_NAME_TO_ID.get(propName);
        if(propId == null)
            return super.orm_propId(propName);
        return propId;
    }

    @Override
    public Object orm_propValue(int propId) {
        switch(propId){
        
            case PROP_ID_memberId:
               return getMemberId();
        
            case PROP_ID_roleId:
               return getRoleId();
        
            case PROP_ID_isOwner:
               return getIsOwner();
        
            case PROP_ID_isManager:
               return getIsManager();
        
            case PROP_ID_isRoleCharger:
               return getIsRoleCharger();
        
            case PROP_ID_version:
               return getVersion();
        
            case PROP_ID_createBy:
               return getCreateBy();
        
            case PROP_ID_createdAt:
               return getCreatedAt();
        
            case PROP_ID_updateBy:
               return getUpdateBy();
        
            case PROP_ID_updatedAt:
               return getUpdatedAt();
        
           default:
              return super.orm_propValue(propId);
        }
    }

    

    @Override
    public void orm_propValue(int propId, Object value){
        switch(propId){
        
            case PROP_ID_memberId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_memberId));
               }
               setMemberId(typedValue);
               break;
            }
        
            case PROP_ID_roleId:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_roleId));
               }
               setRoleId(typedValue);
               break;
            }
        
            case PROP_ID_isOwner:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isOwner));
               }
               setIsOwner(typedValue);
               break;
            }
        
            case PROP_ID_isManager:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isManager));
               }
               setIsManager(typedValue);
               break;
            }
        
            case PROP_ID_isRoleCharger:{
               java.lang.Boolean typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toBoolean(value,
                       err-> newTypeConversionError(PROP_NAME_isRoleCharger));
               }
               setIsRoleCharger(typedValue);
               break;
            }
        
            case PROP_ID_version:{
               java.lang.Integer typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toInteger(value,
                       err-> newTypeConversionError(PROP_NAME_version));
               }
               setVersion(typedValue);
               break;
            }
        
            case PROP_ID_createBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_createBy));
               }
               setCreateBy(typedValue);
               break;
            }
        
            case PROP_ID_createdAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_createdAt));
               }
               setCreatedAt(typedValue);
               break;
            }
        
            case PROP_ID_updateBy:{
               java.lang.String typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toString(value,
                       err-> newTypeConversionError(PROP_NAME_updateBy));
               }
               setUpdateBy(typedValue);
               break;
            }
        
            case PROP_ID_updatedAt:{
               java.sql.Timestamp typedValue = null;
               if(value != null){
                   typedValue = ConvertHelper.toTimestamp(value,
                       err-> newTypeConversionError(PROP_NAME_updatedAt));
               }
               setUpdatedAt(typedValue);
               break;
            }
        
           default:
              super.orm_propValue(propId,value);
        }
    }

    @Override
    public void orm_internalSet(int propId, Object value) {
        switch(propId){
        
            case PROP_ID_memberId:{
               onInitProp(propId);
               this._memberId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_roleId:{
               onInitProp(propId);
               this._roleId = (java.lang.String)value;
               orm_id(); // 如果是设置主键字段，则触发watcher
               break;
            }
        
            case PROP_ID_isOwner:{
               onInitProp(propId);
               this._isOwner = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isManager:{
               onInitProp(propId);
               this._isManager = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_isRoleCharger:{
               onInitProp(propId);
               this._isRoleCharger = (java.lang.Boolean)value;
               
               break;
            }
        
            case PROP_ID_version:{
               onInitProp(propId);
               this._version = (java.lang.Integer)value;
               
               break;
            }
        
            case PROP_ID_createBy:{
               onInitProp(propId);
               this._createBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_createdAt:{
               onInitProp(propId);
               this._createdAt = (java.sql.Timestamp)value;
               
               break;
            }
        
            case PROP_ID_updateBy:{
               onInitProp(propId);
               this._updateBy = (java.lang.String)value;
               
               break;
            }
        
            case PROP_ID_updatedAt:{
               onInitProp(propId);
               this._updatedAt = (java.sql.Timestamp)value;
               
               break;
            }
        
           default:
              super.orm_internalSet(propId,value);
        }
    }

    
    /**
     * 成员ID: member_id
     */
    public final java.lang.String getMemberId(){
         onPropGet(PROP_ID_memberId);
         return _memberId;
    }

    /**
     * 成员ID: member_id
     */
    public final void setMemberId(java.lang.String value){
        if(onPropSet(PROP_ID_memberId,value)){
            this._memberId = value;
            internalClearRefs(PROP_ID_memberId);
            orm_id();
        }
    }
    
    /**
     * 角色ID: role_id
     */
    public final java.lang.String getRoleId(){
         onPropGet(PROP_ID_roleId);
         return _roleId;
    }

    /**
     * 角色ID: role_id
     */
    public final void setRoleId(java.lang.String value){
        if(onPropSet(PROP_ID_roleId,value)){
            this._roleId = value;
            internalClearRefs(PROP_ID_roleId);
            orm_id();
        }
    }
    
    /**
     * 是否拥有者: is_owner
     */
    public final java.lang.Boolean getIsOwner(){
         onPropGet(PROP_ID_isOwner);
         return _isOwner;
    }

    /**
     * 是否拥有者: is_owner
     */
    public final void setIsOwner(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isOwner,value)){
            this._isOwner = value;
            internalClearRefs(PROP_ID_isOwner);
            
        }
    }
    
    /**
     * 是否管理员: is_manager
     */
    public final java.lang.Boolean getIsManager(){
         onPropGet(PROP_ID_isManager);
         return _isManager;
    }

    /**
     * 是否管理员: is_manager
     */
    public final void setIsManager(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isManager,value)){
            this._isManager = value;
            internalClearRefs(PROP_ID_isManager);
            
        }
    }
    
    /**
     * 是否角色负责人: is_role_charger
     */
    public final java.lang.Boolean getIsRoleCharger(){
         onPropGet(PROP_ID_isRoleCharger);
         return _isRoleCharger;
    }

    /**
     * 是否角色负责人: is_role_charger
     */
    public final void setIsRoleCharger(java.lang.Boolean value){
        if(onPropSet(PROP_ID_isRoleCharger,value)){
            this._isRoleCharger = value;
            internalClearRefs(PROP_ID_isRoleCharger);
            
        }
    }
    
    /**
     * 数据版本: version
     */
    public final java.lang.Integer getVersion(){
         onPropGet(PROP_ID_version);
         return _version;
    }

    /**
     * 数据版本: version
     */
    public final void setVersion(java.lang.Integer value){
        if(onPropSet(PROP_ID_version,value)){
            this._version = value;
            internalClearRefs(PROP_ID_version);
            
        }
    }
    
    /**
     * 创建人: create_by
     */
    public final java.lang.String getCreateBy(){
         onPropGet(PROP_ID_createBy);
         return _createBy;
    }

    /**
     * 创建人: create_by
     */
    public final void setCreateBy(java.lang.String value){
        if(onPropSet(PROP_ID_createBy,value)){
            this._createBy = value;
            internalClearRefs(PROP_ID_createBy);
            
        }
    }
    
    /**
     * 创建时间: created_at
     */
    public final java.sql.Timestamp getCreatedAt(){
         onPropGet(PROP_ID_createdAt);
         return _createdAt;
    }

    /**
     * 创建时间: created_at
     */
    public final void setCreatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_createdAt,value)){
            this._createdAt = value;
            internalClearRefs(PROP_ID_createdAt);
            
        }
    }
    
    /**
     * 修改人: update_by
     */
    public final java.lang.String getUpdateBy(){
         onPropGet(PROP_ID_updateBy);
         return _updateBy;
    }

    /**
     * 修改人: update_by
     */
    public final void setUpdateBy(java.lang.String value){
        if(onPropSet(PROP_ID_updateBy,value)){
            this._updateBy = value;
            internalClearRefs(PROP_ID_updateBy);
            
        }
    }
    
    /**
     * 修改时间: updated_at
     */
    public final java.sql.Timestamp getUpdatedAt(){
         onPropGet(PROP_ID_updatedAt);
         return _updatedAt;
    }

    /**
     * 修改时间: updated_at
     */
    public final void setUpdatedAt(java.sql.Timestamp value){
        if(onPropSet(PROP_ID_updatedAt,value)){
            this._updatedAt = value;
            internalClearRefs(PROP_ID_updatedAt);
            
        }
    }
    
    /**
     * 应用成员
     */
    public final com.mlc.application.dao.entity.MlcAppMember getAppMember(){
       return (com.mlc.application.dao.entity.MlcAppMember)internalGetRefEntity(PROP_NAME_appMember);
    }

    public final void setAppMember(com.mlc.application.dao.entity.MlcAppMember refEntity){
   
           if(refEntity == null){
           
                   this.setMemberId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appMember, refEntity,()->{
           
                           this.setMemberId(refEntity.getMemberId());
                       
           });
           }
       
    }
       
    /**
     * 应用角色
     */
    public final com.mlc.application.dao.entity.MlcAppRole getAppRole(){
       return (com.mlc.application.dao.entity.MlcAppRole)internalGetRefEntity(PROP_NAME_appRole);
    }

    public final void setAppRole(com.mlc.application.dao.entity.MlcAppRole refEntity){
   
           if(refEntity == null){
           
                   this.setRoleId(null);
               
           }else{
           internalSetRefEntity(PROP_NAME_appRole, refEntity,()->{
           
                           this.setRoleId(refEntity.getRoleId());
                       
           });
           }
       
    }
       
}
// resume CPD analysis - CPD-ON
