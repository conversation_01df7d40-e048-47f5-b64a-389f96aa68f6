<?xml version="1.0" encoding="UTF-8" ?>
<orm ext:mavenArtifactId="mlc-application" ext:entityPackageName="com.mlc.application.dao.entity"
     ext:useCoreModule="true" ext:registerShortName="true" ext:mavenGroupId="flowweb-zhongzhi"
     ext:basePackageName="com.mlc.application" ext:appName="mlc-application" ext:platformVersion="2.0.0-SNAPSHOT"
     ext:dialect="mysql,oracle" ext:mavenVersion="1.0-develop" x:schema="/nop/schema/orm/orm.xdef"
     xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ref-i18n-en="ref-i18n-en" xmlns:ext="ext"
     xmlns:orm-gen="orm-gen" xmlns:xpl="xpl" xmlns:ui="ui">

    <x:post-extends x:override="replace">
        <orm-gen:DefaultPostExtends xpl:lib="/nop/orm/xlib/orm-gen.xlib"/>
    </x:post-extends>

    <x:gen-extends x:override="replace">
        <orm-gen:DefaultGenExtends xpl:lib="/nop/orm/xlib/orm-gen.xlib"/>
    </x:gen-extends>

    <domains>
        <domain name="pkId" precision="32" stdSqlType="VARCHAR"/>
        <domain name="image" precision="100" stdDomain="file" stdSqlType="VARCHAR"/>
        <domain name="boolFlag" precision="1" stdSqlType="TINYINT"/>
        <domain name="xml-4k" precision="4000" stdSqlType="VARCHAR"/>
        <domain name="json-1000" precision="1000" stdDomain="json" stdSqlType="VARCHAR"/>
        <domain name="json-4K" precision="4000" stdDomain="json" stdSqlType="VARCHAR"/>
        <domain name="remark" precision="255" stdSqlType="VARCHAR"/>
        <domain name="version" stdSqlType="INTEGER"/>
        <domain name="createTime" stdSqlType="TIMESTAMP"/>
        <domain name="createdBy" precision="50" stdSqlType="VARCHAR"/>
        <domain name="updateTime" stdSqlType="TIMESTAMP"/>
        <domain name="updatedBy" precision="50" stdSqlType="VARCHAR"/>
        <domain name="delFlag" precision="1" stdDomain="boolFlag" stdSqlType="TINYINT"/>
        <domain name="delVersion" stdSqlType="BIGINT"/>
        <domain name="csvSet" precision="200" stdDomain="csv-set" stdSqlType="VARCHAR"/>
        <domain name="csvListWithNull" precision="500" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"/>
    </domains>

    <entities>
        <entity className="com.mlc.application.dao.entity.MlcAppInfo" createTimeProp="createdAt" createrProp="createBy"
                displayName="应用信息" name="com.mlc.application.dao.entity.MlcAppInfo" querySpace="application"
                registerShortName="true" tableName="mlc_app_info" updateTimeProp="updatedAt" updaterProp="updateBy"
                versionProp="version" i18n-en:displayName="App Info">
            <columns>
                <column code="app_id" displayName="应用ID" domain="pkId" mandatory="true" name="appId" precision="32"
                        primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="App ID" ui:show="X"/>
                <column code="model_definition_id" displayName="模型定义Id" domain="pkId" mandatory="true"
                        name="modelDefinitionId" precision="32" propId="2" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Model Definition Id"/>
                <column code="name" displayName="应用名" mandatory="true" name="name" precision="30" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Name"/>
                <column code="icon" defaultValue="0_lego" displayName="图标" name="icon" precision="50" propId="4"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Icon"/>
                <column code="icon_color" defaultValue="#2296F3" displayName="图标颜色" name="iconColor" precision="10"
                        propId="5" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Icon Color"/>
                <column code="icon_url" defaultValue="https://fp1.mingdaoyun.cn/customIcon/0_lego.svg"
                        displayName="图标路径" name="iconUrl" precision="100" propId="6" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Icon Url"/>
                <column code="create_type" defaultValue="0" displayName="创建类型" name="createType" propId="7"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Create Type"/>
                <column code="source_type" defaultValue="1" displayName="来源类型" name="sourceType" propId="8"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Source Type"/>
                <column code="nav_color" defaultValue="#2296F3" displayName="导航颜色" name="navColor" precision="30"
                        propId="9" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Nnav Color"/>
                <column code="light_color" displayName="背景色" name="lightColor" precision="30" propId="10"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Light Color"/>
                <column code="group_id" displayName="分组id" name="groupId" precision="32" propId="11"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Group Id"/>
                <column code="is_lock" defaultValue="0" displayName="锁定状态" name="isLock" propId="12"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="isLock"/>
                <column code="is_new" defaultValue="0" displayName="新建状态" name="isNew" propId="13" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="Is New"/>
                <column code="fixed" defaultValue="0" displayName="维护状态" name="fixed" propId="14" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="fixed"/>
                <column code="pc_display" defaultValue="0" displayName="Pc 端显示" name="pcDisplay" propId="15"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Pc Display"/>
                <column code="app_display" defaultValue="0" displayName="App 端显示" name="appDisplay" propId="16"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="App端显示"/>
                <column code="pc_navi_style" defaultValue="0" displayName="pc 端导航方式" name="pcNaviStyle" propId="17"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Pc Navi Style"/>
                <column code="display_icon" defaultValue="011" displayName="显示图标级别" name="displayIcon" precision="3"
                        propId="18" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Display Icon"/>
                <column code="select_app_itme_type" comment="（2 = 是，1 = 老配置，始终第一个）" defaultValue="2"
                        displayName="记住上次使用" name="selectAppItmeType" propId="19" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Select App Itme Type"/>
                <column code="web_mobile_display" defaultValue="0" displayName="Web 移动端显示" name="webMobileDisplay"
                        propId="20" stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Web Mobile Display"/>
                <column code="app_navi_style" defaultValue="0" displayName="移动端导航方式" name="appNaviStyle" propId="21"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="App Navi Style"/>
                <column code="grid_display_mode" defaultValue="0" displayName="移动端显示模式" name="gridDisplayMode"
                        propId="22" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Grid Display Mode"/>
                <column code="app_navi_display_type" defaultValue="0" displayName="移动端分组展开方式" name="appNaviDisplayType"
                        propId="23" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="App Navi Display Type"/>
                <column code="view_hide_navi" defaultValue="1" displayName="查看隐藏项" name="viewHideNavi" propId="24"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="View Hide Navi"/>
                <column code="is_marked" defaultValue="0" displayName="星标状态" name="isMarked" propId="25"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Is Marked"/>
                <column code="description" displayName="应用说明" name="description" precision="2000" propId="26"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="description"/>
                <column code="app_status" defaultValue="1" displayName="状态" name="appStatus" propId="27"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="App Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="28"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="29" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="30" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="31" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="32" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-many cascadeDelete="true" displayName="关联应用分组" name="appSections"
                         refEntityName="com.mlc.application.dao.entity.MlcAppSection" refPropName="appInfo"
                         tagSet="pub,insertable,updatable,query,cascade-delete" i18n-en:displayName="App Sections">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="关联工作表" name="workSheetItems"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet" refPropName="appInfo"
                         tagSet="pub,insertable,updatable,cascade-delete,query" i18n-en:displayName="Work Sheet Items">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                </to-many>
                <to-many displayName="关联应用角色" name="appMembers"
                         refEntityName="com.mlc.application.dao.entity.MlcAppMember" refPropName="appInfo"
                         tagSet="pub,insertable,updatable" i18n-en:displayName="App Roles">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                </to-many>
                <to-many displayName="关联应用角色" name="appRoles" refEntityName="com.mlc.application.dao.entity.MlcAppRole"
                         refPropName="appInfo" tagSet="pub,insertable,updatable" i18n-en:displayName="App Roles">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                </to-many>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppSection" createTimeProp="createdAt"
                createrProp="createBy" displayName="应用分组" name="com.mlc.application.dao.entity.MlcAppSection"
                querySpace="application" registerShortName="true" tableName="mlc_app_section" updateTimeProp="updatedAt"
                updaterProp="updateBy" versionProp="version" i18n-en:displayName="App Section">
            <columns>
                <column code="app_section_id" displayName="应用分组ID" domain="pkId" mandatory="true" name="appSectionId"
                        precision="32" primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Section ID" ui:show="X"/>
                <column code="app_id" displayName="应用ID" mandatory="true" name="appId" precision="32" propId="2"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Id" ui:show="X"/>
                <column code="name" displayName="应用名" mandatory="true" name="name" precision="30" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Name"/>
                <column code="parent_id" displayName="父级ID" name="parentId" precision="50" propId="4"
                        stdDataType="string" stdSqlType="VARCHAR" tagSet="parent" i18n-en:displayName="Parent ID"/>
                <column code="icon" displayName="图标" name="icon" precision="30" propId="5" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="icon"/>
                <column code="icon_url" displayName="图标路径" name="iconUrl" precision="100" propId="6"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Icon Url"/>
                <column code="is_lock" defaultValue="0" displayName="锁定状态" name="isLock" propId="7"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="isLock"/>
                <column code="fixed" defaultValue="0" displayName="维护状态" name="fixed" propId="8" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="fixed"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="9"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="10" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="11" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="12" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="13" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="应用信息" name="appInfo" refDisplayName="关联应用分组"
                        refEntityName="com.mlc.application.dao.entity.MlcAppInfo" refPropName="appSections"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-query,ref-cascade-delete"
                        i18n-en:displayName="App Info" ref-i18n-en:displayName="App Sections">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-one displayName="父资源" name="parent" refDisplayName="子资源"
                        refEntityName="com.mlc.application.dao.entity.MlcAppSection" refPropName="childSections"
                        tagSet="pub,ref-pub,ref-delete" i18n-en:displayName="Parent" ref-i18n-en:displayName="Children">
                    <join>
                        <on leftProp="parentId" rightProp="appSectionId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-many displayName="子资源" name="childSections"
                         refEntityName="com.mlc.application.dao.entity.MlcAppSection" refPropName="parent"
                         tagSet="pub,delete" i18n-en:displayName="Children">
                    <join>
                        <on leftProp="appSectionId" rightProp="parentId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="关联应用组" name="workSheetInfo"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet" refPropName="appSection"
                         tagSet="pub,insertable,updatable,cascade-delete,query" i18n-en:displayName="Work Sheet Info">
                    <join>
                        <on leftProp="appSectionId" rightProp="appSectionId"/>
                    </join>
                </to-many>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppWorksheet" createTimeProp="createdAt"
                createrProp="createBy" displayName="工作表" name="com.mlc.application.dao.entity.MlcAppWorksheet"
                querySpace="application" registerShortName="true" tableName="mlc_app_worksheet"
                updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version" i18n-en:displayName="Worksheet">
            <columns>
                <column code="worksheet_id" displayName="工作表ID" domain="pkId" mandatory="true" name="worksheetId"
                        precision="32" primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Work Sheet ID" ui:show="X"/>
                <column code="app_id" displayName="应用 ID" domain="pkId" mandatory="true" name="appId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Id" ui:show="X"/>
                <column code="app_section_id" displayName="应用分组 ID" domain="pkId" mandatory="true" name="appSectionId"
                        precision="32" propId="3" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="App Section Id" ui:show="X"/>
                <column code="model_object_entity_id" displayName="模型对象 ID" domain="pkId" mandatory="true"
                        name="modelObjectEntityId" precision="32" propId="4" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Model Object Entity Id" ui:show="C"/>
                <column code="model_object_entity_name" displayName="模型对象名称" domain="pkId" mandatory="true"
                        name="modelObjectEntityName" precision="32" propId="5" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Model Object Entity Name" ui:show="C"/>
                <column code="name" displayName="工作表名称" mandatory="true" name="name" precision="30" propId="6"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Name"/>
                <column code="alias" displayName="工作表别名" name="alias" precision="30" propId="7" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Alias"/>
                <column code="entity_name" defaultValue="记录" displayName="记录名称" name="entityName" precision="30"
                        propId="8" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Entity Name"/>
                <column code="icon" defaultValue="table" displayName="图标" name="icon" precision="30" propId="9"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Icon"/>
                <column code="icon_color" defaultValue="#2296F3" displayName="图标颜色" name="iconColor" precision="30"
                        propId="10" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Icon Color"/>
                <column code="icon_url" defaultValue="https://fp1.mingdaoyun.cn/customIcon/table.svg" displayName="图标路径"
                        name="iconUrl" precision="100" propId="11" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Icon Url"/>
                <column code="count" defaultValue="0" displayName="数量" name="count" propId="12" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Count"/>
                <column code="visible_type" defaultValue="1" displayName="分享状态" name="visibleType" propId="13"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Visible Type"/>
                <column code="open_approval" defaultValue="0" displayName="开启审批" name="openApproval" propId="14"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Open Approval"/>
                <column code="workflow_child_table_switch" defaultValue="0" displayName="工作流子表切换"
                        name="workflowChildTableSwitch" propId="15" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Workflow Child Table Switch"/>
                <column code="navigate_hide" defaultValue="0" displayName="侧边栏状态" name="navigateHide" propId="16"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Navigate Hide"/>
                <column code="is_worksheet_query" defaultValue="0" displayName="是否配置工作表查询" name="isWorksheetQuery"
                        propId="17" stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Is Worksheet Query"/>
                <column code="is_marked" defaultValue="0" displayName="收藏状态" name="isMarked" propId="18"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Is Marked"/>
                <column code="controls" displayName="控件" name="controls" precision="65535" propId="19"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Controls"/>
                <column code="advanced_setting" defaultValue="{}" displayName="高级设置" domain="json-1000"
                        name="advancedSetting" precision="1000" propId="20" stdDataType="string" stdDomain="json"
                        stdSqlType="VARCHAR" i18n-en:displayName="Advanced Setting"/>
                <column code="submit_setting" defaultValue="{}" displayName="提交设置" domain="json-1000"
                        name="submitSetting" precision="1000" propId="21" stdDataType="string" stdDomain="json"
                        stdSqlType="VARCHAR" i18n-en:displayName="Submit Setting"/>
                <column code="developer_notes" displayName="开发者备注" name="developerNotes" precision="200" propId="22"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Developer Notes"/>
                <column code="resume" displayName="摘要" name="resume" precision="80" propId="23" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Resume"/>
                <column code="dec" displayName="详细说明" name="dec" precision="500" propId="24" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Dec"/>
                <column code="status" comment="1= 显示，2 = 全隐藏，3 = PC隐藏，4 = 移动端隐藏" defaultValue="1" displayName="状态"
                        name="status" propId="25" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="26"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="27" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="28" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="29" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="30" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="所属应用分组" name="appSection" refDisplayName="关联应用组"
                        refEntityName="com.mlc.application.dao.entity.MlcAppSection" refPropName="workSheetInfo"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete,ref-query"
                        i18n-en:displayName="App Section" ref-i18n-en:displayName="Work Sheet Info">
                    <join>
                        <on leftProp="appSectionId" rightProp="appSectionId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-one displayName="应用信息" name="appInfo" refDisplayName="关联工作表"
                        refEntityName="com.mlc.application.dao.entity.MlcAppInfo" refPropName="workSheetItems"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete,ref-query"
                        i18n-en:displayName="App Info" ref-i18n-en:displayName="Work Sheet Items">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-many cascadeDelete="true" displayName="关联工作表视图" name="worksheetViews"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheetView" refPropName="worksheet"
                         tagSet="pub,insertable,updatable,cascade-delete,query" i18n-en:displayName="Worksheet View">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                    <sort>
                        <field name="orderNum"/>
                    </sort>
                </to-many>
                <to-many cascadeDelete="true" displayName="关联工作表规则" name="worksheetRuleItems"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheetRule" refPropName="worksheet"
                         tagSet="pub,insertable,updatable,cascade-delete" i18n-en:displayName="Worksheet Rule Items">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="关联工作表按钮" name="worksheetBtns"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheetButton" refPropName="worksheet"
                         tagSet="pub,insertable,updatable,cascade-delete" i18n-en:displayName="Worksheet Btns">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="关联工作表打印模板" name="worksheetPrintTemplates"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate"
                         refPropName="worksheet" tagSet="pub,insertable,updatable,cascade-delete"
                         i18n-en:displayName="Worksheet Print Templates">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="关联工作表开发" name="worksheetSwitchs"
                         refEntityName="com.mlc.application.dao.entity.MlcAppWorksheetSwitch" refPropName="worksheet"
                         tagSet="pub,insertable,updatable,cascade-delete" i18n-en:displayName="Worksheet Switchs">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                </to-many>
            </relations>
            <unique-keys>
                <unique-key columns="alias" constraint="UK_MLC_APP_WORKSHEET_ALIAS" name="aliasKey"/>
            </unique-keys>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppWorksheetView" createTimeProp="createdAt"
                createrProp="createBy" displayName="工作表视图" name="com.mlc.application.dao.entity.MlcAppWorksheetView"
                querySpace="application" registerShortName="true" tableName="mlc_app_worksheet_view"
                updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version"
                i18n-en:displayName="Worksheet View">
            <columns>
                <column code="view_id" displayName="视图 ID" domain="pkId" mandatory="true" name="viewId" precision="32"
                        primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="View Id" ui:show="X"/>
                <column code="worksheet_id" displayName="工作表 ID" mandatory="true" name="worksheetId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Worksheet Id"
                        ui:show="X"/>
                <column code="name" displayName="工作表名称" mandatory="true" name="name" precision="30" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Name"/>
                <column code="un_read" defaultValue="0" displayName="是否已读" name="unRead" propId="4"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Un Read"/>
                <column code="sort_cid" displayName="排序字段" name="sortCid" precision="40" propId="5" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Sort Cid"/>
                <column code="sort_type" displayName="排序类型" name="sortType" propId="6" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="sort Type"/>
                <column code="cover_cid" displayName="封面字段" name="coverCid" precision="32" propId="7"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Cover Cid"/>
                <column code="cover_type" comment="0：填满 1：完整显示" defaultValue="0" displayName="封面类型" name="coverType"
                        propId="8" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Cover Type"/>
                <column code="custom_display" comment="移动端使用" defaultValue="0" displayName="是否配置自定义显示列"
                        name="customDisplay" propId="9" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Custom Display"/>
                <column code="display_controls" comment="移动端使用" displayName="显示字段" domain="csvListWithNull"
                        name="displayControls" precision="500" propId="10" stdDataType="string"
                        stdDomain="csv-list-with-null" stdSqlType="VARCHAR" i18n-en:displayName="Display Controls"/>
                <column code="view_type" comment="0:列表 1：看板 2：层级" defaultValue="0" displayName="视图类型" name="viewType"
                        propId="11" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="View Type"
                        ext:dict="com.mlc.base.common.enums.worksheet.ViewTypeEnum"/>
                <column code="child_type" defaultValue="0" displayName="层级类型" name="childType" propId="12"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Child Type"/>
                <column code="view_control" comment="看板等分组控件ID" displayName="视图维度ID(分组ID)" name="viewControl"
                        precision="100" propId="13" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="View Control"/>
                <column code="row_height" defaultValue="0" displayName="行高" name="rowHeight" propId="14"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Row Height"/>
                <column code="show_control_name" defaultValue="0" displayName="显示控件名称" name="showControlName"
                        propId="15" stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Show Control Name"/>
                <column code="show_controls" displayName="Web显示字段" domain="csvListWithNull" name="showControls"
                        precision="500" propId="16" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Show Controls"/>
                <column code="layers_name" displayName="层级名称" domain="csvListWithNull" name="layersName" precision="500"
                        propId="17" stdDataType="string" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"
                        i18n-en:displayName="Layers Name"/>
                <column code="controls" comment="设置此视图下的表单中需要对用户隐藏的字段" displayName="视图隐藏字段" domain="csvListWithNull"
                        name="controls" precision="1000" propId="18" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Controls"/>
                <column code="view_controls" displayName="多表层级视图控件" domain="csvListWithNull" name="viewControls"
                        precision="1000" propId="19" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="View Controls"/>
                <column code="controls_sorts" displayName="字段排序" domain="csvListWithNull" name="controlsSorts"
                        precision="1000" propId="20" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Controls Sorts"/>
                <column code="nav_group" defaultValue="[]" displayName="导航分组" name="navGroup" precision="1000"
                        propId="21" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Nav Group"/>
                <column code="filters" defaultValue="[]" displayName="初始过滤" name="filters" precision="1000" propId="22"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Filters"/>
                <column code="fast_filters" defaultValue="[]" displayName="快速过滤" name="fastFilters" precision="1000"
                        propId="23" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Fast Filters"/>
                <column code="more_sort" defaultValue="[]" displayName="更多排序" name="moreSort" precision="2000"
                        propId="24" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="More Sort"/>
                <column code="advanced_setting" defaultValue="{}" displayName="高级设置" domain="json-1000"
                        name="advancedSetting" precision="1000" propId="25" stdDataType="string" stdDomain="json"
                        stdSqlType="VARCHAR" i18n-en:displayName="Navigate Hide"/>
                <column code="is_marked" defaultValue="0" displayName="收藏状态" name="isMarked" propId="26"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Is Marked"/>
                <column code="order_num" displayName="排序" name="orderNum" propId="27" stdDataType="int"
                        stdSqlType="INTEGER" tagSet="sort asc" i18n-en:displayName="Order Num"/>
                <column code="status" defaultValue="1" displayName="状态" name="status" propId="28" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="29"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="30" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="31" stdDataType="timestamp" stdSqlType="TIMESTAMP" tagSet="sort"
                        i18n-en:displayName="Created At" ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="32" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="33" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="所属工作表" name="worksheet" refDisplayName="关联工作表视图"
                        refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet" refPropName="worksheetViews"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete,ref-query"
                        i18n-en:displayName="Worksheet" ref-i18n-en:displayName="Worksheet View">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                    <ref-set>
                        <sort>
                            <field name="orderNum"/>
                        </sort>
                    </ref-set>
                </to-one>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppWorksheetRule" createTimeProp="createdAt"
                createrProp="createBy" displayName="工作表业务规则" name="com.mlc.application.dao.entity.MlcAppWorksheetRule"
                querySpace="application" registerShortName="true" tableName="mlc_app_worksheet_rule"
                updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version"
                i18n-en:displayName="Worksheet Rule">
            <columns>
                <column code="rule_id" displayName="业务规则 ID" domain="pkId" mandatory="true" name="ruleId" precision="32"
                        primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Rule Id" ui:show="X"/>
                <column code="worksheet_id" displayName="工作表 ID" mandatory="true" name="worksheetId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Worksheet Id"
                        ui:show="X"/>
                <column code="name" displayName="工作表名称" mandatory="true" name="name" precision="30" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Name"/>
                <column code="type" comment="0:交互  1：验证 2：锁定" displayName="规则类型" mandatory="true" name="type" propId="4"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Type"/>
                <column code="check_type" comment="0：前端  1：前后端" displayName="检查类型" name="checkType" propId="5"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Check Type"/>
                <column code="hint_type" comment="0：输入和提交 1：仅提交" displayName="提示类型" name="hintType" propId="6"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Hint Type"/>
                <column code="control_ids" displayName="控件集合" domain="csvListWithNull" name="controlIds" precision="500"
                        propId="7" stdDataType="string" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"
                        i18n-en:displayName="Control Ids"/>
                <column code="filters" defaultValue="[]" displayName="初始过滤" mandatory="true" name="filters"
                        precision="1000" propId="8" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Filters"/>
                <column code="rule_items" defaultValue="[]" displayName="规则项" mandatory="true" name="ruleItems"
                        precision="1000" propId="9" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Rule Items"/>
                <column code="open_drawer" defaultValue="0" displayName="打开抽屉" name="openDrawer" propId="10"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Open Drawer"/>
                <column code="disabled" defaultValue="0" displayName="已禁用" mandatory="true" name="disabled" propId="11"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Disabled"/>
                <column code="order_num" displayName="排序" name="orderNum" propId="12" stdDataType="int"
                        stdSqlType="INTEGER" tagSet="sort" i18n-en:displayName="Order Num"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="13"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="14" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="15" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="16" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="17" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="所属工作表" name="worksheet" refDisplayName="关联工作表规则"
                        refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet" refPropName="worksheetRuleItems"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete"
                        i18n-en:displayName="Worksheet" ref-i18n-en:displayName="Worksheet Rule Items">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                    <ref-set/>
                </to-one>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppWorksheetButton" createTimeProp="createdAt"
                createrProp="createBy" displayName="工作表自定义按钮"
                name="com.mlc.application.dao.entity.MlcAppWorksheetButton" querySpace="application"
                registerShortName="true" tableName="mlc_app_worksheet_button" updateTimeProp="updatedAt"
                updaterProp="updateBy" versionProp="version" i18n-en:displayName="Worksheet Button">
            <columns>
                <column code="btn_id" displayName="按钮ID" domain="pkId" mandatory="true" name="btnId" precision="32"
                        primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Btn Id" ui:show="X"/>
                <column code="worksheet_id" displayName="工作表ID" mandatory="true" name="worksheetId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Worksheet Id"
                        ui:show="X"/>
                <column code="name" displayName="名称" mandatory="true" name="name" precision="20" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Name"/>
                <column code="is_all_view" displayName="是否全部视图" name="isAllView" propId="4" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Is All View"/>
                <column code="display_views" displayName="显示视图" domain="csvListWithNull" name="displayViews"
                        precision="500" propId="5" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Display Views"/>
                <column code="click_type" comment="1：立即执行 2：二次确认 3：填写" displayName="点击类型" name="clickType" propId="6"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Click Type"/>
                <column code="confirm_msg" displayName="确认消息" name="confirmMsg" precision="30" propId="7"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Confirm Msg"/>
                <column code="sure_name" displayName="确认名称" name="sureName" precision="10" propId="8"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Sure Name"/>
                <column code="cancel_name" displayName="取消名称" name="cancelName" precision="10" propId="9"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Cancel Name"/>
                <column code="write_type" comment="1：当前记录 2：关联记录" displayName="填写对象类型" name="writeType" propId="10"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Write Type"/>
                <column code="write_object" comment="1：填写指定字段 2：新建关联记录" displayName="填写内容" name="writeObject"
                        propId="11" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Write Object"/>
                <column code="write_controls" displayName="填写控件" domain="csvListWithNull" name="writeControls"
                        precision="500" propId="12" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Write Controls"/>
                <column code="relation_control" displayName="关联记录ID" name="relationControl" precision="36" propId="13"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Relation Control"/>
                <column code="add_relation_control_id" displayName="新建关联记录ID" name="addRelationControlId" precision="36"
                        propId="14" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Add Relation Control Id"/>
                <column code="workflow_type" comment="1:执行 2：不执行" displayName="继续执行工作流" name="workflowType" propId="15"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Workflow Type"/>
                <column code="workflow_id" displayName="工作流ID" name="workflowId" precision="36" propId="16"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Workflow Id"/>
                <column code="filters" defaultValue="[]" displayName="过滤器" name="filters" precision="1000" propId="17"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Filters"/>
                <column code="color" defaultValue="#2296F3" displayName="颜色" name="color" precision="1000" propId="18"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Color"/>
                <column code="icon" defaultValue="done_2" displayName="图标" name="icon" precision="1000" propId="19"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Icon"/>
                <column code="icon_url" defaultValue="https://fp1.mingdaoyun.cn/custom_icon/PNG/done_2.png"
                        displayName="图标URL" name="iconUrl" precision="100" propId="20" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Icon Url"/>
                <column code="desc" displayName="描述" name="desc" precision="200" propId="21" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Desc"/>
                <column code="advanced_setting" defaultValue="{}" displayName="高级设置" domain="json-1000"
                        name="advancedSetting" precision="1000" propId="22" stdDataType="string" stdDomain="json"
                        stdSqlType="VARCHAR" i18n-en:displayName="Advanced Setting"/>
                <column code="enable_confirm" defaultValue="0" displayName="启用二次确认" name="enableConfirm" propId="23"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Enable Confirm"/>
                <column code="verify_pwd" defaultValue="0" displayName="校验密码" name="verifyPwd" propId="24"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Verify Pwd"/>
                <column code="is_batch" defaultValue="0" displayName="是否多条数据源" name="isBatch" propId="25"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Is Batch"/>
                <column code="show_type" comment="1: 一直 2：满足筛选条件" defaultValue="1" displayName="启用按钮类型" name="showType"
                        propId="26" stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Show Type"/>
                <column code="add_relation_control" displayName="添加关联控件" name="addRelationControl" precision="200"
                        propId="27" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Add Relation Control"/>
                <column code="edit_attrs" displayName="属性" domain="csvListWithNull" name="editAttrs" precision="200"
                        propId="28" stdDataType="string" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"
                        i18n-en:displayName="Edit Attrs"/>
                <column code="disabled" defaultValue="0" displayName="禁用" name="disabled" propId="29"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Disabled"/>
                <column code="status" defaultValue="1" displayName="状态" name="status" propId="30" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="31"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="32" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="33" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="34" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="35" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="所属工作表" name="worksheet" refDisplayName="关联工作表按钮"
                        refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet" refPropName="worksheetBtns"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete"
                        i18n-en:displayName="Worksheet" ref-i18n-en:displayName="Worksheet Btns">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                    <ref-set/>
                </to-one>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate" createTimeProp="createdAt"
                createrProp="createBy" displayName="工作表自定义打印模板"
                name="com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate" querySpace="application"
                registerShortName="true" tableName="mlc_app_worksheet_print_template" updateTimeProp="updatedAt"
                updaterProp="updateBy" versionProp="version" i18n-en:displayName="Worksheet Print Template">
            <columns>
                <column code="print_template_id" displayName="打印模板 ID" domain="pkId" mandatory="true"
                        name="printTemplateId" precision="32" primary="true" propId="1" stdDataType="string"
                        stdSqlType="VARCHAR" tagSet="seq" i18n-en:displayName="Print Template Id" ui:show="X"/>
                <column code="worksheet_id" displayName="工作表 ID" domain="pkId" mandatory="true" name="worksheetId"
                        precision="32" propId="2" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Worksheet Id"/>
                <column code="name" displayName="名称" mandatory="true" name="name" precision="30" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Name"/>
                <column code="form_name" displayName="来自名称" name="formName" precision="30" propId="4"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Form Name"/>
                <column code="company_name" displayName="公司名称" name="companyName" precision="30" propId="5"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Company Name"/>
                <column code="type" defaultValue="0" displayName="类型" name="type" propId="6" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Type"/>
                <column code="range" defaultValue="1" displayName="使用范围" name="range" propId="7" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Range"/>
                <column code="control_styles" defaultValue="[]" displayName="控件样式" name="controlStyles" precision="255"
                        propId="8" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Control Styles"/>
                <column code="relation_style" defaultValue="[]" displayName="关联样式" name="relationStyle" precision="255"
                        propId="9" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Relation Style"/>
                <column code="owner_account" displayName="拥有者" name="ownerAccount" precision="30" propId="10"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Owner Account"/>
                <column code="print_time" defaultValue="1" displayName="打印时间" name="printTime" propId="11"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Print Time"/>
                <column code="qr_code" defaultValue="1" displayName="二维码" name="qrCode" propId="12"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="QrCode"/>
                <column code="print_account" defaultValue="1" displayName="打印人" name="printAccount" propId="13"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Print Account"/>
                <column code="logo_checked" defaultValue="1" displayName="打印 logo" name="logoChecked" propId="14"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Logo Checked"/>
                <column code="title_checked" defaultValue="1" displayName="标题是否打印" name="titleChecked" propId="15"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Title Checked"/>
                <column code="company_name_checked" defaultValue="0" displayName="公司名称否打印" name="companyNameChecked"
                        propId="16" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Company Name Checked"/>
                <column code="form_name_checked" defaultValue="1" displayName="表单标题" name="formNameChecked" propId="17"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Form Name Checked"/>
                <column code="create_time_checked" defaultValue="0" displayName="创建时间是否打印" name="createTimeChecked"
                        propId="18" stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Create Time Checked"/>
                <column code="create_account_checked" defaultValue="0" displayName="创建者是否打印" name="createAccountChecked"
                        propId="19" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Create Account Checked"/>
                <column code="update_time_checked" defaultValue="0" displayName="更新时间是否打印" name="updateTimeChecked"
                        propId="20" stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Update Time Checked"/>
                <column code="update_account_checked" defaultValue="0" displayName="更新人是否打印" name="updateAccountChecked"
                        propId="21" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Update Account Checked"/>
                <column code="owner_account_checked" defaultValue="0" displayName="拥有者是否打印" name="ownerAccountChecked"
                        propId="22" stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="OwnerAccountChecked"/>
                <column code="show_data" defaultValue="0" displayName="空值是否隐藏" name="showData" propId="23"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Show Data"/>
                <column code="print_option" defaultValue="0" displayName="选项字段平铺打印" name="printOption" propId="24"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Print Option"/>
                <column code="share_type" comment="0 普通=&gt;记录分享 1 对内=&gt;记录详情" defaultValue="0" displayName="分享类型"
                        name="shareType" propId="25" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Share Type"/>
                <column code="font" displayName="字体" name="font" propId="26" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Font"/>
                <column code="allow_download_permission" defaultValue="0" displayName="允许下载打印文件"
                        name="allowDownloadPermission" propId="27" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Allow Download Permission"/>
                <column code="advance_settings"
                        defaultValue="[
            {
                &quot;key&quot;: &quot;atta_style&quot;,
                &quot;value&quot;: &quot;{}&quot;
            }
        ]"
                        displayName="高级设置" domain="json-1000" name="advanceSettings" precision="1000" propId="28"
                        stdDataType="string" stdDomain="json" stdSqlType="VARCHAR"
                        i18n-en:displayName="Advanced Settings"/>
                <column code="approve_position" comment="0:在明细内显示 1:在明细表下方显示" defaultValue="0" displayName="审批签名位置"
                        name="approvePosition" propId="29" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Approve Position"/>
                <column code="approval_ids" defaultValue="[]" displayName="审批批准编号" name="approvalIds" precision="100"
                        propId="30" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Approval Ids"/>
                <column code="views" displayName="视图" domain="csvListWithNull" name="views" precision="500" propId="31"
                        stdDataType="string" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"
                        i18n-en:displayName="Views"/>
                <column code="order_number" defaultValue="[]" displayName="排序" name="orderNumber" precision="10"
                        propId="32" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Order Number"/>
                <column code="filters" defaultValue="[]" displayName="过滤器" name="filters" precision="1000" propId="33"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Filters"/>
                <column code="remark" displayName="备注" name="remark" propId="34" stdDataType="string"
                        stdSqlType="VARCHAR" i18n-en:displayName="Remark"/>
                <column code="disabled" defaultValue="0" displayName="禁用" name="disabled" propId="35"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Disabled"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="36"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="37" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="38" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="39" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="40" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="所属工作表" name="worksheet" refDisplayName="关联工作表打印模板"
                        refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet"
                        refPropName="worksheetPrintTemplates"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete"
                        i18n-en:displayName="Worksheet" ref-i18n-en:displayName="Worksheet Print Templates">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                    <ref-set/>
                </to-one>
            </relations>
            <unique-keys>
                <unique-key columns="name" constraint="UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME" name="nameKey"/>
            </unique-keys>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppWorksheetSwitch" createTimeProp="createdAt"
                createrProp="createBy" displayName="工作表开关" name="com.mlc.application.dao.entity.MlcAppWorksheetSwitch"
                querySpace="application" registerShortName="true" tableName="mlc_app_worksheet_switch"
                updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version"
                i18n-en:displayName="Worksheet Button">
            <columns>
                <column code="switch_id" displayName="按钮ID" domain="pkId" mandatory="true" name="switchId"
                        precision="32" primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Btn Id" ui:show="X"/>
                <column code="worksheet_id" displayName="工作表ID" mandatory="true" name="worksheetId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Worksheet Id"
                        ui:show="X"/>
                <column code="type" displayName="开关类型" mandatory="true" name="type" propId="3" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Type"/>
                <column code="role_type" displayName="角色类型" mandatory="true" name="roleType" propId="4"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Role Type"/>
                <column code="state" displayName="状态" mandatory="true" name="state" propId="5" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="State"/>
                <column code="view" displayName="视图" domain="csvListWithNull" name="view" precision="100" propId="6"
                        stdDataType="string" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"
                        i18n-en:displayName="View"/>
                <column code="view_ids" displayName="视图" domain="csvListWithNull" name="viewIds" precision="100"
                        propId="7" stdDataType="string" stdDomain="csv-list-with-null" stdSqlType="VARCHAR"
                        i18n-en:displayName="View Ids"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="8"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="9" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="10" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="11" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="12" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <comment>相关于资源点</comment>
            <relations>
                <to-one displayName="所属工作表" name="worksheet" refDisplayName="关联工作表开发"
                        refEntityName="com.mlc.application.dao.entity.MlcAppWorksheet" refPropName="worksheetSwitchs"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete"
                        i18n-en:displayName="Worksheet" ref-i18n-en:displayName="Worksheet Switchs">
                    <join>
                        <on leftProp="worksheetId" rightProp="worksheetId"/>
                    </join>
                    <ref-set/>
                </to-one>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppMember" createTimeProp="createdAt"
                createrProp="createBy" displayName="应用成员" name="com.mlc.application.dao.entity.MlcAppMember"
                querySpace="application" registerShortName="true" tableName="mlc_app_member" updateTimeProp="updatedAt"
                updaterProp="updateBy" versionProp="version" i18n-en:displayName="App Member">
            <columns>
                <column code="member_id" displayName="成员ID" domain="pkId" mandatory="true" name="memberId"
                        precision="32" primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Member Id" ui:show="X"/>
                <column code="app_id" displayName="应用 ID" domain="pkId" mandatory="true" name="appId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Id" ui:show="X"/>
                <column code="member_org_user_id" displayName="成员关联组织用户" domain="pkId" mandatory="true"
                        name="memberOrgUserId" precision="32" propId="3" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Member Pk" ui:show="X"/>
                <column code="member_type" comment="100:管理员 2:运营者  1:开发者" displayName="角色类型" mandatory="true"
                        name="memberType" propId="4" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Member Type"
                        ext:dict="com.mlc.base.common.enums.application.AppMemberTypeEnum"/>
                <column code="status" defaultValue="1" displayName="状态" mandatory="true" name="status" propId="5"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="6"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="7" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="8" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="9" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="10" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <comment>这里不只有用户，还有组织角色、部门、职位等</comment>
            <relations>
                <to-one displayName="应用信息" name="appInfo" refDisplayName="关联应用角色"
                        refEntityName="com.mlc.application.dao.entity.MlcAppInfo" refPropName="appMembers"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable" i18n-en:displayName="App Info"
                        ref-i18n-en:displayName="App Roles">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-many cascadeDelete="true" displayName="应用角色映射" name="appRoleMappings"
                         refEntityName="com.mlc.application.dao.entity.MlcAppMemberRole" refPropName="appMember"
                         tagSet="pub,cascade-delete,insertable,updatable,connection"
                         i18n-en:displayName="App Role Mappings">
                    <join>
                        <on leftProp="memberId" rightProp="memberId"/>
                    </join>
                </to-many>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppMemberRole" createTimeProp="createdAt"
                createrProp="createBy" displayName="成员角色中间表" name="com.mlc.application.dao.entity.MlcAppMemberRole"
                querySpace="application" registerShortName="true" tableName="mlc_app_member_role"
                tagSet="no-web,many-to-many" updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version"
                i18n-en:displayName="Member Role">
            <columns>
                <column code="member_id" displayName="成员ID" domain="pkId" mandatory="true" name="memberId"
                        precision="32" primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Member Id" ui:show="X"/>
                <column code="role_id" displayName="角色ID" domain="pkId" mandatory="true" name="roleId" precision="32"
                        primary="true" propId="2" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Role ID" ui:show="X"/>
                <column code="is_owner" comment="只能为用户，且只有一个" defaultValue="0" displayName="是否拥有者" mandatory="true"
                        name="isOwner" propId="3" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Is Owner"/>
                <column code="is_manager" comment="仅支持外部链接应用" defaultValue="0" displayName="是否管理员" mandatory="true"
                        name="isManager" propId="4" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Is Manager"/>
                <column code="is_role_charger" defaultValue="0" displayName="是否角色负责人" mandatory="true"
                        name="isRoleCharger" propId="5" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Is Role Charger"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="6"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="7" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Create By" ui:show="X"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="8" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="X"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="9" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="X"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="10" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="X"/>
            </columns>
            <relations>
                <to-one displayName="应用成员" name="appMember" refDisplayName="应用角色映射"
                        refEntityName="com.mlc.application.dao.entity.MlcAppMember" refPropName="appRoleMappings"
                        tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable,ref-connection"
                        i18n-en:displayName="App Member" ref-i18n-en:displayName="App Role Mappings">
                    <join>
                        <on leftProp="memberId" rightProp="memberId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-one displayName="应用角色" name="appRole" refDisplayName="应用成员映射"
                        refEntityName="com.mlc.application.dao.entity.MlcAppRole" refPropName="appMemberMappings"
                        tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable,ref-connection"
                        i18n-en:displayName="App Role" ref-i18n-en:displayName="App Member Mappings">
                    <join>
                        <on leftProp="roleId" rightProp="roleId"/>
                    </join>
                    <ref-set/>
                </to-one>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppRole" createTimeProp="createdAt" createrProp="createBy"
                displayName="应用角色" name="com.mlc.application.dao.entity.MlcAppRole" querySpace="application"
                registerShortName="true" tableName="mlc_app_role" updateTimeProp="updatedAt" updaterProp="updateBy"
                versionProp="version" i18n-en:displayName="App Role">
            <columns>
                <column code="role_id" displayName="角色 ID" domain="pkId" mandatory="true" name="roleId" precision="32"
                        primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Role Id" ui:show="X"/>
                <column code="app_id" displayName="应用 ID" domain="pkId" mandatory="true" name="appId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="App Id" ui:show="X"/>
                <column code="name" displayName="名称" mandatory="true" name="name" precision="20" propId="3"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Worksheet Id"/>
                <column code="role_type" comment="100:管理员 2:运营者  1:开发者 0:自定义" displayName="角色类型" mandatory="true"
                        name="roleType" propId="4" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="Role Type" ext:dict="com.mlc.base.common.enums.application.AppRoleTypeEnum"/>
                <column code="app_settings_enum" defaultValue="1" displayName="设置" mandatory="true"
                        name="appSettingsEnum" propId="5" stdDataType="int" stdSqlType="INTEGER"
                        i18n-en:displayName="App Settings Enum"/>
                <column code="notify" defaultValue="0" displayName="是否通知" mandatory="true" name="notify" propId="6"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Notify"/>
                <column code="is_debug" defaultValue="0" displayName="是否开启调试" mandatory="true" name="isDebug" propId="7"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="Is Debug"/>
                <column code="hide_app_for_members" defaultValue="0" displayName="隐藏应用" mandatory="true"
                        name="hideAppForMembers" propId="8" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="Hide App For Members"/>
                <column code="permission_way" displayName="权限定义方式" name="permissionWay" propId="9" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Permission Way"/>
                <column code="extend_attrs" displayName="操作标签" domain="csvListWithNull" name="extendAttrs"
                        precision="100" propId="10" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Extend Attrs"/>
                <column code="optional_controls" displayName="操作标签" domain="csvListWithNull" name="optionalControls"
                        precision="100" propId="11" stdDataType="string" stdDomain="csv-list-with-null"
                        stdSqlType="VARCHAR" i18n-en:displayName="Optional Controls"/>
                <column code="general_add" displayName="允许新增" name="generalAdd" propId="12" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="General Add"/>
                <column code="general_share" displayName="允许分享" name="generalShare" propId="13" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="General Share"/>
                <column code="general_import" displayName="允许导入" name="generalImport" propId="14" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="General Import"/>
                <column code="general_export" displayName="允许导出" name="generalExport" propId="15" stdDataType="boolean"
                        stdSqlType="BOOLEAN" i18n-en:displayName="General Export"/>
                <column code="general_discussion" displayName="允许讨论" name="generalDiscussion" propId="16"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="General Discussion"/>
                <column code="general_system_printing" displayName="允许打印" name="generalSystemPrinting" propId="17"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="General System printing"/>
                <column code="general_attachment_download" displayName="允许下载附件" name="generalAttachmentDownload"
                        propId="18" stdDataType="boolean" stdSqlType="BOOLEAN"
                        i18n-en:displayName="General Attachment Download"/>
                <column code="general_logging" displayName="允许查看日志" name="generalLogging" propId="19"
                        stdDataType="boolean" stdSqlType="BOOLEAN" i18n-en:displayName="General Logging"/>
                <column code="sort_index" displayName="排序" name="sortIndex" propId="20" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Sort Index"/>
                <column code="description" displayName="描述" name="description" precision="200" propId="21"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Description"/>
                <column code="status" defaultValue="1" displayName="状态" mandatory="true" name="status" propId="22"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="23"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="25" stdDataType="string" stdSqlType="VARCHAR" tagSet="sort"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="26" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="27" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="28" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="应用信息" name="appInfo" refDisplayName="关联应用角色"
                        refEntityName="com.mlc.application.dao.entity.MlcAppInfo" refPropName="appRoles"
                        tagSet="pub,ref-pub,ref-insertable,ref-updatable" i18n-en:displayName="App Info"
                        ref-i18n-en:displayName="App Roles">
                    <join>
                        <on leftProp="appId" rightProp="appId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-many cascadeDelete="true" displayName="应用成员映射" name="appMemberMappings"
                         refEntityName="com.mlc.application.dao.entity.MlcAppMemberRole" refPropName="appRole"
                         tagSet="pub,cascade-delete,insertable,updatable,connection"
                         i18n-en:displayName="App Member Mappings">
                    <join>
                        <on leftProp="roleId" rightProp="roleId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="资源映射" name="resourceMappings"
                         refEntityName="com.mlc.application.dao.entity.MlcAppRoleResource" refPropName="role"
                         tagSet="pub,cascade-delete,insertable,updatable" i18n-en:displayName="Resources">
                    <join>
                        <on leftProp="roleId" rightProp="roleId"/>
                    </join>
                </to-many>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppResource" createTimeProp="createdAt"
                createrProp="createBy" displayName="应用资源树表" name="com.mlc.application.dao.entity.MlcAppResource"
                querySpace="application" registerShortName="true" tableName="mlc_app_resource"
                updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version"
                i18n-en:displayName="App Resource">
            <columns>
                <column code="resource_id" displayName="角色工作表字段 ID" domain="pkId" mandatory="true" name="resourceId"
                        precision="32" primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="Role Sheet Field Id" ui:show="X"/>
                <column code="parent_id" displayName="父级ID" domain="pkId" name="parentId" precision="32" propId="2"
                        stdDataType="string" stdSqlType="VARCHAR" tagSet="parent" i18n-en:displayName="Field Id"/>
                <column code="children_id" displayName="子级ID" mandatory="true" name="childrenId" precision="50"
                        propId="3" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Role Sheet Id"/>
                <column code="resource_type" displayName="资源类型" mandatory="true" name="resourceType" propId="4"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Resource Type"/>
                <column code="permissions" displayName="权限标识集合" domain="json-1000" mandatory="true" name="permissions"
                        precision="1000" propId="5" stdDataType="string" stdDomain="json" stdSqlType="VARCHAR"
                        i18n-en:displayName="Permissions"/>
                <column code="status" defaultValue="1" displayName="状态" mandatory="true" name="status" propId="6"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Status"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="7"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="8" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="9" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="10" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="11" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <comment>对工作表、视图、实体字段等或者可继承资源的统一抽象</comment>
            <relations>
                <to-one displayName="父级" name="parent" refDisplayName="子级"
                        refEntityName="com.mlc.application.dao.entity.MlcAppResource" refPropName="childrenSet"
                        tagSet="pub,ref-pub,ref-cascade-delete" i18n-en:displayName="Parent"
                        ref-i18n-en:displayName="Children Set">
                    <join>
                        <on leftProp="parentId" rightProp="childrenId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-many cascadeDelete="true" displayName="子级" name="childrenSet"
                         refEntityName="com.mlc.application.dao.entity.MlcAppResource" refPropName="parent"
                         tagSet="pub,cascade-delete" i18n-en:displayName="Children Set">
                    <join>
                        <on leftProp="childrenId" rightProp="parentId"/>
                    </join>
                </to-many>
                <to-many cascadeDelete="true" displayName="角色映射" name="roleMappings"
                         refEntityName="com.mlc.application.dao.entity.MlcAppRoleResource" refPropName="resource"
                         tagSet="pub,cascade-delete,insertable,updatable" i18n-en:displayName="Roles">
                    <join>
                        <on leftProp="resourceId" rightProp="resourceId"/>
                    </join>
                </to-many>
            </relations>
        </entity>
        <entity className="com.mlc.application.dao.entity.MlcAppRoleResource" createTimeProp="createdAt"
                createrProp="createBy" displayName="应用角色资源表" name="com.mlc.application.dao.entity.MlcAppRoleResource"
                querySpace="application" registerShortName="true" tableName="mlc_app_role_resource"
                tagSet="no-web,many-to-many" updateTimeProp="updatedAt" updaterProp="updateBy" versionProp="version"
                i18n-en:displayName="App Role Resource">
            <columns>
                <column code="sid" displayName="主键ID" domain="pkId" mandatory="true" name="sid" precision="32"
                        primary="true" propId="1" stdDataType="string" stdSqlType="VARCHAR" tagSet="seq"
                        i18n-en:displayName="SID" ui:show="X"/>
                <column code="role_id" displayName="角色ID" domain="pkId" mandatory="true" name="roleId" precision="32"
                        propId="2" stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Role_id" ui:show="X"/>
                <column code="resource_id" displayName="资源ID" domain="pkId" mandatory="true" name="resourceId"
                        precision="32" propId="3" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Resource Id" ui:show="X"/>
                <column code="operation" displayName="权限操作类型" mandatory="true" name="operation" propId="4"
                        stdDataType="string" stdSqlType="VARCHAR" i18n-en:displayName="Operation"
                        ext:dict="com.mlc.base.common.enums.application.AppPermissionOperationEnum"/>
                <column code="delta_permissions" displayName="变更的权限标签" domain="json-1000" mandatory="true"
                        name="deltaPermissions" precision="1000" propId="5" stdDataType="string" stdDomain="json"
                        stdSqlType="VARCHAR" i18n-en:displayName="Delta Permissions"/>
                <column code="priority" displayName="冲突时优先级" name="priority" propId="6" stdDataType="int"
                        stdSqlType="INTEGER" i18n-en:displayName="Priority"/>
                <column code="version" displayName="数据版本" domain="version" mandatory="true" name="version" propId="7"
                        stdDataType="int" stdSqlType="INTEGER" i18n-en:displayName="Version" ui:show="X"/>
                <column code="create_by" displayName="创建人" domain="createdBy" mandatory="true" name="createBy"
                        precision="50" propId="8" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Create By" ui:show="L"/>
                <column code="created_at" displayName="创建时间" domain="createTime" mandatory="true" name="createdAt"
                        propId="9" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Created At"
                        ui:show="L"/>
                <column code="update_by" displayName="修改人" domain="updatedBy" mandatory="true" name="updateBy"
                        precision="50" propId="10" stdDataType="string" stdSqlType="VARCHAR"
                        i18n-en:displayName="Update By" ui:show="L"/>
                <column code="updated_at" displayName="修改时间" domain="updateTime" mandatory="true" name="updatedAt"
                        propId="11" stdDataType="timestamp" stdSqlType="TIMESTAMP" i18n-en:displayName="Updated At"
                        ui:show="L"/>
            </columns>
            <relations>
                <to-one displayName="角色" name="role" refDisplayName="资源映射"
                        refEntityName="com.mlc.application.dao.entity.MlcAppRole" refPropName="resourceMappings"
                        tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable" i18n-en:displayName="Role"
                        ref-i18n-en:displayName="Resources">
                    <join>
                        <on leftProp="roleId" rightProp="roleId"/>
                    </join>
                    <ref-set/>
                </to-one>
                <to-one displayName="资源" name="resource" refDisplayName="角色映射"
                        refEntityName="com.mlc.application.dao.entity.MlcAppResource" refPropName="roleMappings"
                        tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable"
                        i18n-en:displayName="Resource" ref-i18n-en:displayName="Roles">
                    <join>
                        <on leftProp="resourceId" rightProp="resourceId"/>
                    </join>
                    <ref-set/>
                </to-one>
            </relations>
        </entity>
    </entities>
</orm>