/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.api.beans;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.base.common.beans.filter.IFilterConvertible;
import com.mlc.base.common.beans.sort.FrontSortBean;
import com.mlc.base.core.worksheetControl.base.BasicControl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;


/**
 * 工作表视图数据Bean
 */
@Getter
@Setter
public class SheetTierDataBean {

    private String worksheetId;

    // 表单中配置的所有的控件
    private List<BasicControl> worksheetControls;

    // 从树结构中获取 工作表/视图/控件:ID -> 资源ID 的映射
    private Map<String, String> castToResourceIdMap;

    // 视图层级列表
    private List<ViewTierDataBean> viewTierList;

    public SheetTierDataBean(String worksheetId, List<BasicControl> worksheetControls, Map<String, String> castToResourceIdMap) {
        this.worksheetId = worksheetId;
        this.worksheetControls =  worksheetControls; //Collections.unmodifiableList(worksheetControls);
        this.castToResourceIdMap = castToResourceIdMap; //Collections.unmodifiableMap(castToResourceIdMap);
    }

    public void addViewTier(ViewTierDataBean viewTierDataBean) {
        if (viewTierList == null) {
            viewTierList = new ArrayList<>();
        }
        viewTierList.add(viewTierDataBean);
    }

    @Getter
    @Setter
    public static class ViewTierDataBean {
        // 视图ID
        private String viewId;

        // 视图类型
        private Integer viewType;

        // 看板等分组控件ID
        private String viewControl;

        // 初始过滤
        private String filters;

        // 快速过滤
        private String fastFilters;

        // 导航分组过滤
        private String navGroup;

        // 排序
        private String moreSort;


        // 当前视图下需要对用户隐藏的控件
        private Set<String> hiddenControls;

        // 当前视图下<排除需要对用户隐藏的控件>，其余的需要显示的控件
        private Set<String> showControls;

        /**
         * 获取排序条件列表
         */
        public List<FrontSortBean> getMoreSorts() {
            try {
                return new ObjectMapper().readValue(this.moreSort, new TypeReference<>() {});
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

        /**
         * 获取默认筛选条件列表
         */
        public List<IFilterConvertible> convertDefaultFilters() {
            return IFilterConvertible.deserializeFilterList(this.filters);
        }

        /**
         * 获取快速筛选条件列表
         */
        public List<IFilterConvertible> convertFastFilters() {
            return IFilterConvertible.deserializeFilterList(this.fastFilters);
        }

        /**
         * 获取导航分组筛选条件列表
         */
        public List<IFilterConvertible> convertNavGroupFilter() {
            return IFilterConvertible.deserializeFilterList(this.navGroup);
        }
    }
}
