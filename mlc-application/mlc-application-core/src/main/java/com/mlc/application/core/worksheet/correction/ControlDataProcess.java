/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.worksheet.correction;

import static com.mlc.base.common.exception.errors.CommonErrors.ERR_COMMON_BIZ_DATA_FORMAT_CONVERT;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.mlc.base.common.enums.meta.ControlTypeEnum;
import com.mlc.base.core.MlcBaseCoreConstants;
import io.nop.api.core.exceptions.NopException;
import io.nop.biz.api.IBizObject;
import io.nop.biz.api.IBizObjectManager;
import io.nop.biz.crud.BizSchemaHelper;
import io.nop.orm.OrmConstants;
import io.nop.xlang.xmeta.IObjPropMeta;
import io.nop.xlang.xmeta.ISchema;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 控件数据处理器
 */
@Slf4j
public class ControlDataProcess {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final IBizObjectManager bizObjectManager;

    public ControlDataProcess(IBizObjectManager bizObjectManager) {
        this.bizObjectManager = bizObjectManager;
    }

    /**
     * 处理全量更新
     * 主子表一起提交，子表外键自动关联主表的主键
     * 全量更新，提交整个对象树的数据
     */
    public Map<String, Object> fullReorgan(String modelObjectEntityName, List<Map<String, Object>> items) {

        IBizObject bizObject = bizObjectManager.getBizObject(modelObjectEntityName);
        return this.fullNestingHandler(modelObjectEntityName,
                                      bizObject.getObjMeta().getRootSchema(), null, items);
    }

    /**
     * 处理差量更新 子表单独提交，分别处理新增、修改、删除 对子表的差量更新，提交到单独的对象上，而非操作整个对象
     */
    public DifferentialUpdate differenceReorgan(String modelObjectEntityName, String mainRowId, List<Map<String, Object>> items) {
        IBizObject bizObject = bizObjectManager.getBizObject(modelObjectEntityName);

        DifferentialUpdate differentialUpdate = new DifferentialUpdate();
        Map<String, Object> results = this.diffNestingHandler(
                                         mainRowId, modelObjectEntityName, bizObject.getObjMeta().getRootSchema(),
                                null, items, differentialUpdate
        );

        if (!results.isEmpty()){
            results.put(OrmConstants.PROP_ID, mainRowId);
            // 如果 objectEntityName 重复，是因为一对一关系要更新的字段为主表字段，而 mainMap 也是主表字段，所以需要合并 ？？？
            differentialUpdate.merge(modelObjectEntityName, results);
        }
        return differentialUpdate;
    }

    private Map<String, Object> fullNestingHandler(String baseBizObjName, ISchema schema, String propName,
                                                List<Map<String, Object>> itemData) {

        Map<String, Object> result = new LinkedHashMap<>();

        itemData.forEach(mapItem -> {
            ProcessedControl processedControl = new ProcessedControl(mapItem);
            IObjPropMeta propMeta = schema.getProp(processedControl.key);
            if (propMeta == null) {
                // 可能是一些控件的数据，不是字段元信息，这里暂时不处理
                log.warn("字段元数据为空, 字段名称: {}", processedControl.key);
                return;
            }
            String subPropName = propName == null ? propMeta.getName() : propName + '.' + propMeta.getName();

            // 如果是子表，递归处理，对容器内的数据进行处理
            if (Objects.equals(processedControl.controlType, ControlTypeEnum.SUB_LIST.getValue())){
                ISchema propSchema = BizSchemaHelper.getPropSchema(propMeta, true, bizObjectManager, baseBizObjName);
                List<List<Map<String, Object>>> subList = SubListProcess.parseRows(processedControl.value);
                List<Object> converted = new ArrayList<>(subList.size());
                subList.forEach(subItem -> {
                    Map<String, Object> subRet =
                        this.fullNestingHandler(propSchema.getBizObjName(), propSchema, subPropName, subItem);
                    converted.add(subRet);
                });
                processedControl.value = converted;
            } else if (Objects.equals(processedControl.controlType, ControlTypeEnum.RELATE_SHEET.getValue())){
                processedControl.value = RelateSheetProcess.processFull(mapItem, propMeta);
            } else {
                // 格式化普通数据
                processedControl.value = processFormatData(mapItem, processedControl.controlType);
            }
            result.put(processedControl.key, processedControl.value);
        });
        return result;
    }


    private Map<String, Object> diffNestingHandler(String mainRowId, String baseBizObjName, ISchema schema, String propName,
        List<Map<String, Object>> itemData, DifferentialUpdate differentialUpdate) {

        Map<String, Object> result = new LinkedHashMap<>();

        itemData.forEach(mapItem -> {
            ProcessedControl processedControl = new ProcessedControl(mapItem);
            IObjPropMeta propMeta = schema.getProp(processedControl.key);
            String subPropName = propName == null ? propMeta.getName() : propName + '.' + propMeta.getName();

            // 如果是子表，递归处理，对容器内的数据进行处理
            if (Objects.equals(processedControl.controlType, ControlTypeEnum.SUB_LIST.getValue())){
                ISchema propSchema = BizSchemaHelper.getPropSchema(propMeta, true, bizObjectManager, baseBizObjName);

                List<Map<String, Object>> rows = SubListProcess.parseRows(processedControl.value);
                SubListProcess.processDifferentialUpdates(
                      mainRowId, rows, propMeta, differentialUpdate,
                      (subItem, rowId) ->
                         this.diffNestingHandler(rowId, baseBizObjName, propSchema, subPropName, subItem, differentialUpdate)
                );
                return;
            } else if (Objects.equals(processedControl.controlType, ControlTypeEnum.RELATE_SHEET.getValue())){
                List<Map<String, Object>> maps =
                    RelateSheetProcess.processDiff(mainRowId, mapItem, schema, propMeta, differentialUpdate);
                if(maps != null) {
                    processedControl.value = maps;
                } else {
                    return;
                }
            } else {
                processedControl.value = processFormatData(mapItem, processedControl.controlType);
            }
            result.put(processedControl.key, processedControl.value);
        });
        return result;
    }

    @Getter
    public static class DifferentialUpdate {
        private final Map<String, List<Map<String, Object>>> save = new LinkedHashMap<>();
        private final Map<String, List<Map<String, Object>>> update = new LinkedHashMap<>();
        private final Map<String, List<Map<String, Object>>> delete = new LinkedHashMap<>();

        public void merge(String bizObjName, Map<String, Object> other) {
            update.computeIfAbsent(bizObjName, key -> new ArrayList<>()).add(other);
        }

        public List<Map<String, Object>> getSaveList(String bizObjName) {
            return this.save.computeIfAbsent(bizObjName, key -> new ArrayList<>());
        }

        public List<Map<String, Object>> getUpdateList(String bizObjName) {
            return this.update.computeIfAbsent(bizObjName, key -> new ArrayList<>());
        }

        public List<Map<String, Object>> getDeleteList(String bizObjName) {
            return this.delete.computeIfAbsent(bizObjName, key -> new ArrayList<>());
        }
    }

    @Data
    private static class ProcessedControl {
        String key;
        Object value;
        Integer controlType;

        ProcessedControl(Map<String, Object> mapItem) {
            this.key = MlcBaseCoreConstants.RELATION_NAME_REPLACE_PATTERN
                        .matcher(mapItem.get("controlId").toString()).replaceAll("");
            this.value = mapItem.get("value");
            this.controlType = (Integer)mapItem.get("type");
        }
    }

    public Object processFormatData(Map<String, Object> item, Integer controlType) {
        Object value = item.get("value");
        if (value == null || Strings.isNullOrEmpty(value.toString())) {
            return null;
        }
        try {
            if (Objects.equals(controlType, ControlTypeEnum.USER_PICKER.getValue())) {
                List<Map<String, String>> userPickerList = objectMapper.readValue(value.toString(), new TypeReference<>() {});
                List<String> list = userPickerList.stream()
                                              .map(user -> user.get("accountId"))
                                              .collect(Collectors.toList());
                // 存储 ["1", "2", "3"] 这种格式，和多选等控件的数据格式一致
                value = objectMapper.writeValueAsString(list);
                return value;
            } else if (Objects.equals(controlType, ControlTypeEnum.DEPARTMENT.getValue())) {
                List<Map<String, String>> deptList = objectMapper.readValue(value.toString(), new TypeReference<>() {});
                List<String> list = deptList.stream()
                                              .map(user -> user.get("departmentId"))
                                              .collect(Collectors.toList());
                // 存储 ["1", "2", "3"] 这种格式，和多选等控件的数据格式一致
                value = objectMapper.writeValueAsString(list);
                return value;
            }
            return value;
        } catch (Exception e) {
            throw new NopException(ERR_COMMON_BIZ_DATA_FORMAT_CONVERT, e);
        }
    }
}
