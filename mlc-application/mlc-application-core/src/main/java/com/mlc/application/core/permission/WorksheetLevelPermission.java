/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.permission;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作表级别数据范围权限
 */
@Getter
@AllArgsConstructor
public enum WorksheetLevelPermission implements IPermissionPoint {

    // 数据范围  readLevel:key  =  {roleid1: 20，roleid2: 30，roleid3：100}
    READ_LEVEL("readLevel", 20),
    EDIT_LEVEL("editLevel", 20),
    REMOVE_LEVEL("removeLevel", 20);

    private final String key;
    private final Object defaultValue;

    private static final Map<String, WorksheetLevelPermission> KEY_BODY_MAP = Arrays.stream(values())
                 .collect(Collectors.toMap(WorksheetLevelPermission::getKey, e -> e));

    public static WorksheetLevelPermission getBodyByKey(String key) {
        return KEY_BODY_MAP.get(key);
    }

    private static final Map<String, Object> KEY_DEFAULT_VALUE_MAP = Arrays.stream(values())
                 .collect(Collectors.toMap(WorksheetLevelPermission::getKey, WorksheetLevelPermission::getDefaultValue));

    public static Map<String, Object> getDefaultPermissions() {
        return Collections.unmodifiableMap(KEY_DEFAULT_VALUE_MAP);
    }
}