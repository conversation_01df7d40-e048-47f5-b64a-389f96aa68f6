/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按"原样"提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.utils;

import io.nop.api.core.util.MultiCsvSet;
import io.nop.commons.collections.KeyedList;
import io.nop.xlang.xmeta.impl.ObjPropAuthModel;

import java.util.Arrays;
import java.util.Locale;
import java.util.Set;

import com.mlc.application.core.permission.WorksheetViewPermission;
import com.mlc.application.core.permission.WorksheetFieldPermission;
import com.mlc.application.core.permission.WorksheetLevelPermission;
import com.mlc.application.core.permission.IPermissionPoint;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 操作权限转换器
 * 负责将GraphQL操作转换为对应的权限集合
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MetaPermissionExtractor {

    /**
     * 权限类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PermissionCategory {
        VIEW("view"),
        FIELD("field"),
        LEVEL("level");
        
        private final String category;
    }

    /**
     * 操作前缀分类
     */
    @Getter
    @AllArgsConstructor
    public enum ActionPrefix {
        // 读操作前缀（默认）
        READ(Set.of("get", "find", "query", "list", "search"), WorksheetViewPermission.CAN_READ, WorksheetFieldPermission.NOT_READ, WorksheetLevelPermission.READ_LEVEL),

        // 写操作前缀
        WRITE(Set.of("add", "save", "create", "update"), WorksheetViewPermission.CAN_EDIT, WorksheetFieldPermission.NOT_EDIT, WorksheetLevelPermission.EDIT_LEVEL),

        // 删除操作前缀
        DELETE(Set.of("delete", "remove"), WorksheetViewPermission.CAN_REMOVE, null, WorksheetLevelPermission.REMOVE_LEVEL);

        private final Set<String> prefixes;
        private final WorksheetViewPermission viewPermission;
        private final WorksheetFieldPermission fieldPermission;
        private final WorksheetLevelPermission levelPermission;

        /**
         * 根据操作名称判断属于哪种操作类型
         */
        public static ActionPrefix getActionPrefixByName(String actionName) {
            return Arrays.stream(values())
                    .filter(prefix -> prefix.prefixes.stream().anyMatch(actionName.toLowerCase(Locale.ROOT)::startsWith))
                    .findFirst()
                    .orElse(READ);
        }
        
    }


    /**
     * 根据权限点获取权限
     */
    static MultiCsvSet getPermissionByPoint(IPermissionPoint permissionPoint, KeyedList<ObjPropAuthModel> auths) {
        return auths.stream()
                    .filter(auth -> permissionPoint.getKey().equals(auth.getFor())) // 过滤出指定类型的权限
                    .findFirst()
                    .map(ObjPropAuthModel::getPermissions)
                    .orElse(null);
    }
    
    /**
     * 获取指定权限类别对应的权限
     */
    static MultiCsvSet getPermissionByCategory(PermissionCategory category, String actionName, KeyedList<ObjPropAuthModel> auths) {
        ActionPrefix actionPrefix = ActionPrefix.getActionPrefixByName(actionName);
        
        IPermissionPoint permissionPoint = null;
        switch (category) {
            case VIEW:
                permissionPoint = actionPrefix.getViewPermission();
                break;
            case FIELD:
                permissionPoint = actionPrefix.getFieldPermission();
                break;
            case LEVEL:
                permissionPoint = actionPrefix.getLevelPermission();
                break;
        }
        
        if (permissionPoint == null) {
            log.warn("未找到操作 {} 对应的 {} 类型权限", actionName, category.getCategory());
            return null;
        }
        
        return getPermissionByPoint(permissionPoint, auths);
    }


    /**
     * 获取特定类别的操作权限
     *
     * @param category 权限类别
     * @param actionName 操作名称
     * @param auths 权限模型列表
     * @return 权限集合
     */
    public static MultiCsvSet transformPermission(PermissionCategory category, String actionName, KeyedList<ObjPropAuthModel> auths) {
        if (auths == null || auths.isEmpty()) {
            log.warn("权限模型列表为空，无法转换权限");
            return null;
        }

        if (actionName == null || actionName.isEmpty()) {
            log.warn("操作名称为空，无法转换权限");
            return null;
        }

        return getPermissionByCategory(category, actionName, auths);
    }
}
