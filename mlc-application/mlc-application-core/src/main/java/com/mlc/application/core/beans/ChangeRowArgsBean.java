/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.beans;

import com.google.common.base.Strings;
import com.mlc.application.core.MlcApplicationErrors;
import com.mlc.base.core.worksheetControl.base.BasicControl;
import io.nop.api.core.annotations.meta.PropMeta;
import io.nop.api.core.exceptions.NopException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

@Getter
@Setter
public class ChangeRowArgsBean {

    // 行ID，也是主键
    private String rowId;

    // 多行ID
    private List<String> rowIds;

    private Integer deleteType;

    @PropMeta(description = "应用Id", mandatory = true)
    private String appId;

    @PropMeta(description = "工作表id", mandatory = true)
    private String worksheetId;

    @PropMeta(description = "视图Id", mandatory = true)
    private String viewId;

    @PropMeta(description = "模型对象id", mandatory = true)
    private String modelObjectEntityId;

    private String modelObjectEntityName;

    private String projectID;

    // 该行所有的cell
    private List<Map<String, Object>> receiveControls;

    // 批量新增所有rows
    private List<BasicControl> receiveRows;

    // 自定义按钮ID
    private String btnId;

    // 按钮备注
    private String btnRemark;

    // 点击按钮对应的工作表ID
    private String btnWorksheetId;

    // 点击按钮对应的行记录ID
    private String btnRowId;

    private Boolean hasFilters;

    // 主记录ID,编辑时用的
    private Object masterRecord;

    // 1：正常 21：草稿箱
    // @PropMeta(description = "行状态", mandatory = true)
    private Integer rowStatus;

    // 草稿ID
    private String draftRowId;

    /**
     * 新旧控件数据，value 是新控件的值
     */
    private List<Map<String, Object>> newOldControl;

    // 好像是用来标识分享类型的，但是好像是废弃的
    @Deprecated
    private Integer getType;

    // -- 验证码相关 -- //
    private String ticket;

    private String randStr;

    // 验证码类型（默认腾讯云）
    private Object captchaType;

    // 验证码【根据配置来校验是否必填】
    private String verifyCode;
    // -- 验证码相关 -- //

    // 推送ID
    private String pushUniqueId;

    // 未登录用户临时登录凭据
    private String clientId;

    public Set<String> getRowIds() {
        if (CollectionUtils.isEmpty(rowIds) || rowIds.stream().anyMatch(Strings::isNullOrEmpty)) {
            throw new NopException(MlcApplicationErrors.ERR_APPLICATION_QUERY_DATA);
        }
        Set<String> collect = rowIds.stream().collect(Collectors.toSet());
        return Collections.unmodifiableSet(collect);
    }

    /**
     * 获取主键
     */
    public String getId() {
        if(Strings.isNullOrEmpty(rowId)) {
            throw new NopException(MlcApplicationErrors.ERR_APPLICATION_QUERY_DATA);
        }
        return rowId;
    }
}
