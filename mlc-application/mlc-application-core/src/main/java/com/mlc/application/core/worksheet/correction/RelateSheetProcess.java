/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.worksheet.correction;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.mlc.application.core.worksheet.correction.ControlDataProcess.DifferentialUpdate;
import com.mlc.base.common.enums.meta.DataEditTypeEnum;
import io.nop.api.core.ioc.BeanContainer;
import io.nop.biz.BizConstants;
import io.nop.orm.IOrmTemplate;
import io.nop.orm.OrmConstants;
import io.nop.orm.model.IEntityModel;
import io.nop.orm.support.OrmMappingTableMeta;
import io.nop.xlang.xmeta.IObjPropMeta;
import io.nop.xlang.xmeta.ISchema;
import io.nop.xlang.xmeta.ObjPropKind;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class RelateSheetProcess {

    public static Object processFull(Map<String, Object> item, IObjPropMeta metaObjProp) {
        boolean isToMany = getExtKind(metaObjProp);
        if (isToMany) {
            return processToMany(item, metaObjProp);
        } else {
            return processToOne(item);
        }
    }

    /**
     * 一对多关系的全量更新
     *
     * @return 组装成 [{id->行ID: 1},{id->行ID: 2}] 的形式，交给模型处理，模型自动分析为关联表添加外键
     */
    private static List<Map<String, Object>> processToMany(Map<String, Object> item, IObjPropMeta metaObjProp) {

        List<Map<String, Object>> dataList = parseJsonList(item.get("value"));
        return dataList.stream().map(dataMap -> {
            Map<String, Object> resMap = new HashMap<>();

            Object manyToManyRefProp = metaObjProp.prop_get("orm:manyToManyRefProp");
            if (manyToManyRefProp != null) { // 如果是多对多关系，需要把数据放到另一层的关联键上
                resMap.put(manyToManyRefProp.toString(), dataMap.get("sid"));
            } else {
                resMap.put(OrmConstants.PROP_ID, dataMap.get("sid"));
            }
            return resMap;
        }).collect(Collectors.toList());
    }

    /**
     * 一对一关系的全量更新
     *
     * @return 返回要更新的字段值
     */
    private static String processToOne(Map<String, Object> item) {
        return parseJsonList(item.get("value")).stream()
                                               .map(map -> map.get("sid").toString())
                                               .findFirst()
                                               .orElseThrow(() -> new RuntimeException("更新的数据为空"));
    }

    /**
     * 差量更新
     */
    public static List<Map<String, Object>> processDiff(String mainRowId, Map<String, Object> item,
                                  ISchema schema, IObjPropMeta metaObjProp, DifferentialUpdate differentialUpdate) {

        boolean toMany = getExtKind(metaObjProp);
        String joinPropStr = (String) metaObjProp.prop_get(toMany ? "ext:joinRightProp" : "ext:joinLeftProp");

        // 如果表格行ID为空，说明是新增的数据，需要处理成 processToMany 的形式交给模型处理
        if (Strings.isNullOrEmpty(mainRowId)){
            return processToMany(item, metaObjProp);
        }

        // 不要改成 Map.of()，它是不可变结构，因为后面还要修改值
        if (toMany) {
            differenceToMany(item, joinPropStr, mainRowId, metaObjProp, differentialUpdate);
        } else {
            // 对一使用的是主表对象
            List<Map<String, Object>> objects =
                differentialUpdate.getUpdate().computeIfAbsent(schema.getBizObjName(), key -> new ArrayList<>());
            objects.add(differenceToOne(item, joinPropStr, mainRowId));
        }
        return null;
    }


    private static boolean getExtKind(IObjPropMeta metaObjProp) {
        Object extKind = metaObjProp.prop_get(BizConstants.EXT_KIND);
        if (extKind == null) {
            throw new RuntimeException("ext:kind is null");
        }
        ObjPropKind kind = ObjPropKind.fromText(extKind.toString());
        return kind == ObjPropKind.TO_MANY;
    }

    /**
     * 处理一对多关系的差量更新
     *
     * @param item 要更新的数据
     * @param columnName 要更新列的属性名
     * @param columnValue 要更新的列值
     */
    private static void differenceToMany(Map<String, Object> item, String columnName, String columnValue,
        IObjPropMeta metaObjProp, DifferentialUpdate differentialUpdate) {

        String itemBizObjName = metaObjProp.getItemBizObjName();
        List<Map<String, Object>> saveList = differentialUpdate.getSaveList(itemBizObjName);
        List<Map<String, Object>> updateList = differentialUpdate.getUpdateList(itemBizObjName);
        List<Map<String, Object>> deleteList = differentialUpdate.getDeleteList(itemBizObjName);

        IEntityModel forEtityModel = BeanContainer.getBeanByType(IOrmTemplate.class).getOrmModel()
                                                  .requireEntityModel(itemBizObjName);
        boolean isManyToMany = OrmMappingTableMeta.isMappingTable(forEtityModel);

        List<Map<String, Object>> list = parseJsonList(item.get("value"));

        for (Map<String, Object> map : list) {
            Map<String, Object> optionMap = new HashMap<>();

            Object subRowId = getRealRowId(map);
            optionMap.put(OrmConstants.PROP_ID, subRowId);

            Integer editType = (Integer) map.get("editType");

            // 非中间表的关联只有更新操作
            if (isManyToMany) {
                optionMap.put(columnName, columnValue);
                (editType != null && editType == DataEditTypeEnum.DELETE.getCode() ? deleteList : saveList).add(optionMap);
            } else {
                optionMap.put(columnName, editType == DataEditTypeEnum.DELETE.getCode() ? null : columnValue);
                updateList.add(optionMap);
            }
        }
    }

    /**
     * 处理一对一关系的差量更新
     *
     * @param item 要更新的数据
     * @param columnName 要更新列的属性名
     * @param rowId 要更新的行id
     */
    private static Map<String, Object> differenceToOne(Map<String, Object> item, String columnName, String rowId) {
        Map<String, Object> optionMap = new HashMap<>();
        try {
            List<Map<String, Object>> list =
                new ObjectMapper().readValue(item.get("value").toString(), new TypeReference<>() {});
            if (list.isEmpty()) {
                optionMap.put(columnName, null);
            } else {
                for (Map<String, Object> map : list) {

                    Integer editType = (Integer)map.get("editType");
                    if (editType == null || editType == DataEditTypeEnum.ADD.getCode()) {
                        String columnValue = getRealRowId(map).toString();
                        if (columnValue != null) {
                            optionMap.put(columnName, columnValue);
                        }
                    } else if (editType == DataEditTypeEnum.DELETE.getCode()) {
                        optionMap.put(columnName, null);
                    }
                }
            }
            optionMap.put(OrmConstants.PROP_ID, rowId);
        } catch (Exception e) {
            throw new RuntimeException("解析数据异常", e);
        }
        return optionMap;
    }

    private static List<Map<String, Object>> parseJsonList(Object jsonString) {
        try {
            return new ObjectMapper().readValue(jsonString.toString(), new TypeReference<>() {});
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static Object getRealRowId(Map<String, Object> map) {
        Object subRowId = map.getOrDefault("rowid", map.get("sid"));
        if (subRowId == null) {
            throw new RuntimeException("关联记录存在数据异常");
        }
        return subRowId;
    }
}
