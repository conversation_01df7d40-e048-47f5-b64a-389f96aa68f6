/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */



/**
 * 计划从控件根上开始构造 DDD
 * 要迁移的包括：com.mlc.application.core.worksheet.correction.ControlDataHandler 控件的数据转换规则（防腐层？）
 *             com.mlc.base.common.enums.meta.ControlTypeEnum 放到具体的领域中(域)
 *             控件 domain, 以及控件的属性 domain
 *             控件对应的关联关系名称生成方式配置（提供一个业务适配器？）
 *             控件的属性的校验规则
 *
 */
package com.mlc.application.core.worksheet;
