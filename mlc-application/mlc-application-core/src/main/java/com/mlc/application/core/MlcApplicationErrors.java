/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core;

import static io.nop.api.core.exceptions.ErrorCode.define;

import io.nop.api.core.exceptions.ErrorCode;

public interface MlcApplicationErrors {

    ErrorCode ERR_APPLICATION_QUERY_DATA = define("mlc.err.application.query-data", "数据查询异常");

    ErrorCode ERR_WORKSHEET_CONTROL_NOT_FOUND = define("mlc.err.worksheet.control.not-found", "工作表控件未找到");

    ErrorCode ERR_RELATE_MODEL_MAPPING = define("mlc.err.control-model-mapping", "控件模型对象关系映射异常");
}
