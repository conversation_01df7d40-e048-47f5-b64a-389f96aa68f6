/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.worksheet.correction;

import static com.mlc.base.common.exception.errors.CommonErrors.ERR_COMMON_WORKSHEET_CONTROL_CONVERT;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.mlc.application.core.worksheet.correction.ControlDataProcess.DifferentialUpdate;
import com.mlc.base.common.enums.meta.DataEditTypeEnum;
import io.nop.api.core.exceptions.NopException;
import io.nop.orm.OrmConstants;
import io.nop.xlang.xmeta.IObjPropMeta;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SubListProcess {

    private final ObjectMapper objectMapper = new ObjectMapper();

    public static <R> R parseRows(Object value) {
        try {
            return new ObjectMapper().readValue(value.toString(), new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            log.error("子表控件数据处理异常，当前数据：{}", value, e);
            throw new NopException(ERR_COMMON_WORKSHEET_CONTROL_CONVERT, e);
        }
    }

    public static void processDifferentialUpdates(String mainRowId, List<Map<String, Object>> rows,
        IObjPropMeta objPropMeta, DifferentialUpdate differentialUpdate, BiFunction<List<Map<String, Object>>, String, Map<String, Object>> nestedHandler) {

        String itemBizObjName = objPropMeta.getItemBizObjName();
        List<Map<String, Object>> saveList = differentialUpdate.getSaveList(itemBizObjName);
        List<Map<String, Object>> updateList = differentialUpdate.getUpdateList(itemBizObjName);
        List<Map<String, Object>> deleteList = differentialUpdate.getDeleteList(itemBizObjName);

        rows.forEach(row -> {
            Integer editType = (Integer) row.get("editType");
            String tableRowId = row.get("rowid") == null ? null : row.get("rowid").toString();
            if (DataEditTypeEnum.EDIT.getCode() == editType) {

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> bodyList = (List<Map<String, Object>>) row.get("newOldControl");
                // tableRowId 把当前表格循环的行ID传入, 让关联表格内的数据能够关联到当前行
                Map<String, Object> collect = nestedHandler.apply(bodyList, tableRowId);

                if (Strings.isNullOrEmpty(tableRowId)) {
                    // rightProp 为子表外键字段, 将主表的主键设置为它的字段值, 以关联主子表
                    collect.put(objPropMeta.prop_get("ext:joinRightProp").toString(), mainRowId);
                    saveList.add(collect);
                } else {
                    if (!collect.isEmpty()) {
                        collect.put(OrmConstants.PROP_ID, tableRowId);
                        updateList.add(collect);
                    }
                }
            } else {
                deleteList.add(Map.of(OrmConstants.PROP_ID, tableRowId));
            }
        });
    }
}
