/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.permission;

import com.mlc.base.common.enums.application.AppDataScopeLevelEnum;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;

/**
 * 每个权限的枚举, 用于获取默认权限
 * 也可以通过数据库获取，这里只是简单的获取默认权限
 */
public class ResourcePermissionByEnum implements IResourcePermission {

    @Override
    public Map<String, Object> getDefaultWorksheetPermissions() {
        Map<String, Object> defaultPermissions = WorksheetPermission.getDefaultPermissions();
        defaultPermissions.putAll(WorksheetLevelPermission.getDefaultPermissions());
        return defaultPermissions;
    }

    @Override
    public Map<String, Object> getDefaultWorksheetViewPermissions() {
        return WorksheetViewPermission.getDefaultPermissions();
    }

    @Override
    public Map<String, Object> getDefaultWorksheetFieldPermissions() {
        return WorksheetFieldPermission.getDefaultPermissions();
    }

    @Override
    public Map<String, Object> getInitialWorksheetPermissions() {
        Map<String, Object> identityHashMap = new IdentityHashMap<>(WorksheetPermission.getDefaultPermissions());

        List<Integer> levelValues = AppDataScopeLevelEnum.allValues();
        // 为 defaultPermissions 里面的每个权限添加 levelValues 里面的值
        WorksheetLevelPermission.getDefaultPermissions().forEach((key, value) -> {
            for (Integer newV : levelValues) {
                String newKey = new String(key); // IdentityHashMap 会根据 key 的引用地址来判断是否相等
                identityHashMap.put(newKey, newV);
            }
        });
        return identityHashMap;
    }

}
