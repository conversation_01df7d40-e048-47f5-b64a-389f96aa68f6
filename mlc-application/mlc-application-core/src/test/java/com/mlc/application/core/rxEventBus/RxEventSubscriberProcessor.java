/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.rxEventBus;

import java.lang.reflect.Method;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RxEventSubscriberProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 获取 Bean 的所有方法
        Method[] methods = bean.getClass().getDeclaredMethods();

        // 查找带有 @RxEventSubscriber 注解的方法
        Arrays.stream(methods)
              .filter(method -> method.isAnnotationPresent(RxEventSubscriber.class))
              .forEach(method -> subscribeToEvent(bean, method));

        return bean;
    }

    private void subscribeToEvent(Object bean, Method method) {

        RxEventSubscriber annotation = method.getAnnotation(RxEventSubscriber.class);
        // 遍历 `classes` 属性中的每个事件类型
        for (Class<?> eventType : annotation.classes()) {
            // 订阅事件，将方法作为 lambda 注册到 RxEventBus 的 Observable 中
            RxEventBusEnum.INSTANCE.getObservable()
               .filter(event -> eventType.isAssignableFrom(event.getClass())) // 过滤符合 `classes` 的事件
               .subscribe(event -> {
                   try {
                       method.invoke(bean, event);
                   } catch (Exception e) {
                       log.error("Error invoking method: {}", method.getName(), e);
                   }
               });
        }
    }
}