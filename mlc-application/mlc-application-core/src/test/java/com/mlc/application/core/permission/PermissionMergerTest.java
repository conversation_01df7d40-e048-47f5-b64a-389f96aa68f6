/*
 * 版权所有 © 晓与行信息技术(天津)有限公司
 *
 * 联系方式：
 * 手机：18500235210
 * 邮箱：<EMAIL>
 *
 * 授权声明：
 * 本软件及其源代码的版权归晓与行信息技术(天津)有限公司所有。
 * 未经本公司明确书面授权，任何个人或组织不得对本软件进行复制、修改、分发、出售、租赁或以其他任何形式进行商业利用。
 * 禁止将本软件或其任何部分用于非法目的或违反法律法规的活动。
 *
 * 本软件按“原样”提供，不附带任何明示或暗示的保证，包括但不限于对适销性、特定用途的适用性和不侵权性的保证。
 * 在任何情况下，晓与行信息技术(天津)有限公司或其贡献者均不对因使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害承担责任。
 *
 * 请尊重版权，合法使用本软件。
 */

package com.mlc.application.core.permission;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;


/**
 * 计算用户最大权限
 * 此版是以字段为最小权限单位的，每个视图权限下面都嵌套着字段权限
 * 但是此项目中视图权限和字段权限是并行的，字段权限也是挂在工作表下面的，所以此版不适用
 */
@Slf4j
public class PermissionMergerTest {

    // 字段权限类
    private class FieldPermission {
        private final String fieldId;
        private Boolean notRead;
        private Boolean notEdit;
        private Boolean notAdd;
        private Boolean isDecrypt;

        FieldPermission(String fieldId, Boolean notRead, Boolean notEdit, Boolean notAdd, Boolean isDecrypt) {
            this.fieldId = fieldId;
            this.notRead = notRead;
            this.notEdit = notEdit;
            this.notAdd = notAdd;
            this.isDecrypt = isDecrypt;
        }

        // 合并字段权限
        public void merge(FieldPermission other) {

            // 根据视图权限判断字段权限是否可读、可编辑
            // boolean canRead = !field.notRead && view.canRead;
            // boolean canEdit = !field.notEdit && view.canEdit;

            if (other == null) return;
            this.notRead = this.notRead || other.notRead;
            this.notEdit = this.notEdit || other.notEdit;
            this.notAdd = this.notAdd || other.notAdd;
            this.isDecrypt = this.isDecrypt || other.isDecrypt;
        }
    }

    // 定义视图权限类
    private class ViewPermission {
        private final String viewId;
        private boolean canRead;
        private boolean canEdit;
        private boolean canRemove;
        private final Map<String, FieldPermission> fields = new HashMap<>();

        ViewPermission(String viewId, Boolean canRead, Boolean canEdit, Boolean canRemove) {
            this.viewId = viewId;
            this.canRead = canRead;
            this.canEdit = canEdit;
            this.canRemove = canRemove;
        }

        // 合并视图权限
        public void merge(ViewPermission other) {
            if (other == null) return;
            this.canRead = this.canRead || other.canRead;
            this.canEdit = this.canEdit || other.canEdit;
            this.canRemove = this.canRemove || other.canRemove;

            // 合并字段权限
            for (Map.Entry<String, FieldPermission> otherEntry : other.fields.entrySet()) {
                this.fields
                    .computeIfAbsent(otherEntry.getKey(), k -> otherEntry.getValue())
                    .merge(otherEntry.getValue());
            }
        }
    }

    // 工作表权限类
    private class WorksheetPermission {
        String sheetId;
        boolean canAdd;
        Integer readLevel;
        Integer editLevel;
        Integer removeLevel;
        Map<String, ViewPermission> views = new HashMap<>();

        WorksheetPermission(String sheetId, Boolean canAdd, Integer readLevel, Integer editLevel, Integer removeLevel) {
            this.sheetId = sheetId;
            this.canAdd = canAdd;
            this.readLevel = readLevel;
            this.editLevel = editLevel;
            this.removeLevel = removeLevel;
        }

        // 合并工作表权限
        public void merge(WorksheetPermission other) {
            if (other == null) return;
            this.canAdd = this.canAdd || other.canAdd;
            this.readLevel = Math.max(this.readLevel, other.readLevel);
            this.editLevel = Math.max(this.editLevel, other.editLevel);
            this.removeLevel = Math.max(this.removeLevel, other.removeLevel);

            // 合并视图权限
            for (Map.Entry<String, ViewPermission> otherEntry : other.views.entrySet()) {
                this.views
                    .computeIfAbsent(otherEntry.getKey(), k -> otherEntry.getValue())
                    .merge(otherEntry.getValue());
            }
        }
    }

    // 角色权限类
    private class RolePermission {
        String roleId;
        Map<String, WorksheetPermission> worksheets = new HashMap<>();

        RolePermission(String roleId) {
            this.roleId = roleId;
        }

        // 合并角色权限
        public void merge(RolePermission other) {
            if (other == null) return;

            // 合并工作表权限
            for (Map.Entry<String, WorksheetPermission> otherEntry : other.worksheets.entrySet()) {
                this.worksheets
                    .computeIfAbsent(otherEntry.getKey(), k -> otherEntry.getValue())
                    .merge(otherEntry.getValue());
            }
        }
    }

    private RolePermission mergeRolePermissions(List<RolePermission> rolePermissions) {
        if (rolePermissions == null || rolePermissions.isEmpty()) return null;

        // 以第一个角色权限为基础进行合并, 作为当前最大权限合集
        RolePermission result = rolePermissions.get(0);
        for (int i = 1; i < rolePermissions.size(); i++) {
            result.merge(rolePermissions.get(i));
        }
        return result;
    }


    @Test
    public void execution() {
        // 创建示例数据，分别为角色、工作表、视图和字段
//        FieldPermission field1 = new FieldPermission("field1", true, false, false, false);
//        FieldPermission field2 = new FieldPermission("field1", true, true, false, false);  // 相同 ID 合并
//
//        ViewPermission view1 = new ViewPermission("view1", true, true, true);
//        view1.fields.put(field1.fieldId, field1);
//
//        ViewPermission view2 = new ViewPermission("view1", false, false, false);  // 相同 ID 合并
//        view2.fields.put(field2.fieldId, field2);
//
//        WorksheetPermission sheet1 = new WorksheetPermission("sheet1", true, 30, 30, 30);
//        sheet1.views.put(view1.viewId, view1);
//
//        WorksheetPermission sheet2 = new WorksheetPermission("sheet1", false, 20, 25, 30);  // 相同 ID 合并
//        sheet2.views.put(view2.viewId, view2);
//
//        RolePermission role1 = new RolePermission("role1");
//        role1.worksheets.put(sheet1.sheetId, sheet1);
//
//        RolePermission role2 = new RolePermission("role1");
//        role2.worksheets.put(sheet2.sheetId, sheet2);
//
//        // 合并多个角色权限
//        List<RolePermission> rolePermissions = List.of(role1, role2);

        Random random = new SecureRandom();
        List<RolePermission> rolePermissions = new ArrayList<>();
        // 创建示例数据，分别为角色、工作表、视图和字段
        for (int r = 0; r < 2; r++) {
            RolePermission role = new RolePermission("role" + r);
            for (int w = 0; w < 30; w++) {
                WorksheetPermission sheet = new WorksheetPermission("sheet" + w, random.nextBoolean(), 30, 30, 30);
                for (int v = 0; v < 10; v++) {
                    ViewPermission view = new ViewPermission("view" + v, random.nextBoolean(), random.nextBoolean(),
                                                             random.nextBoolean());
                    for (int f = 0; f < 500; f++) {
                        FieldPermission field = new FieldPermission("field" + f, random.nextBoolean(),
                                                                    random.nextBoolean(), random.nextBoolean(),
                                                                    random.nextBoolean());
                        view.fields.put(field.fieldId, field);
                    }
                    sheet.views.put(view.viewId, view);
                }
                role.worksheets.put(sheet.sheetId, sheet);
            }
            rolePermissions.add(role);
        }

        // 合并多个角色权限
        long l = System.currentTimeMillis();
        RolePermission mergedRole = mergeRolePermissions(rolePermissions);
        log.info("Time: {}ms", System.currentTimeMillis() - l);

        // 输出合并后的权限
//        mergedRole.worksheets.values().forEach(sheet -> {
//            System.out.println("Sheet: " + sheet.sheetId);
//            sheet.views.values().forEach(view -> {
//                System.out.println("  View: " + view.viewId);
//                view.fields.values().forEach(field -> {
//                    System.out.println("    Field: " + field.fieldId + ", notRead: " + field.notRead +
//                                           ", notEdit: " + field.notEdit + ", notAdd: " + field.notAdd);
//                });
//            });
//        });
    }
}

