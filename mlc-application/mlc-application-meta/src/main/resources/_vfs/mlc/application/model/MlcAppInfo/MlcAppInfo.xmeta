<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" x:extends="_MlcAppInfo.xmeta">

    <props>
        <prop name="projectId" mapToProp="nopTenantId" graphql:type="String" internal="true"/>

        <prop name="permissionType" queryable="true" internal="true" displayName="权限信息，暂时写死">
            <schema type="java.lang.Integer"/>
            <getter>200</getter>
        </prop>
    </props>
</meta>