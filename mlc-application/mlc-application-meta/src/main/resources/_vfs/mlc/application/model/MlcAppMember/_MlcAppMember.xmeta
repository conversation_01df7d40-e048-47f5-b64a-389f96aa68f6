<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="应用成员" i18n-en:displayName="App Member" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppMember</entityName>

    <primaryKey>memberId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="memberId" displayName="成员ID" propId="1" i18n-en:displayName="Member Id" tagSet="seq"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="false" internal="true"
              ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="appId" displayName="应用 ID" propId="2" i18n-en:displayName="App Id" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" internal="true" ui:show="X" ext:relation="appInfo">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="memberOrgUserId" displayName="成员关联组织用户" propId="3" i18n-en:displayName="Member Pk" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="memberType" displayName="角色类型" propId="4" i18n-en:displayName="Member Type" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer" dict="com.mlc.base.common.enums.application.AppMemberTypeEnum"/>
        </prop>
        <prop name="status" displayName="状态" propId="5" i18n-en:displayName="Status" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="6" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="7" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="8" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="9" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="10" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="appInfo" displayName="应用信息" i18n-en:displayName="App Info"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable" ext:kind="to-one" internal="true" queryable="true"
              ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="false" updatable="false" mandatory="true"
              lazy="true">
            <schema bizObjName="MlcAppInfo"/>
        </prop>
        <prop name="appRoleMappings" displayName="应用角色映射" i18n-en:displayName="App Role Mappings"
              tagSet="pub,cascade-delete,insertable,updatable,connection" ext:kind="to-many" internal="true"
              ext:joinLeftProp="memberId" ext:joinRightProp="memberId" orm:manyToManyRefProp="roleId" insertable="true"
              updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppMemberRole"/>
            </schema>
        </prop>
        <prop name="relatedAppRoleList" displayName="relatedAppRoleList" insertable="true" updatable="true"
              tagSet="pub,cascade-delete,insertable,updatable,connection" depends="~appRoleMappings" internal="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppRole"/>
            </schema>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefProps(
                                entity["appRoleMappings"], "appRole");
                        </getter>
        </prop>
        <prop name="relatedAppRoleList_ids" displayName="relatedAppRoleList" insertable="true" updatable="true"
              graphql:labelProp="relatedAppRoleList_label" lazy="true"
              ui:pickerUrl="/mlc/application/pages/MlcAppRole/picker.page.yaml" ui:refLabelProp="id" internal="true"
              depends="~appRoleMappings">
            <schema type="List&lt;String&gt;" domain="ref-ids"/>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefIds(
                                 entity["appRoleMappings"], "appRole");
                        </getter>
            <setter>
                            import io.nop.orm.support.OrmEntityHelper;
                            OrmEntityHelper.setRefIds(
                                 entity["appRoleMappings"], "appRole",value);
                        </setter>
        </prop>
        <prop name="relatedAppRoleList_label" displayName="relatedAppRoleList" internal="true"
              depends="~appRoleMappings" lazy="true">
            <schema type="String"/>
            <getter>
                    import io.nop.orm.support.OrmEntityHelper;
                    return OrmEntityHelper.getLabelForRefProps(
                         entity["appRoleMappings"], "appRole");
                </getter>
        </prop>
    </props>
</meta>