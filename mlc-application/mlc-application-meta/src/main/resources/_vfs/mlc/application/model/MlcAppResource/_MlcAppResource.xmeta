<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="应用资源树表" i18n-en:displayName="App Resource" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppResource</entityName>

    <primaryKey>resourceId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <tree parentProp="parentId" childrenProp="childrenSet"/>

    <props>
        <prop name="resourceId" displayName="角色工作表字段 ID" propId="1" i18n-en:displayName="Role Sheet Field Id"
              tagSet="seq" mandatory="true" queryable="true" sortable="true" insertable="true" updatable="false"
              internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="parentId" displayName="父级ID" propId="2" i18n-en:displayName="Field Id" tagSet="parent"
              queryable="true" sortable="true" insertable="true" updatable="true" ui:control="tree-parent"
              ext:relation="parent">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="childrenId" displayName="子级ID" propId="3" i18n-en:displayName="Role Sheet Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="50"/>
        </prop>
        <prop name="resourceType" displayName="资源类型" propId="4" i18n-en:displayName="Resource Type" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String"/>
        </prop>
        <prop name="permissions" displayName="权限标识集合" propId="5" i18n-en:displayName="Permissions" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true"
              graphql:jsonComponentProp="permissionsComponent">
            <schema stdDomain="json" domain="json-1000" type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="status" displayName="状态" propId="6" i18n-en:displayName="Status" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="7" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="8" i18n-en:displayName="Create By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="9" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="10" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="11" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="parent" displayName="父级" i18n-en:displayName="Parent" tagSet="pub,ref-pub,ref-cascade-delete"
              ext:kind="to-one" internal="true" queryable="true" ext:joinLeftProp="parentId"
              ext:joinRightProp="childrenId" insertable="false" updatable="false" lazy="true">
            <schema bizObjName="MlcAppResource"/>
        </prop>
        <prop name="childrenSet" displayName="子级" i18n-en:displayName="Children Set" tagSet="pub,cascade-delete"
              ext:kind="to-many" internal="true" ext:joinLeftProp="childrenId" ext:joinRightProp="parentId"
              insertable="false" updatable="false" lazy="true">
            <schema>
                <item bizObjName="MlcAppResource"/>
            </schema>
        </prop>
        <prop name="roleMappings" displayName="角色映射" i18n-en:displayName="Roles"
              tagSet="pub,cascade-delete,insertable,updatable" ext:kind="to-many" internal="true"
              ext:joinLeftProp="resourceId" ext:joinRightProp="resourceId" orm:manyToManyRefProp="roleId"
              insertable="true" updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppRoleResource"/>
            </schema>
        </prop>
        <prop name="permissionsComponent" tagSet="json,pub,edit" ext:kind="component" internal="true" insertable="true"
              updatable="true" mandatory="true" lazy="true">
            <schema type="io.nop.orm.component.JsonOrmComponent"/>
        </prop>
        <prop name="relatedRoleList" displayName="relatedRoleList" insertable="true" updatable="true"
              tagSet="pub,cascade-delete,insertable,updatable" depends="~roleMappings" internal="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppRole"/>
            </schema>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefProps(
                                entity["roleMappings"], "role");
                        </getter>
        </prop>
        <prop name="relatedRoleList_ids" displayName="relatedRoleList" insertable="true" updatable="true"
              graphql:labelProp="relatedRoleList_label" lazy="true"
              ui:pickerUrl="/mlc/application/pages/MlcAppRole/picker.page.yaml" ui:refLabelProp="id" internal="true"
              depends="~roleMappings">
            <schema type="List&lt;String&gt;" domain="ref-ids"/>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefIds(
                                 entity["roleMappings"], "role");
                        </getter>
            <setter>
                            import io.nop.orm.support.OrmEntityHelper;
                            OrmEntityHelper.setRefIds(
                                 entity["roleMappings"], "role",value);
                        </setter>
        </prop>
        <prop name="relatedRoleList_label" displayName="relatedRoleList" internal="true" depends="~roleMappings"
              lazy="true">
            <schema type="String"/>
            <getter>
                    import io.nop.orm.support.OrmEntityHelper;
                    return OrmEntityHelper.getLabelForRefProps(
                         entity["roleMappings"], "role");
                </getter>
        </prop>
    </props>
</meta>