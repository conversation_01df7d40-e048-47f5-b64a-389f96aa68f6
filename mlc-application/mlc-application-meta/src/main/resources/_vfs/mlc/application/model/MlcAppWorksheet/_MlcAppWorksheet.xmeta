<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="工作表" i18n-en:displayName="Worksheet" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppWorksheet</entityName>

    <primaryKey>worksheetId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys>
        <key name="aliasKey" props="alias"/>
    </keys>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="worksheetId" displayName="工作表ID" propId="1" i18n-en:displayName="Work Sheet ID" tagSet="seq"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="false" internal="true"
              ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="appId" displayName="应用 ID" propId="2" i18n-en:displayName="App Id" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" internal="true" ui:show="X" ext:relation="appInfo">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="appSectionId" displayName="应用分组 ID" propId="3" i18n-en:displayName="App Section Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" internal="true" ui:show="X"
              ext:relation="appSection">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="modelObjectEntityId" displayName="模型对象 ID" propId="4" i18n-en:displayName="Model Object Entity Id"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="false" ui:show="C">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="modelObjectEntityName" displayName="模型对象名称" propId="5"
              i18n-en:displayName="Model Object Entity Name" mandatory="true" queryable="true" sortable="true"
              insertable="true" updatable="false" ui:show="C">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="工作表名称" propId="6" i18n-en:displayName="App Name" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="alias" displayName="工作表别名" propId="7" i18n-en:displayName="Alias" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="entityName" displayName="记录名称" propId="8" i18n-en:displayName="Entity Name" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="记录">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="icon" displayName="图标" propId="9" i18n-en:displayName="Icon" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="table">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="iconColor" displayName="图标颜色" propId="10" i18n-en:displayName="Icon Color" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="#2296F3">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="iconUrl" displayName="图标路径" propId="11" i18n-en:displayName="Icon Url" queryable="true"
              sortable="true" insertable="true" updatable="true"
              defaultValue="https://fp1.mingdaoyun.cn/customIcon/table.svg">
            <schema type="java.lang.String" precision="100"/>
        </prop>
        <prop name="count" displayName="数量" propId="12" i18n-en:displayName="Count" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="visibleType" displayName="分享状态" propId="13" i18n-en:displayName="Visible Type" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="openApproval" displayName="开启审批" propId="14" i18n-en:displayName="Open Approval" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="workflowChildTableSwitch" displayName="工作流子表切换" propId="15"
              i18n-en:displayName="Workflow Child Table Switch" queryable="true" sortable="true" insertable="true"
              updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="navigateHide" displayName="侧边栏状态" propId="16" i18n-en:displayName="Navigate Hide" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isWorksheetQuery" displayName="是否配置工作表查询" propId="17" i18n-en:displayName="Is Worksheet Query"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isMarked" displayName="收藏状态" propId="18" i18n-en:displayName="Is Marked" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="controls" displayName="控件" propId="19" i18n-en:displayName="Controls" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="65535"/>
        </prop>
        <prop name="advancedSetting" displayName="高级设置" propId="20" i18n-en:displayName="Advanced Setting"
              queryable="true" sortable="true" insertable="true" updatable="true"
              graphql:jsonComponentProp="advancedSettingComponent" defaultValue="{}">
            <schema stdDomain="json" domain="json-1000" type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="submitSetting" displayName="提交设置" propId="21" i18n-en:displayName="Submit Setting" queryable="true"
              sortable="true" insertable="true" updatable="true" graphql:jsonComponentProp="submitSettingComponent"
              defaultValue="{}">
            <schema stdDomain="json" domain="json-1000" type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="developerNotes" displayName="开发者备注" propId="22" i18n-en:displayName="Developer Notes"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="200"/>
        </prop>
        <prop name="resume" displayName="摘要" propId="23" i18n-en:displayName="Resume" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.String" precision="80"/>
        </prop>
        <prop name="dec" displayName="详细说明" propId="24" i18n-en:displayName="Dec" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.String" precision="500"/>
        </prop>
        <prop name="status" displayName="状态" propId="25" i18n-en:displayName="Status" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="26" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="27" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="28" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="29" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="30" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="appSection" displayName="所属应用分组" i18n-en:displayName="App Section"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete,ref-query" ext:kind="to-one"
              internal="true" queryable="true" ext:joinLeftProp="appSectionId" ext:joinRightProp="appSectionId"
              insertable="false" updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppSection"/>
        </prop>
        <prop name="appInfo" displayName="应用信息" i18n-en:displayName="App Info"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete,ref-query" ext:kind="to-one"
              internal="true" queryable="true" ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppInfo"/>
        </prop>
        <prop name="worksheetViews" displayName="关联工作表视图" i18n-en:displayName="Worksheet View"
              tagSet="pub,insertable,updatable,cascade-delete,query" ext:kind="to-many" internal="true"
              ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="true" updatable="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheetView"/>
            </schema>
        </prop>
        <prop name="worksheetRuleItems" displayName="关联工作表规则" i18n-en:displayName="Worksheet Rule Items"
              tagSet="pub,insertable,updatable,cascade-delete" ext:kind="to-many" internal="true"
              ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="true" updatable="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheetRule"/>
            </schema>
        </prop>
        <prop name="worksheetBtns" displayName="关联工作表按钮" i18n-en:displayName="Worksheet Btns"
              tagSet="pub,insertable,updatable,cascade-delete" ext:kind="to-many" internal="true"
              ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="true" updatable="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheetButton"/>
            </schema>
        </prop>
        <prop name="worksheetPrintTemplates" displayName="关联工作表打印模板" i18n-en:displayName="Worksheet Print Templates"
              tagSet="pub,insertable,updatable,cascade-delete" ext:kind="to-many" internal="true"
              ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="true" updatable="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheetPrintTemplate"/>
            </schema>
        </prop>
        <prop name="worksheetSwitchs" displayName="关联工作表开发" i18n-en:displayName="Worksheet Switchs"
              tagSet="pub,insertable,updatable,cascade-delete" ext:kind="to-many" internal="true"
              ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="true" updatable="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheetSwitch"/>
            </schema>
        </prop>
        <prop name="advancedSettingComponent" tagSet="json,pub,edit" ext:kind="component" internal="true"
              insertable="true" updatable="true" lazy="true">
            <schema type="io.nop.orm.component.JsonOrmComponent"/>
        </prop>
        <prop name="submitSettingComponent" tagSet="json,pub,edit" ext:kind="component" internal="true"
              insertable="true" updatable="true" lazy="true">
            <schema type="io.nop.orm.component.JsonOrmComponent"/>
        </prop>
    </props>
</meta>