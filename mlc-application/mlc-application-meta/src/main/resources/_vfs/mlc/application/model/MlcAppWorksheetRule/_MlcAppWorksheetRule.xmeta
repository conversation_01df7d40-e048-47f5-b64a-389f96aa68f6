<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="工作表业务规则" i18n-en:displayName="Worksheet Rule" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppWorksheetRule</entityName>

    <primaryKey>ruleId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <orderBy>
        <field name="orderNum" desc="false"/>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="ruleId" displayName="业务规则 ID" propId="1" i18n-en:displayName="Rule Id" tagSet="seq" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="worksheetId" displayName="工作表 ID" propId="2" i18n-en:displayName="Worksheet Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" internal="true" ui:show="X"
              ext:relation="worksheet">
            <schema type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="工作表名称" propId="3" i18n-en:displayName="Name" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="type" displayName="规则类型" propId="4" i18n-en:displayName="Type" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="checkType" displayName="检查类型" propId="5" i18n-en:displayName="Check Type" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="hintType" displayName="提示类型" propId="6" i18n-en:displayName="Hint Type" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="controlIds" displayName="控件集合" propId="7" i18n-en:displayName="Control Ids" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="500"/>
        </prop>
        <prop name="filters" displayName="初始过滤" propId="8" i18n-en:displayName="Filters" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="ruleItems" displayName="规则项" propId="9" i18n-en:displayName="Rule Items" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="openDrawer" displayName="打开抽屉" propId="10" i18n-en:displayName="Open Drawer" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="disabled" displayName="已禁用" propId="11" i18n-en:displayName="Disabled" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="orderNum" displayName="排序" propId="12" i18n-en:displayName="Order Num" tagSet="sort"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="13" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="14" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="15" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="16" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="17" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="worksheet" displayName="所属工作表" i18n-en:displayName="Worksheet"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete" ext:kind="to-one" internal="true"
              queryable="true" ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppWorksheet"/>
        </prop>
    </props>
</meta>