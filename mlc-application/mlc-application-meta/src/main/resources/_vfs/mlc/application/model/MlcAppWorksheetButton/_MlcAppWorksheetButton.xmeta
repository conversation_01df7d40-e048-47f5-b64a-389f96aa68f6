<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="工作表自定义按钮" i18n-en:displayName="Worksheet Button" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppWorksheetButton</entityName>

    <primaryKey>btnId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="btnId" displayName="按钮ID" propId="1" i18n-en:displayName="Btn Id" tagSet="seq" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="worksheetId" displayName="工作表ID" propId="2" i18n-en:displayName="Worksheet Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" internal="true" ui:show="X"
              ext:relation="worksheet">
            <schema type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="名称" propId="3" i18n-en:displayName="Name" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="20"/>
        </prop>
        <prop name="isAllView" displayName="是否全部视图" propId="4" i18n-en:displayName="Is All View" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="displayViews" displayName="显示视图" propId="5" i18n-en:displayName="Display Views" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="500"/>
        </prop>
        <prop name="clickType" displayName="点击类型" propId="6" i18n-en:displayName="Click Type" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="confirmMsg" displayName="确认消息" propId="7" i18n-en:displayName="Confirm Msg" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="sureName" displayName="确认名称" propId="8" i18n-en:displayName="Sure Name" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="10"/>
        </prop>
        <prop name="cancelName" displayName="取消名称" propId="9" i18n-en:displayName="Cancel Name" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="10"/>
        </prop>
        <prop name="writeType" displayName="填写对象类型" propId="10" i18n-en:displayName="Write Type" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="writeObject" displayName="填写内容" propId="11" i18n-en:displayName="Write Object" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="writeControls" displayName="填写控件" propId="12" i18n-en:displayName="Write Controls" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="500"/>
        </prop>
        <prop name="relationControl" displayName="关联记录ID" propId="13" i18n-en:displayName="Relation Control"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="36"/>
        </prop>
        <prop name="addRelationControlId" displayName="新建关联记录ID" propId="14"
              i18n-en:displayName="Add Relation Control Id" queryable="true" sortable="true" insertable="true"
              updatable="true">
            <schema type="java.lang.String" precision="36"/>
        </prop>
        <prop name="workflowType" displayName="继续执行工作流" propId="15" i18n-en:displayName="Workflow Type" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="workflowId" displayName="工作流ID" propId="16" i18n-en:displayName="Workflow Id" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="36"/>
        </prop>
        <prop name="filters" displayName="过滤器" propId="17" i18n-en:displayName="Filters" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="color" displayName="颜色" propId="18" i18n-en:displayName="Color" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="#2296F3">
            <schema type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="icon" displayName="图标" propId="19" i18n-en:displayName="Icon" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="done_2">
            <schema type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="iconUrl" displayName="图标URL" propId="20" i18n-en:displayName="Icon Url" queryable="true"
              sortable="true" insertable="true" updatable="true"
              defaultValue="https://fp1.mingdaoyun.cn/custom_icon/PNG/done_2.png">
            <schema type="java.lang.String" precision="100"/>
        </prop>
        <prop name="desc" displayName="描述" propId="21" i18n-en:displayName="Desc" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.String" precision="200"/>
        </prop>
        <prop name="advancedSetting" displayName="高级设置" propId="22" i18n-en:displayName="Advanced Setting"
              queryable="true" sortable="true" insertable="true" updatable="true"
              graphql:jsonComponentProp="advancedSettingComponent" defaultValue="{}">
            <schema stdDomain="json" domain="json-1000" type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="enableConfirm" displayName="启用二次确认" propId="23" i18n-en:displayName="Enable Confirm"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="verifyPwd" displayName="校验密码" propId="24" i18n-en:displayName="Verify Pwd" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isBatch" displayName="是否多条数据源" propId="25" i18n-en:displayName="Is Batch" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="showType" displayName="启用按钮类型" propId="26" i18n-en:displayName="Show Type" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="addRelationControl" displayName="添加关联控件" propId="27" i18n-en:displayName="Add Relation Control"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="200"/>
        </prop>
        <prop name="editAttrs" displayName="属性" propId="28" i18n-en:displayName="Edit Attrs" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="200"/>
        </prop>
        <prop name="disabled" displayName="禁用" propId="29" i18n-en:displayName="Disabled" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="status" displayName="状态" propId="30" i18n-en:displayName="Status" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="31" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="32" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="33" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="34" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="35" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="worksheet" displayName="所属工作表" i18n-en:displayName="Worksheet"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete" ext:kind="to-one" internal="true"
              queryable="true" ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppWorksheet"/>
        </prop>
        <prop name="advancedSettingComponent" tagSet="json,pub,edit" ext:kind="component" internal="true"
              insertable="true" updatable="true" lazy="true">
            <schema type="io.nop.orm.component.JsonOrmComponent"/>
        </prop>
    </props>
</meta>