<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="应用分组" i18n-en:displayName="App Section" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppSection</entityName>

    <primaryKey>appSectionId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <tree parentProp="parentId" childrenProp="childSections"/>

    <props>
        <prop name="appSectionId" displayName="应用分组ID" propId="1" i18n-en:displayName="Section ID" tagSet="seq"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="false" internal="true"
              ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="appId" displayName="应用ID" propId="2" i18n-en:displayName="App Id" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" internal="true" ui:show="X" ext:relation="appInfo">
            <schema type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="应用名" propId="3" i18n-en:displayName="App Name" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="parentId" displayName="父级ID" propId="4" i18n-en:displayName="Parent ID" tagSet="parent"
              queryable="true" sortable="true" insertable="true" updatable="true" ui:control="tree-parent"
              ext:relation="parent">
            <schema type="java.lang.String" precision="50"/>
        </prop>
        <prop name="icon" displayName="图标" propId="5" i18n-en:displayName="icon" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="iconUrl" displayName="图标路径" propId="6" i18n-en:displayName="Icon Url" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="100"/>
        </prop>
        <prop name="isLock" displayName="锁定状态" propId="7" i18n-en:displayName="isLock" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="fixed" displayName="维护状态" propId="8" i18n-en:displayName="fixed" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="9" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="10" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="11" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="12" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="13" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="appInfo" displayName="应用信息" i18n-en:displayName="App Info"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-query,ref-cascade-delete" ext:kind="to-one"
              internal="true" queryable="true" ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppInfo"/>
        </prop>
        <prop name="parent" displayName="父资源" i18n-en:displayName="Parent" tagSet="pub,ref-pub,ref-delete"
              ext:kind="to-one" internal="true" queryable="true" ext:joinLeftProp="parentId"
              ext:joinRightProp="appSectionId" insertable="false" updatable="false" lazy="true">
            <schema bizObjName="MlcAppSection"/>
        </prop>
        <prop name="childSections" displayName="子资源" i18n-en:displayName="Children" tagSet="pub,delete"
              ext:kind="to-many" internal="true" ext:joinLeftProp="appSectionId" ext:joinRightProp="parentId"
              insertable="false" updatable="false" lazy="true">
            <schema>
                <item bizObjName="MlcAppSection"/>
            </schema>
        </prop>
        <prop name="workSheetInfo" displayName="关联应用组" i18n-en:displayName="Work Sheet Info"
              tagSet="pub,insertable,updatable,cascade-delete,query" ext:kind="to-many" internal="true"
              ext:joinLeftProp="appSectionId" ext:joinRightProp="appSectionId" insertable="true" updatable="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheet"/>
            </schema>
        </prop>
    </props>
</meta>