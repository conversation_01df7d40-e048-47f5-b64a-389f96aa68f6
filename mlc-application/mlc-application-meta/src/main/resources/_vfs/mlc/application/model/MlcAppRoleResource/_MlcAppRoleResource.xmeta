<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="应用角色资源表" i18n-en:displayName="App Role Resource" tagSet="no-web,many-to-many" xmlns:ui="ui"
      xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppRoleResource</entityName>

    <primaryKey>sid</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <props>
        <prop name="sid" displayName="主键ID" propId="1" i18n-en:displayName="SID" tagSet="seq" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="roleId" displayName="角色ID" propId="2" i18n-en:displayName="Role_id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" internal="true" ui:show="X"
              ext:relation="role">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="resourceId" displayName="资源ID" propId="3" i18n-en:displayName="Resource Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" internal="true" ui:show="X"
              ext:relation="resource">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="operation" displayName="权限操作类型" propId="4" i18n-en:displayName="Operation" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" dict="com.mlc.base.common.enums.application.AppPermissionOperationEnum"/>
        </prop>
        <prop name="deltaPermissions" displayName="变更的权限标签" propId="5" i18n-en:displayName="Delta Permissions"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="true"
              graphql:jsonComponentProp="deltaPermissionsComponent">
            <schema stdDomain="json" domain="json-1000" type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="priority" displayName="冲突时优先级" propId="6" i18n-en:displayName="Priority" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="7" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="8" i18n-en:displayName="Create By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="9" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="10" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="11" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="role" displayName="角色" i18n-en:displayName="Role"
              tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable" ext:kind="to-one" internal="true"
              queryable="true" ext:joinLeftProp="roleId" ext:joinRightProp="roleId" insertable="false" updatable="false"
              mandatory="true" lazy="true">
            <schema bizObjName="MlcAppRole"/>
        </prop>
        <prop name="resource" displayName="资源" i18n-en:displayName="Resource"
              tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable" ext:kind="to-one" internal="true"
              queryable="true" ext:joinLeftProp="resourceId" ext:joinRightProp="resourceId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppResource"/>
        </prop>
        <prop name="deltaPermissionsComponent" tagSet="json,pub,edit" ext:kind="component" internal="true"
              insertable="true" updatable="true" mandatory="true" lazy="true">
            <schema type="io.nop.orm.component.JsonOrmComponent"/>
        </prop>
    </props>
</meta>