<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef"
  x:extends="_MlcAppWorksheet.xmeta">

  <props>
    <prop name="controls" displayName="工作表控件" lazy="true">
      <schema stdDomain="worksheetComponentList"/>

      <transformIn>
        if (value instanceof String) {
          return value;
        }
        return $Jackson.toJsonString(value);
      </transformIn>

      <transformOut>
        <c:script><![CDATA[
          inject('nopBizObjectManager').getBizObject('MlcAppWorksheet').invoke('handleFillRelationControl', {worksheet: entity}, null, svcCtx)
        ]]></c:script>
      </transformOut>
    </prop>

    <prop name="dec" displayName="详细说明" lazy="true">
    </prop>

    <prop name="allowAdd" displayName="允许添加" lazy="true">
      <schema type="java.lang.Boolean"/>
    </prop>

    <prop name="advancedSetting" lazy="true">
      <schema stdDomain="list-json-string"/>
    </prop>

    <prop name="submitSetting" lazy="true">
      <schema stdDomain="list-json-string"/>
    </prop>

    <prop name="template" displayName="模板列表" queryable="true" internal="true">
      <schema bizObjName="MlcAppWorksheet"/>
      <getter>entity</getter>
    </prop>

    <prop name="roleType" queryable="true" internal="true" displayName="应用角色最大权限">
      <schema type="java.lang.Integer"/>
      <getter>
          import io.nop.api.core.auth.IUserContext;
          return IUserContext.get().getApplicationMemberRoleBean().calcMaxRoleType();
      </getter>
    </prop>

    <prop name="projectId" mapToProp="nopTenantId" graphql:type="String" internal="true"/>

    <prop name="sheetViews" queryable="true" displayName="缓存角色视图列表" internal="true">
      <schema type="List&lt;com.mlc.application.service.site.beans.MenuTreeBean.MenuWorkSheetViewInfo&gt;"/>
    </prop>
  </props>
</meta>