<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef"
  x:extends="_MlcAppWorksheetButton.xmeta">

  <props>
    <prop name="filters" lazy="true">
      <schema stdDomain="list-json-string"/>
    </prop>

    <prop name="writeControls" lazy="true">
      <schema stdDomain="list-json-string"/>
    </prop>

    <prop name="advancedSetting" lazy="true">
      <schema stdDomain="list-json-string"/>
    </prop>

  </props>

</meta>