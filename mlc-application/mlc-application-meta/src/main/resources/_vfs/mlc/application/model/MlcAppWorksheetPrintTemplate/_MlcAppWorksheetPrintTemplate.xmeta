<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="工作表自定义打印模板" i18n-en:displayName="Worksheet Print Template" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppWorksheetPrintTemplate</entityName>

    <primaryKey>printTemplateId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys>
        <key name="nameKey" props="name"/>
    </keys>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="printTemplateId" displayName="打印模板 ID" propId="1" i18n-en:displayName="Print Template Id"
              tagSet="seq" mandatory="true" queryable="true" sortable="true" insertable="true" updatable="false"
              internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="worksheetId" displayName="工作表 ID" propId="2" i18n-en:displayName="Worksheet Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" ext:relation="worksheet">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="名称" propId="3" i18n-en:displayName="Name" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="formName" displayName="来自名称" propId="4" i18n-en:displayName="Form Name" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="companyName" displayName="公司名称" propId="5" i18n-en:displayName="Company Name" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="type" displayName="类型" propId="6" i18n-en:displayName="Type" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="range" displayName="使用范围" propId="7" i18n-en:displayName="Range" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="controlStyles" displayName="控件样式" propId="8" i18n-en:displayName="Control Styles" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="255"/>
        </prop>
        <prop name="relationStyle" displayName="关联样式" propId="9" i18n-en:displayName="Relation Style" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="255"/>
        </prop>
        <prop name="ownerAccount" displayName="拥有者" propId="10" i18n-en:displayName="Owner Account" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="printTime" displayName="打印时间" propId="11" i18n-en:displayName="Print Time" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="qrCode" displayName="二维码" propId="12" i18n-en:displayName="QrCode" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="printAccount" displayName="打印人" propId="13" i18n-en:displayName="Print Account" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="logoChecked" displayName="打印 logo" propId="14" i18n-en:displayName="Logo Checked" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="titleChecked" displayName="标题是否打印" propId="15" i18n-en:displayName="Title Checked" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="companyNameChecked" displayName="公司名称否打印" propId="16" i18n-en:displayName="Company Name Checked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="formNameChecked" displayName="表单标题" propId="17" i18n-en:displayName="Form Name Checked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="createTimeChecked" displayName="创建时间是否打印" propId="18" i18n-en:displayName="Create Time Checked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="createAccountChecked" displayName="创建者是否打印" propId="19" i18n-en:displayName="Create Account Checked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="updateTimeChecked" displayName="更新时间是否打印" propId="20" i18n-en:displayName="Update Time Checked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="updateAccountChecked" displayName="更新人是否打印" propId="21" i18n-en:displayName="Update Account Checked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="ownerAccountChecked" displayName="拥有者是否打印" propId="22" i18n-en:displayName="OwnerAccountChecked"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="showData" displayName="空值是否隐藏" propId="23" i18n-en:displayName="Show Data" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="printOption" displayName="选项字段平铺打印" propId="24" i18n-en:displayName="Print Option" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="shareType" displayName="分享类型" propId="25" i18n-en:displayName="Share Type" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="font" displayName="字体" propId="26" i18n-en:displayName="Font" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="allowDownloadPermission" displayName="允许下载打印文件" propId="27"
              i18n-en:displayName="Allow Download Permission" queryable="true" sortable="true" insertable="true"
              updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="advanceSettings" displayName="高级设置" propId="28" i18n-en:displayName="Advanced Settings"
              queryable="true" sortable="true" insertable="true" updatable="true"
              graphql:jsonComponentProp="advanceSettingsComponent"
              defaultValue="[
            {
                &quot;key&quot;: &quot;atta_style&quot;,
                &quot;value&quot;: &quot;{}&quot;
            }
        ]">
            <schema stdDomain="json" domain="json-1000" type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="approvePosition" displayName="审批签名位置" propId="29" i18n-en:displayName="Approve Position"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="approvalIds" displayName="审批批准编号" propId="30" i18n-en:displayName="Approval Ids" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="100"/>
        </prop>
        <prop name="views" displayName="视图" propId="31" i18n-en:displayName="Views" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="500"/>
        </prop>
        <prop name="orderNumber" displayName="排序" propId="32" i18n-en:displayName="Order Number" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="10"/>
        </prop>
        <prop name="filters" displayName="过滤器" propId="33" i18n-en:displayName="Filters" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="[]">
            <schema type="java.lang.String" precision="1000"/>
        </prop>
        <prop name="remark" displayName="备注" propId="34" i18n-en:displayName="Remark" queryable="true" sortable="true"
              insertable="true" updatable="true">
            <schema type="java.lang.String"/>
        </prop>
        <prop name="disabled" displayName="禁用" propId="35" i18n-en:displayName="Disabled" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="36" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="37" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="38" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="39" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="40" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="worksheet" displayName="所属工作表" i18n-en:displayName="Worksheet"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable,ref-cascade-delete" ext:kind="to-one" internal="true"
              queryable="true" ext:joinLeftProp="worksheetId" ext:joinRightProp="worksheetId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppWorksheet"/>
        </prop>
        <prop name="advanceSettingsComponent" tagSet="json,pub,edit" ext:kind="component" internal="true"
              insertable="true" updatable="true" lazy="true">
            <schema type="io.nop.orm.component.JsonOrmComponent"/>
        </prop>
    </props>
</meta>