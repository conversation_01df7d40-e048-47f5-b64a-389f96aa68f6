<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="应用角色" i18n-en:displayName="App Role" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppRole</entityName>

    <primaryKey>roleId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="roleId" displayName="角色 ID" propId="1" i18n-en:displayName="Role Id" tagSet="seq" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="appId" displayName="应用 ID" propId="2" i18n-en:displayName="App Id" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" internal="true" ui:show="X" ext:relation="appInfo">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="名称" propId="3" i18n-en:displayName="Worksheet Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="20"/>
        </prop>
        <prop name="roleType" displayName="角色类型" propId="4" i18n-en:displayName="Role Type" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer" dict="com.mlc.base.common.enums.application.AppRoleTypeEnum"/>
        </prop>
        <prop name="appSettingsEnum" displayName="设置" propId="5" i18n-en:displayName="App Settings Enum"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="notify" displayName="是否通知" propId="6" i18n-en:displayName="Notify" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isDebug" displayName="是否开启调试" propId="7" i18n-en:displayName="Is Debug" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="hideAppForMembers" displayName="隐藏应用" propId="8" i18n-en:displayName="Hide App For Members"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="permissionWay" displayName="权限定义方式" propId="9" i18n-en:displayName="Permission Way" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="extendAttrs" displayName="操作标签" propId="10" i18n-en:displayName="Extend Attrs" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="100"/>
        </prop>
        <prop name="optionalControls" displayName="操作标签" propId="11" i18n-en:displayName="Optional Controls"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema stdDomain="csv-list-with-null" domain="csvListWithNull" type="java.lang.String" precision="100"/>
        </prop>
        <prop name="generalAdd" displayName="允许新增" propId="12" i18n-en:displayName="General Add" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalShare" displayName="允许分享" propId="13" i18n-en:displayName="General Share" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalImport" displayName="允许导入" propId="14" i18n-en:displayName="General Import" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalExport" displayName="允许导出" propId="15" i18n-en:displayName="General Export" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalDiscussion" displayName="允许讨论" propId="16" i18n-en:displayName="General Discussion"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalSystemPrinting" displayName="允许打印" propId="17" i18n-en:displayName="General System printing"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalAttachmentDownload" displayName="允许下载附件" propId="18"
              i18n-en:displayName="General Attachment Download" queryable="true" sortable="true" insertable="true"
              updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="generalLogging" displayName="允许查看日志" propId="19" i18n-en:displayName="General Logging"
              queryable="true" sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="sortIndex" displayName="排序" propId="20" i18n-en:displayName="Sort Index" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="description" displayName="描述" propId="21" i18n-en:displayName="Description" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="200"/>
        </prop>
        <prop name="status" displayName="状态" propId="22" i18n-en:displayName="Status" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="23" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="25" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="26" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="27" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="28" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="appInfo" displayName="应用信息" i18n-en:displayName="App Info"
              tagSet="pub,ref-pub,ref-insertable,ref-updatable" ext:kind="to-one" internal="true" queryable="true"
              ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="false" updatable="false" mandatory="true"
              lazy="true">
            <schema bizObjName="MlcAppInfo"/>
        </prop>
        <prop name="appMemberMappings" displayName="应用成员映射" i18n-en:displayName="App Member Mappings"
              tagSet="pub,cascade-delete,insertable,updatable,connection" ext:kind="to-many" internal="true"
              ext:joinLeftProp="roleId" ext:joinRightProp="roleId" orm:manyToManyRefProp="memberId" insertable="true"
              updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppMemberRole"/>
            </schema>
        </prop>
        <prop name="resourceMappings" displayName="资源映射" i18n-en:displayName="Resources"
              tagSet="pub,cascade-delete,insertable,updatable" ext:kind="to-many" internal="true"
              ext:joinLeftProp="roleId" ext:joinRightProp="roleId" orm:manyToManyRefProp="resourceId" insertable="true"
              updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppRoleResource"/>
            </schema>
        </prop>
        <prop name="relatedAppMemberList" displayName="relatedAppMemberList" insertable="true" updatable="true"
              tagSet="pub,cascade-delete,insertable,updatable,connection" depends="~appMemberMappings" internal="true"
              lazy="true">
            <schema>
                <item bizObjName="MlcAppMember"/>
            </schema>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefProps(
                                entity["appMemberMappings"], "appMember");
                        </getter>
        </prop>
        <prop name="relatedAppMemberList_ids" displayName="relatedAppMemberList" insertable="true" updatable="true"
              graphql:labelProp="relatedAppMemberList_label" lazy="true"
              ui:pickerUrl="/mlc/application/pages/MlcAppMember/picker.page.yaml" ui:refLabelProp="id" internal="true"
              depends="~appMemberMappings">
            <schema type="List&lt;String&gt;" domain="ref-ids"/>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefIds(
                                 entity["appMemberMappings"], "appMember");
                        </getter>
            <setter>
                            import io.nop.orm.support.OrmEntityHelper;
                            OrmEntityHelper.setRefIds(
                                 entity["appMemberMappings"], "appMember",value);
                        </setter>
        </prop>
        <prop name="relatedAppMemberList_label" displayName="relatedAppMemberList" internal="true"
              depends="~appMemberMappings" lazy="true">
            <schema type="String"/>
            <getter>
                    import io.nop.orm.support.OrmEntityHelper;
                    return OrmEntityHelper.getLabelForRefProps(
                         entity["appMemberMappings"], "appMember");
                </getter>
        </prop>
        <prop name="relatedResourceList" displayName="relatedResourceList" insertable="true" updatable="true"
              tagSet="pub,cascade-delete,insertable,updatable" depends="~resourceMappings" internal="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppResource"/>
            </schema>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefProps(
                                entity["resourceMappings"], "resource");
                        </getter>
        </prop>
        <prop name="relatedResourceList_ids" displayName="relatedResourceList" insertable="true" updatable="true"
              graphql:labelProp="relatedResourceList_label" lazy="true"
              ui:pickerUrl="/mlc/application/pages/MlcAppResource/picker.page.yaml" ui:refLabelProp="id" internal="true"
              depends="~resourceMappings">
            <schema type="List&lt;String&gt;" domain="ref-ids"/>
            <getter>
                            import io.nop.orm.support.OrmEntityHelper;
                            return OrmEntityHelper.getRefIds(
                                 entity["resourceMappings"], "resource");
                        </getter>
            <setter>
                            import io.nop.orm.support.OrmEntityHelper;
                            OrmEntityHelper.setRefIds(
                                 entity["resourceMappings"], "resource",value);
                        </setter>
        </prop>
        <prop name="relatedResourceList_label" displayName="relatedResourceList" internal="true"
              depends="~resourceMappings" lazy="true">
            <schema type="String"/>
            <getter>
                    import io.nop.orm.support.OrmEntityHelper;
                    return OrmEntityHelper.getLabelForRefProps(
                         entity["resourceMappings"], "resource");
                </getter>
        </prop>
    </props>
</meta>