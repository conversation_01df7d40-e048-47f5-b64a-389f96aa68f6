<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="应用信息" i18n-en:displayName="App Info" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppInfo</entityName>

    <primaryKey>appId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <orderBy>
        <field name="createBy" desc="false"/>
    </orderBy>

    <props>
        <prop name="appId" displayName="应用ID" propId="1" i18n-en:displayName="App ID" tagSet="seq" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="modelDefinitionId" displayName="模型定义Id" propId="2" i18n-en:displayName="Model Definition Id"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="true">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="name" displayName="应用名" propId="3" i18n-en:displayName="App Name" mandatory="true" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="icon" displayName="图标" propId="4" i18n-en:displayName="Icon" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0_lego">
            <schema type="java.lang.String" precision="50"/>
        </prop>
        <prop name="iconColor" displayName="图标颜色" propId="5" i18n-en:displayName="Icon Color" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="#2296F3">
            <schema type="java.lang.String" precision="10"/>
        </prop>
        <prop name="iconUrl" displayName="图标路径" propId="6" i18n-en:displayName="Icon Url" queryable="true"
              sortable="true" insertable="true" updatable="true"
              defaultValue="https://fp1.mingdaoyun.cn/customIcon/0_lego.svg">
            <schema type="java.lang.String" precision="100"/>
        </prop>
        <prop name="createType" displayName="创建类型" propId="7" i18n-en:displayName="Create Type" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="sourceType" displayName="来源类型" propId="8" i18n-en:displayName="Source Type" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="navColor" displayName="导航颜色" propId="9" i18n-en:displayName="Nnav Color" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="#2296F3">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="lightColor" displayName="背景色" propId="10" i18n-en:displayName="Light Color" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="30"/>
        </prop>
        <prop name="groupId" displayName="分组id" propId="11" i18n-en:displayName="Group Id" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="32"/>
        </prop>
        <prop name="isLock" displayName="锁定状态" propId="12" i18n-en:displayName="isLock" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isNew" displayName="新建状态" propId="13" i18n-en:displayName="Is New" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="fixed" displayName="维护状态" propId="14" i18n-en:displayName="fixed" queryable="true" sortable="true"
              insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="pcDisplay" displayName="Pc 端显示" propId="15" i18n-en:displayName="Pc Display" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="appDisplay" displayName="App 端显示" propId="16" i18n-en:displayName="App端显示" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="pcNaviStyle" displayName="pc 端导航方式" propId="17" i18n-en:displayName="Pc Navi Style" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="displayIcon" displayName="显示图标级别" propId="18" i18n-en:displayName="Display Icon" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="011">
            <schema type="java.lang.String" precision="3"/>
        </prop>
        <prop name="selectAppItmeType" displayName="记住上次使用" propId="19" i18n-en:displayName="Select App Itme Type"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="2">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="webMobileDisplay" displayName="Web 移动端显示" propId="20" i18n-en:displayName="Web Mobile Display"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="appNaviStyle" displayName="移动端导航方式" propId="21" i18n-en:displayName="App Navi Style"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="gridDisplayMode" displayName="移动端显示模式" propId="22" i18n-en:displayName="Grid Display Mode"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="appNaviDisplayType" displayName="移动端分组展开方式" propId="23" i18n-en:displayName="App Navi Display Type"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="viewHideNavi" displayName="查看隐藏项" propId="24" i18n-en:displayName="View Hide Navi" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isMarked" displayName="星标状态" propId="25" i18n-en:displayName="Is Marked" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="description" displayName="应用说明" propId="26" i18n-en:displayName="description" queryable="true"
              sortable="true" insertable="true" updatable="true">
            <schema type="java.lang.String" precision="2000"/>
        </prop>
        <prop name="appStatus" displayName="状态" propId="27" i18n-en:displayName="App Status" queryable="true"
              sortable="true" insertable="true" updatable="true" defaultValue="1">
            <schema type="java.lang.Integer"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="28" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="29" i18n-en:displayName="Create By" tagSet="sort"
              mandatory="true" queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="30" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="31" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="32" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" ui:show="L">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="appSections" displayName="关联应用分组" i18n-en:displayName="App Sections"
              tagSet="pub,insertable,updatable,query,cascade-delete" ext:kind="to-many" internal="true"
              ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="true" updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppSection"/>
            </schema>
        </prop>
        <prop name="workSheetItems" displayName="关联工作表" i18n-en:displayName="Work Sheet Items"
              tagSet="pub,insertable,updatable,cascade-delete,query" ext:kind="to-many" internal="true"
              ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="true" updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppWorksheet"/>
            </schema>
        </prop>
        <prop name="appMembers" displayName="关联应用角色" i18n-en:displayName="App Roles" tagSet="pub,insertable,updatable"
              ext:kind="to-many" internal="true" ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="true"
              updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppMember"/>
            </schema>
        </prop>
        <prop name="appRoles" displayName="关联应用角色" i18n-en:displayName="App Roles" tagSet="pub,insertable,updatable"
              ext:kind="to-many" internal="true" ext:joinLeftProp="appId" ext:joinRightProp="appId" insertable="true"
              updatable="true" lazy="true">
            <schema>
                <item bizObjName="MlcAppRole"/>
            </schema>
        </prop>
    </props>
</meta>