<?xml version="1.0" encoding="UTF-8" ?>
<meta x:schema="/nop/schema/xmeta.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:i18n-en="i18n-en" xmlns:ext="ext"
      xmlns:xpl="xpl" ext:model="orm" xmlns:c="c" xmlns:graphql="graphql" xmlns:meta-gen="meta-gen" xmlns:biz="biz"
      displayName="成员角色中间表" i18n-en:displayName="Member Role" tagSet="no-web,many-to-many" xmlns:ui="ui" xmlns:orm="orm">

    <entityName>com.mlc.application.dao.entity.MlcAppMemberRole</entityName>

    <primaryKey>memberId,roleId</primaryKey>

    <x:gen-extends>
        <meta-gen:DefaultMetaGenExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:gen-extends>

    <x:post-extends>
        <meta-gen:DefaultMetaPostExtends xpl:lib="/nop/core/xlib/meta-gen.xlib"/>
    </x:post-extends>

    <keys/>

    <props>
        <prop name="memberId" displayName="成员ID" propId="1" i18n-en:displayName="Member Id" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X"
              ext:relation="appMember">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="roleId" displayName="角色ID" propId="2" i18n-en:displayName="Role ID" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="false" internal="true" ui:show="X"
              ext:relation="appRole">
            <schema domain="pkId" type="java.lang.String" precision="32"/>
        </prop>
        <prop name="isOwner" displayName="是否拥有者" propId="3" i18n-en:displayName="Is Owner" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isManager" displayName="是否管理员" propId="4" i18n-en:displayName="Is Manager" mandatory="true"
              queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="isRoleCharger" displayName="是否角色负责人" propId="5" i18n-en:displayName="Is Role Charger"
              mandatory="true" queryable="true" sortable="true" insertable="true" updatable="true" defaultValue="0">
            <schema type="java.lang.Boolean"/>
        </prop>
        <prop name="version" displayName="数据版本" propId="6" i18n-en:displayName="Version" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="version" type="java.lang.Integer"/>
        </prop>
        <prop name="createBy" displayName="创建人" propId="7" i18n-en:displayName="Create By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="createdBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="createdAt" displayName="创建时间" propId="8" i18n-en:displayName="Created At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="createTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="updateBy" displayName="修改人" propId="9" i18n-en:displayName="Update By" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="updatedBy" type="java.lang.String" precision="50"/>
        </prop>
        <prop name="updatedAt" displayName="修改时间" propId="10" i18n-en:displayName="Updated At" mandatory="true"
              queryable="true" sortable="true" insertable="false" updatable="false" internal="true" ui:show="X">
            <schema domain="updateTime" type="java.sql.Timestamp"/>
        </prop>
        <prop name="appMember" displayName="应用成员" i18n-en:displayName="App Member"
              tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable,ref-connection" ext:kind="to-one"
              internal="true" queryable="true" ext:joinLeftProp="memberId" ext:joinRightProp="memberId"
              insertable="false" updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppMember"/>
        </prop>
        <prop name="appRole" displayName="应用角色" i18n-en:displayName="App Role"
              tagSet="pub,ref-pub,ref-cascade-delete,ref-insertable,ref-updatable,ref-connection" ext:kind="to-one"
              internal="true" queryable="true" ext:joinLeftProp="roleId" ext:joinRightProp="roleId" insertable="false"
              updatable="false" mandatory="true" lazy="true">
            <schema bizObjName="MlcAppRole"/>
        </prop>
    </props>
</meta>