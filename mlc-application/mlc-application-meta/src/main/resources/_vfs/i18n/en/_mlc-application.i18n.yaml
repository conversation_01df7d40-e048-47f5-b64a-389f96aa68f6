entity:
  label:
    MlcAppInfo: App Info
    MlcAppMember: App Member
    MlcAppMemberRole: Member Role
    MlcAppResource: App Resource
    MlcAppRole: App Role
    MlcAppRoleResource: App Role Resource
    MlcAppSection: App Section
    MlcAppWorksheet: Worksheet
    MlcAppWorksheetButton: Worksheet Button
    MlcAppWorksheetPrintTemplate: Worksheet Print Template
    MlcAppWorksheetRule: Worksheet Rule
    MlcAppWorksheetSwitch: Worksheet Button
    MlcAppWorksheetView: Worksheet View
prop:
  label:
    MlcAppInfo:
      appId: App ID
      modelDefinitionId: Model Definition Id
      name: App Name
      icon: Icon
      iconColor: Icon Color
      iconUrl: Icon Url
      createType: Create Type
      sourceType: Source Type
      navColor: Nnav Color
      lightColor: Light Color
      groupId: Group Id
      isLock: isLock
      isNew: Is New
      fixed: fixed
      pcDisplay: Pc Display
      appDisplay: App端显示
      pcNaviStyle: Pc Navi Style
      displayIcon: Display Icon
      selectAppItmeType: Select App Itme Type
      webMobileDisplay: Web Mobile Display
      appNaviStyle: App Navi Style
      gridDisplayMode: Grid Display Mode
      appNaviDisplayType: App Navi Display Type
      viewHideNavi: View Hide Navi
      isMarked: Is Marked
      description: description
      appStatus: App Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      appSections: App Sections
      workSheetItems: Work Sheet Items
      appMembers: App Roles
      appRoles: App Roles
      projectId: null
      permissionType: null
    MlcAppMember:
      memberId: Member Id
      appId: App Id
      memberOrgUserId: Member Pk
      memberType: Member Type
      memberType_label: null
      status: Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      appInfo: App Info
      appRoleMappings: App Role Mappings
      appRoleMappingsConnection: null
      relatedAppRoleList: null
      relatedAppRoleList_ids: null
      relatedAppRoleList_label: null
      name: null
    MlcAppMemberRole:
      memberId: Member Id
      roleId: Role ID
      isOwner: Is Owner
      isManager: Is Manager
      isRoleCharger: Is Role Charger
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      appMember: App Member
      appRole: App Role
    MlcAppResource:
      resourceId: Role Sheet Field Id
      parentId: Field Id
      childrenId: Role Sheet Id
      resourceType: Resource Type
      permissions: Permissions
      status: Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      parent: Parent
      childrenSet: Children Set
      roleMappings: Roles
      permissionsComponent: null
      relatedRoleList: null
      relatedRoleList_ids: null
      relatedRoleList_label: null
    MlcAppRole:
      roleId: Role Id
      appId: App Id
      name: Worksheet Id
      roleType: Role Type
      roleType_label: null
      appSettingsEnum: App Settings Enum
      notify: Notify
      isDebug: Is Debug
      hideAppForMembers: Hide App For Members
      permissionWay: Permission Way
      extendAttrs: Extend Attrs
      optionalControls: Optional Controls
      generalAdd: General Add
      generalShare: General Share
      generalImport: General Import
      generalExport: General Export
      generalDiscussion: General Discussion
      generalSystemPrinting: General System printing
      generalAttachmentDownload: General Attachment Download
      generalLogging: General Logging
      sortIndex: Sort Index
      description: Description
      status: Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      appInfo: App Info
      appMemberMappings: App Member Mappings
      appMemberMappingsConnection: null
      resourceMappings: Resources
      relatedAppMemberList: null
      relatedAppMemberList_ids: null
      relatedAppMemberList_label: null
      relatedResourceList: null
      relatedResourceList_ids: null
      relatedResourceList_label: null
    MlcAppRoleResource:
      sid: SID
      roleId: Role_id
      resourceId: Resource Id
      operation: Operation
      operation_label: null
      deltaPermissions: Delta Permissions
      priority: Priority
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      role: Role
      resource: Resource
      deltaPermissionsComponent: null
    MlcAppSection:
      appSectionId: Section ID
      appId: App Id
      name: App Name
      parentId: Parent ID
      icon: icon
      iconUrl: Icon Url
      isLock: isLock
      fixed: fixed
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      appInfo: App Info
      parent: Parent
      childSections: Children
      workSheetInfo: Work Sheet Info
      sectionFirstWorkSheetId: null
    MlcAppWorksheet:
      worksheetId: Work Sheet ID
      appId: App Id
      appSectionId: App Section Id
      modelObjectEntityId: Model Object Entity Id
      modelObjectEntityName: Model Object Entity Name
      name: App Name
      alias: Alias
      entityName: Entity Name
      icon: Icon
      iconColor: Icon Color
      iconUrl: Icon Url
      count: Count
      visibleType: Visible Type
      openApproval: Open Approval
      workflowChildTableSwitch: Workflow Child Table Switch
      navigateHide: Navigate Hide
      isWorksheetQuery: Is Worksheet Query
      isMarked: Is Marked
      controls: Controls
      dec: Dec
      allowAdd: null
      status: Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      appSection: App Section
      appInfo: App Info
      worksheetViews: Worksheet View
      worksheetRuleItems: Worksheet Rule Items
      worksheetBtns: Worksheet Btns
      worksheetPrintTemplates: Worksheet Print Templates
      worksheetSwitchs: Worksheet Switchs
      advancedSettingComponent: null
      submitSettingComponent: null
      advancedSetting: Advanced Setting
      submitSetting: Submit Setting
      template: null
      roleType: null
      projectId: null
      sheetViews: null
      developerNotes: Developer Notes
      resume: Resume
    MlcAppWorksheetButton:
      btnId: Btn Id
      worksheetId: Worksheet Id
      name: Name
      isAllView: Is All View
      displayViews: Display Views
      clickType: Click Type
      confirmMsg: Confirm Msg
      sureName: Sure Name
      cancelName: Cancel Name
      writeType: Write Type
      writeObject: Write Object
      filters: Filters
      color: Color
      icon: Icon
      iconUrl: Icon Url
      desc: Desc
      writeControls: Write Controls
      relationControl: Relation Control
      addRelationControlId: Add Relation Control Id
      workflowType: Workflow Type
      workflowId: Workflow Id
      advancedSetting: Advanced Setting
      enableConfirm: Enable Confirm
      verifyPwd: Verify Pwd
      isBatch: Is Batch
      showType: Show Type
      addRelationControl: Add Relation Control
      editAttrs: Edit Attrs
      disabled: Disabled
      status: Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      worksheet: Worksheet
      advancedSettingComponent: null
    MlcAppWorksheetPrintTemplate:
      printTemplateId: Print Template Id
      worksheetId: Worksheet Id
      name: Name
      formName: Form Name
      companyName: Company Name
      type: Type
      range: Range
      controlStyles: Control Styles
      relationStyle: Relation Style
      ownerAccount: Owner Account
      printTime: Print Time
      qrCode: QrCode
      printAccount: Print Account
      logoChecked: Logo Checked
      titleChecked: Title Checked
      companyNameChecked: Company Name Checked
      formNameChecked: Form Name Checked
      createTimeChecked: Create Time Checked
      createAccountChecked: Create Account Checked
      updateTimeChecked: Update Time Checked
      updateAccountChecked: Update Account Checked
      ownerAccountChecked: OwnerAccountChecked
      showData: Show Data
      printOption: Print Option
      shareType: Share Type
      font: Font
      allowDownloadPermission: Allow Download Permission
      advanceSettings: Advanced Settings
      approvePosition: Approve Position
      approvalIds: Approval Ids
      views: Views
      orderNumber: Order Number
      filters: Filters
      remark: Remark
      disabled: Disabled
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      worksheet: Worksheet
      advanceSettingsComponent: null
    MlcAppWorksheetRule:
      ruleId: Rule Id
      worksheetId: Worksheet Id
      name: Name
      type: Type
      checkType: Check Type
      hintType: Hint Type
      controlIds: Control Ids
      filters: Filters
      ruleItems: Rule Items
      openDrawer: Open Drawer
      disabled: Disabled
      orderNum: Order Num
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      worksheet: Worksheet
    MlcAppWorksheetSwitch:
      switchId: Btn Id
      worksheetId: Worksheet Id
      type: Type
      roleType: Role Type
      state: State
      view: View
      viewIds: View Ids
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      worksheet: Worksheet
    MlcAppWorksheetView:
      viewId: View Id
      worksheetId: Worksheet Id
      name: Name
      unRead: Un Read
      sortCid: Sort Cid
      sortType: sort Type
      coverCid: Cover Cid
      coverType: Cover Type
      customDisplay: Custom Display
      displayControls: Display Controls
      viewType: View Type
      viewType_label: null
      childType: Child Type
      viewControl: View Control
      rowHeight: Row Height
      showControlName: Show Control Name
      showControls: Show Controls
      layersName: Layers Name
      controls: Controls
      viewControls: View Controls
      controlsSorts: Controls Sorts
      navGroup: Nav Group
      filters: Filters
      fastFilters: Fast Filters
      moreSort: More Sort
      worksheetName: null
      advancedSetting: Navigate Hide
      isMarked: Is Marked
      orderNum: Order Num
      status: Status
      version: Version
      createBy: Create By
      createdAt: Created At
      updateBy: Update By
      updatedAt: Updated At
      worksheet: Worksheet
      advancedSettingComponent: null
