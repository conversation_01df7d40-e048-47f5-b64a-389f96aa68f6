entity:
  label:
    MlcAppInfo: 应用信息
    MlcAppMember: 应用成员
    MlcAppMemberRole: 成员角色中间表
    MlcAppResource: 应用资源树表
    MlcAppRole: 应用角色
    MlcAppRoleResource: 应用角色资源表
    MlcAppSection: 应用分组
    MlcAppWorksheet: 工作表
    MlcAppWorksheetButton: 工作表自定义按钮
    MlcAppWorksheetPrintTemplate: 工作表自定义打印模板
    MlcAppWorksheetRule: 工作表业务规则
    MlcAppWorksheetSwitch: 工作表开关
    MlcAppWorksheetView: 工作表视图
prop:
  label:
    MlcAppInfo:
      appDisplay: App 端显示
      appId: 应用ID
      appMembers: 关联应用角色
      appNaviDisplayType: 移动端分组展开方式
      appNaviStyle: 移动端导航方式
      appRoles: 关联应用角色
      appSections: 关联应用分组
      appStatus: 状态
      createBy: 创建人
      createType: 创建类型
      createdAt: 创建时间
      description: 应用说明
      displayIcon: 显示图标级别
      fixed: 维护状态
      gridDisplayMode: 移动端显示模式
      groupId: 分组id
      icon: 图标
      iconColor: 图标颜色
      iconUrl: 图标路径
      isLock: 锁定状态
      isMarked: 星标状态
      isNew: 新建状态
      lightColor: 背景色
      modelDefinitionId: 模型定义Id
      name: 应用名
      navColor: 导航颜色
      pcDisplay: Pc 端显示
      pcNaviStyle: pc 端导航方式
      permissionType: 权限信息，暂时写死
      projectId: null
      selectAppItmeType: 记住上次使用
      sourceType: 来源类型
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
      viewHideNavi: 查看隐藏项
      webMobileDisplay: Web 移动端显示
      workSheetItems: 关联工作表
    MlcAppMember:
      appId: 应用 ID
      appInfo: 应用信息
      appRoleMappings: 应用角色映射
      appRoleMappingsConnection: 应用角色映射
      createBy: 创建人
      createdAt: 创建时间
      memberId: 成员ID
      memberOrgUserId: 成员关联组织用户
      memberType: 角色类型
      memberType_label: 角色类型
      name: null
      relatedAppRoleList: relatedAppRoleList
      relatedAppRoleList_ids: relatedAppRoleList
      relatedAppRoleList_label: relatedAppRoleList
      status: 状态
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
    MlcAppMemberRole:
      appMember: 应用成员
      appRole: 应用角色
      createBy: 创建人
      createdAt: 创建时间
      isManager: 是否管理员
      isOwner: 是否拥有者
      isRoleCharger: 是否角色负责人
      memberId: 成员ID
      roleId: 角色ID
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
    MlcAppResource:
      childrenId: 子级ID
      childrenSet: 子级
      createBy: 创建人
      createdAt: 创建时间
      parent: 父级
      parentId: 父级ID
      permissions: 权限标识集合
      permissionsComponent: null
      relatedRoleList: relatedRoleList
      relatedRoleList_ids: relatedRoleList
      relatedRoleList_label: relatedRoleList
      resourceId: 角色工作表字段 ID
      resourceType: 资源类型
      roleMappings: 角色映射
      status: 状态
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
    MlcAppRole:
      appId: 应用 ID
      appInfo: 应用信息
      appMemberMappings: 应用成员映射
      appMemberMappingsConnection: 应用成员映射
      appSettingsEnum: 设置
      createBy: 创建人
      createdAt: 创建时间
      description: 描述
      extendAttrs: 操作标签
      generalAdd: 允许新增
      generalAttachmentDownload: 允许下载附件
      generalDiscussion: 允许讨论
      generalExport: 允许导出
      generalImport: 允许导入
      generalLogging: 允许查看日志
      generalShare: 允许分享
      generalSystemPrinting: 允许打印
      hideAppForMembers: 隐藏应用
      isDebug: 是否开启调试
      name: 名称
      notify: 是否通知
      optionalControls: 操作标签
      permissionWay: 权限定义方式
      relatedAppMemberList: relatedAppMemberList
      relatedAppMemberList_ids: relatedAppMemberList
      relatedAppMemberList_label: relatedAppMemberList
      relatedResourceList: relatedResourceList
      relatedResourceList_ids: relatedResourceList
      relatedResourceList_label: relatedResourceList
      resourceMappings: 资源映射
      roleId: 角色 ID
      roleType: 角色类型
      roleType_label: 角色类型
      sortIndex: 排序
      status: 状态
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
    MlcAppRoleResource:
      createBy: 创建人
      createdAt: 创建时间
      deltaPermissions: 变更的权限标签
      deltaPermissionsComponent: null
      operation: 权限操作类型
      operation_label: 权限操作类型
      priority: 冲突时优先级
      resource: 资源
      resourceId: 资源ID
      role: 角色
      roleId: 角色ID
      sid: 主键ID
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
    MlcAppSection:
      appId: 应用ID
      appInfo: 应用信息
      appSectionId: 应用分组ID
      childSections: 子资源
      createBy: 创建人
      createdAt: 创建时间
      fixed: 维护状态
      icon: 图标
      iconUrl: 图标路径
      isLock: 锁定状态
      name: 应用名
      parent: 父资源
      parentId: 父级ID
      sectionFirstWorkSheetId: null
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
      workSheetInfo: 关联应用组
    MlcAppWorksheet:
      advancedSetting: 高级设置
      advancedSettingComponent: null
      alias: 工作表别名
      allowAdd: 允许添加
      appId: 应用 ID
      appInfo: 应用信息
      appSection: 所属应用分组
      appSectionId: 应用分组 ID
      controls: 工作表控件
      count: 数量
      createBy: 创建人
      createdAt: 创建时间
      dec: 详细说明
      developerNotes: 开发者备注
      entityName: 记录名称
      icon: 图标
      iconColor: 图标颜色
      iconUrl: 图标路径
      isMarked: 收藏状态
      isWorksheetQuery: 是否配置工作表查询
      modelObjectEntityId: 模型对象 ID
      modelObjectEntityName: 模型对象名称
      name: 工作表名称
      navigateHide: 侧边栏状态
      openApproval: 开启审批
      projectId: null
      resume: 摘要
      roleType: 应用角色最大权限
      sheetViews: 缓存角色视图列表
      status: 状态
      submitSetting: 提交设置
      submitSettingComponent: null
      template: 模板列表
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
      visibleType: 分享状态
      workflowChildTableSwitch: 工作流子表切换
      worksheetBtns: 关联工作表按钮
      worksheetId: 工作表ID
      worksheetPrintTemplates: 关联工作表打印模板
      worksheetRuleItems: 关联工作表规则
      worksheetSwitchs: 关联工作表开发
      worksheetViews: 关联工作表视图
    MlcAppWorksheetButton:
      addRelationControl: 添加关联控件
      addRelationControlId: 新建关联记录ID
      advancedSetting: 高级设置
      advancedSettingComponent: null
      btnId: 按钮ID
      cancelName: 取消名称
      clickType: 点击类型
      color: 颜色
      confirmMsg: 确认消息
      createBy: 创建人
      createdAt: 创建时间
      desc: 描述
      disabled: 禁用
      displayViews: 显示视图
      editAttrs: 属性
      enableConfirm: 启用二次确认
      filters: 过滤器
      icon: 图标
      iconUrl: 图标URL
      isAllView: 是否全部视图
      isBatch: 是否多条数据源
      name: 名称
      relationControl: 关联记录ID
      showType: 启用按钮类型
      status: 状态
      sureName: 确认名称
      updateBy: 修改人
      updatedAt: 修改时间
      verifyPwd: 校验密码
      version: 数据版本
      workflowId: 工作流ID
      workflowType: 继续执行工作流
      worksheet: 所属工作表
      worksheetId: 工作表ID
      writeControls: 填写控件
      writeObject: 填写内容
      writeType: 填写对象类型
    MlcAppWorksheetPrintTemplate:
      advanceSettings: 高级设置
      advanceSettingsComponent: null
      allowDownloadPermission: 允许下载打印文件
      approvalIds: 审批批准编号
      approvePosition: 审批签名位置
      companyName: 公司名称
      companyNameChecked: 公司名称否打印
      controlStyles: 控件样式
      createAccountChecked: 创建者是否打印
      createBy: 创建人
      createTimeChecked: 创建时间是否打印
      createdAt: 创建时间
      disabled: 禁用
      filters: 过滤器
      font: 字体
      formName: 来自名称
      formNameChecked: 表单标题
      logoChecked: 打印 logo
      name: 名称
      orderNumber: 排序
      ownerAccount: 拥有者
      ownerAccountChecked: 拥有者是否打印
      printAccount: 打印人
      printOption: 选项字段平铺打印
      printTemplateId: 打印模板 ID
      printTime: 打印时间
      qrCode: 二维码
      range: 使用范围
      relationStyle: 关联样式
      remark: 备注
      shareType: 分享类型
      showData: 空值是否隐藏
      titleChecked: 标题是否打印
      type: 类型
      updateAccountChecked: 更新人是否打印
      updateBy: 修改人
      updateTimeChecked: 更新时间是否打印
      updatedAt: 修改时间
      version: 数据版本
      views: 视图
      worksheet: 所属工作表
      worksheetId: 工作表 ID
    MlcAppWorksheetRule:
      checkType: 检查类型
      controlIds: 控件集合
      createBy: 创建人
      createdAt: 创建时间
      disabled: 已禁用
      filters: 初始过滤
      hintType: 提示类型
      name: 工作表名称
      openDrawer: 打开抽屉
      orderNum: 排序
      ruleId: 业务规则 ID
      ruleItems: 规则项
      type: 规则类型
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
      worksheet: 所属工作表
      worksheetId: 工作表 ID
    MlcAppWorksheetSwitch:
      createBy: 创建人
      createdAt: 创建时间
      roleType: 角色类型
      state: 状态
      switchId: 按钮ID
      type: 开关类型
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
      view: 视图
      viewIds: 视图
      worksheet: 所属工作表
      worksheetId: 工作表ID
    MlcAppWorksheetView:
      advancedSetting: 高级设置
      advancedSettingComponent: null
      childType: 层级类型
      controls: 视图隐藏字段
      controlsSorts: 字段排序
      coverCid: 封面字段
      coverType: 封面类型
      createBy: 创建人
      createdAt: 创建时间
      customDisplay: 是否配置自定义显示列
      displayControls: 显示字段
      fastFilters: 快速过滤
      filters: 初始过滤
      isMarked: 收藏状态
      layersName: 层级名称
      moreSort: 更多排序
      name: 工作表名称
      navGroup: 导航分组
      orderNum: 排序
      rowHeight: 行高
      showControlName: 显示控件名称
      showControls: Web显示字段
      sortCid: 排序字段
      sortType: 排序类型
      status: 状态
      unRead: 是否已读
      updateBy: 修改人
      updatedAt: 修改时间
      version: 数据版本
      viewControl: 视图维度ID(分组ID)
      viewControls: 多表层级视图控件
      viewId: 视图 ID
      viewType: 视图类型
      viewType_label: 视图类型
      worksheet: 所属工作表
      worksheetId: 工作表 ID
      worksheetName: null
