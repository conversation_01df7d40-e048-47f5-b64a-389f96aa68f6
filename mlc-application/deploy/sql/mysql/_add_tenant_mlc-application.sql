
    alter table mlc_app_info add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_member add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_member_role add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_resource add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_role add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_role_resource add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_section add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_button add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_print_template add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_rule add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_switch add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_view add NOP_TENANT_ID VARCHAR(32) DEFAULT '0' NOT NULL;

alter table mlc_app_info drop primary key;
alter table mlc_app_info add primary key (NOP_TENANT_ID, app_id);

alter table mlc_app_member drop primary key;
alter table mlc_app_member add primary key (NOP_TENANT_ID, member_id);

alter table mlc_app_member_role drop primary key;
alter table mlc_app_member_role add primary key (NOP_TENANT_ID, member_id,role_id);

alter table mlc_app_resource drop primary key;
alter table mlc_app_resource add primary key (NOP_TENANT_ID, resource_id);

alter table mlc_app_role drop primary key;
alter table mlc_app_role add primary key (NOP_TENANT_ID, role_id);

alter table mlc_app_role_resource drop primary key;
alter table mlc_app_role_resource add primary key (NOP_TENANT_ID, sid);

alter table mlc_app_section drop primary key;
alter table mlc_app_section add primary key (NOP_TENANT_ID, app_section_id);

alter table mlc_app_worksheet drop primary key;
alter table mlc_app_worksheet add primary key (NOP_TENANT_ID, worksheet_id);

alter table mlc_app_worksheet_button drop primary key;
alter table mlc_app_worksheet_button add primary key (NOP_TENANT_ID, btn_id);

alter table mlc_app_worksheet_print_template drop primary key;
alter table mlc_app_worksheet_print_template add primary key (NOP_TENANT_ID, print_template_id);

alter table mlc_app_worksheet_rule drop primary key;
alter table mlc_app_worksheet_rule add primary key (NOP_TENANT_ID, rule_id);

alter table mlc_app_worksheet_switch drop primary key;
alter table mlc_app_worksheet_switch add primary key (NOP_TENANT_ID, switch_id);

alter table mlc_app_worksheet_view drop primary key;
alter table mlc_app_worksheet_view add primary key (NOP_TENANT_ID, view_id);

alter table mlc_app_worksheet drop constraint UK_MLC_APP_WORKSHEET_ALIAS;
alter table mlc_app_worksheet add constraint UK_MLC_APP_WORKSHEET_ALIAS
                     unique (NOP_TENANT_ID,alias);

                alter table mlc_app_worksheet_print_template drop constraint UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME;
alter table mlc_app_worksheet_print_template add constraint UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME
                     unique (NOP_TENANT_ID,name);

                
