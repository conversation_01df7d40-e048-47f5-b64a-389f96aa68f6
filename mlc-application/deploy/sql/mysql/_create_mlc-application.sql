
CREATE TABLE mlc_app_info(
  app_id VARCHAR(32) NOT NULL    COMMENT '应用ID',
  model_definition_id VARCHAR(32) NOT NULL    COMMENT '模型定义Id',
  name VARCHAR(30) NOT NULL    COMMENT '应用名',
  icon VARCHAR(50) default '0_lego'  NULL    COMMENT '图标',
  icon_color VARCHAR(10) default '#2296F3'  NULL    COMMENT '图标颜色',
  icon_url VARCHAR(100) default 'https://fp1.mingdaoyun.cn/customIcon/0_lego.svg'  NULL    COMMENT '图标路径',
  create_type INTEGER default 0  NULL    COMMENT '创建类型',
  source_type INTEGER default 1  NULL    COMMENT '来源类型',
  nav_color VARCHAR(30) default '#2296F3'  NULL    COMMENT '导航颜色',
  light_color VARCHAR(30) NULL    COMMENT '背景色',
  group_id VARCHAR(32) NULL    COMMENT '分组id',
  is_lock BOOLEAN default 0  NULL    COMMENT '锁定状态',
  is_new BOOLEAN default 0  NULL    COMMENT '新建状态',
  fixed BOOLEAN default 0  NULL    COMMENT '维护状态',
  pc_display BOOLEAN default 0  NULL    COMMENT 'Pc 端显示',
  app_display BOOLEAN default 0  NULL    COMMENT 'App 端显示',
  pc_navi_style INTEGER default 0  NULL    COMMENT 'pc 端导航方式',
  display_icon VARCHAR(3) default '011'  NULL    COMMENT '显示图标级别',
  select_app_itme_type INTEGER default 2  NULL    COMMENT '记住上次使用',
  web_mobile_display BOOLEAN default 0  NULL    COMMENT 'Web 移动端显示',
  app_navi_style INTEGER default 0  NULL    COMMENT '移动端导航方式',
  grid_display_mode INTEGER default 0  NULL    COMMENT '移动端显示模式',
  app_navi_display_type INTEGER default 0  NULL    COMMENT '移动端分组展开方式',
  view_hide_navi BOOLEAN default 1  NULL    COMMENT '查看隐藏项',
  is_marked BOOLEAN default 0  NULL    COMMENT '星标状态',
  description VARCHAR(2000) NULL    COMMENT '应用说明',
  app_status INTEGER default 1  NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_info primary key (app_id)
);

CREATE TABLE mlc_app_resource(
  resource_id VARCHAR(32) NOT NULL    COMMENT '角色工作表字段 ID',
  parent_id VARCHAR(32) NULL    COMMENT '父级ID',
  children_id VARCHAR(50) NOT NULL    COMMENT '子级ID',
  resource_type LONGTEXT NOT NULL    COMMENT '资源类型',
  permissions VARCHAR(1000) NOT NULL    COMMENT '权限标识集合',
  status INTEGER default 1  NOT NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_resource primary key (resource_id)
);

CREATE TABLE mlc_app_section(
  app_section_id VARCHAR(32) NOT NULL    COMMENT '应用分组ID',
  app_id VARCHAR(32) NOT NULL    COMMENT '应用ID',
  name VARCHAR(30) NOT NULL    COMMENT '应用名',
  parent_id VARCHAR(50) NULL    COMMENT '父级ID',
  icon VARCHAR(30) NULL    COMMENT '图标',
  icon_url VARCHAR(100) NULL    COMMENT '图标路径',
  is_lock BOOLEAN default 0  NULL    COMMENT '锁定状态',
  fixed BOOLEAN default 0  NULL    COMMENT '维护状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_section primary key (app_section_id)
);

CREATE TABLE mlc_app_member(
  member_id VARCHAR(32) NOT NULL    COMMENT '成员ID',
  app_id VARCHAR(32) NOT NULL    COMMENT '应用 ID',
  member_org_user_id VARCHAR(32) NOT NULL    COMMENT '成员关联组织用户',
  member_type INTEGER NOT NULL    COMMENT '角色类型',
  status INTEGER default 1  NOT NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_member primary key (member_id)
);

CREATE TABLE mlc_app_role(
  role_id VARCHAR(32) NOT NULL    COMMENT '角色 ID',
  app_id VARCHAR(32) NOT NULL    COMMENT '应用 ID',
  name VARCHAR(20) NOT NULL    COMMENT '名称',
  role_type INTEGER NOT NULL    COMMENT '角色类型',
  app_settings_enum INTEGER default 1  NOT NULL    COMMENT '设置',
  notify BOOLEAN default 0  NOT NULL    COMMENT '是否通知',
  is_debug BOOLEAN default 0  NOT NULL    COMMENT '是否开启调试',
  hide_app_for_members BOOLEAN default 0  NOT NULL    COMMENT '隐藏应用',
  permission_way INTEGER NULL    COMMENT '权限定义方式',
  extend_attrs VARCHAR(100) NULL    COMMENT '操作标签',
  optional_controls VARCHAR(100) NULL    COMMENT '操作标签',
  general_add BOOLEAN NULL    COMMENT '允许新增',
  general_share BOOLEAN NULL    COMMENT '允许分享',
  general_import BOOLEAN NULL    COMMENT '允许导入',
  general_export BOOLEAN NULL    COMMENT '允许导出',
  general_discussion BOOLEAN NULL    COMMENT '允许讨论',
  general_system_printing BOOLEAN NULL    COMMENT '允许打印',
  general_attachment_download BOOLEAN NULL    COMMENT '允许下载附件',
  general_logging BOOLEAN NULL    COMMENT '允许查看日志',
  sort_index INTEGER NULL    COMMENT '排序',
  description VARCHAR(200) NULL    COMMENT '描述',
  status INTEGER default 1  NOT NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_role primary key (role_id)
);

CREATE TABLE mlc_app_worksheet(
  worksheet_id VARCHAR(32) NOT NULL    COMMENT '工作表ID',
  app_id VARCHAR(32) NOT NULL    COMMENT '应用 ID',
  app_section_id VARCHAR(32) NOT NULL    COMMENT '应用分组 ID',
  model_object_entity_id VARCHAR(32) NOT NULL    COMMENT '模型对象 ID',
  model_object_entity_name VARCHAR(32) NOT NULL    COMMENT '模型对象名称',
  name VARCHAR(30) NOT NULL    COMMENT '工作表名称',
  alias VARCHAR(30) NULL    COMMENT '工作表别名',
  entity_name VARCHAR(30) default '记录'  NULL    COMMENT '记录名称',
  icon VARCHAR(30) default 'table'  NULL    COMMENT '图标',
  icon_color VARCHAR(30) default '#2296F3'  NULL    COMMENT '图标颜色',
  icon_url VARCHAR(100) default 'https://fp1.mingdaoyun.cn/customIcon/table.svg'  NULL    COMMENT '图标路径',
  count INTEGER default 0  NULL    COMMENT '数量',
  visible_type INTEGER default 1  NULL    COMMENT '分享状态',
  open_approval BOOLEAN default 0  NULL    COMMENT '开启审批',
  workflow_child_table_switch BOOLEAN default 0  NULL    COMMENT '工作流子表切换',
  navigate_hide BOOLEAN default 0  NULL    COMMENT '侧边栏状态',
  is_worksheet_query BOOLEAN default 0  NULL    COMMENT '是否配置工作表查询',
  is_marked BOOLEAN default 0  NULL    COMMENT '收藏状态',
  controls TEXT NULL    COMMENT '控件',
  advanced_setting VARCHAR(1000) default '{}'  NULL    COMMENT '高级设置',
  submit_setting VARCHAR(1000) default '{}'  NULL    COMMENT '提交设置',
  developer_notes VARCHAR(200) NULL    COMMENT '开发者备注',
  resume VARCHAR(80) NULL    COMMENT '摘要',
  `dec` VARCHAR(500) NULL    COMMENT '详细说明',
  status INTEGER default 1  NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint UK_MLC_APP_WORKSHEET_ALIAS unique (alias),
  constraint PK_mlc_app_worksheet primary key (worksheet_id)
);

CREATE TABLE mlc_app_member_role(
  member_id VARCHAR(32) NOT NULL    COMMENT '成员ID',
  role_id VARCHAR(32) NOT NULL    COMMENT '角色ID',
  is_owner BOOLEAN default 0  NOT NULL    COMMENT '是否拥有者',
  is_manager BOOLEAN default 0  NOT NULL    COMMENT '是否管理员',
  is_role_charger BOOLEAN default 0  NOT NULL    COMMENT '是否角色负责人',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_member_role primary key (member_id,role_id)
);

CREATE TABLE mlc_app_role_resource(
  sid VARCHAR(32) NOT NULL    COMMENT '主键ID',
  role_id VARCHAR(32) NOT NULL    COMMENT '角色ID',
  resource_id VARCHAR(32) NOT NULL    COMMENT '资源ID',
  operation LONGTEXT NOT NULL    COMMENT '权限操作类型',
  delta_permissions VARCHAR(1000) NOT NULL    COMMENT '变更的权限标签',
  priority INTEGER NULL    COMMENT '冲突时优先级',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_role_resource primary key (sid)
);

CREATE TABLE mlc_app_worksheet_view(
  view_id VARCHAR(32) NOT NULL    COMMENT '视图 ID',
  worksheet_id VARCHAR(32) NOT NULL    COMMENT '工作表 ID',
  name VARCHAR(30) NOT NULL    COMMENT '工作表名称',
  un_read BOOLEAN default 0  NULL    COMMENT '是否已读',
  sort_cid VARCHAR(40) NULL    COMMENT '排序字段',
  sort_type INTEGER NULL    COMMENT '排序类型',
  cover_cid VARCHAR(32) NULL    COMMENT '封面字段',
  cover_type INTEGER default 0  NULL    COMMENT '封面类型',
  custom_display BOOLEAN default 0  NULL    COMMENT '是否配置自定义显示列',
  display_controls VARCHAR(500) NULL    COMMENT '显示字段',
  view_type INTEGER default 0  NULL    COMMENT '视图类型',
  child_type INTEGER default 0  NULL    COMMENT '层级类型',
  view_control VARCHAR(100) NULL    COMMENT '视图维度ID(分组ID)',
  row_height INTEGER default 0  NULL    COMMENT '行高',
  show_control_name BOOLEAN default 0  NULL    COMMENT '显示控件名称',
  show_controls VARCHAR(500) NULL    COMMENT 'Web显示字段',
  layers_name VARCHAR(500) NULL    COMMENT '层级名称',
  controls VARCHAR(1000) NULL    COMMENT '视图隐藏字段',
  view_controls VARCHAR(1000) NULL    COMMENT '多表层级视图控件',
  controls_sorts VARCHAR(1000) NULL    COMMENT '字段排序',
  nav_group VARCHAR(1000) default '[]'  NULL    COMMENT '导航分组',
  filters VARCHAR(1000) default '[]'  NULL    COMMENT '初始过滤',
  fast_filters VARCHAR(1000) default '[]'  NULL    COMMENT '快速过滤',
  more_sort VARCHAR(2000) default '[]'  NULL    COMMENT '更多排序',
  advanced_setting VARCHAR(1000) default '{}'  NULL    COMMENT '高级设置',
  is_marked BOOLEAN default 0  NULL    COMMENT '收藏状态',
  order_num INTEGER NULL    COMMENT '排序',
  status INTEGER default 1  NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_worksheet_view primary key (view_id)
);

CREATE TABLE mlc_app_worksheet_rule(
  rule_id VARCHAR(32) NOT NULL    COMMENT '业务规则 ID',
  worksheet_id VARCHAR(32) NOT NULL    COMMENT '工作表 ID',
  name VARCHAR(30) NOT NULL    COMMENT '工作表名称',
  type INTEGER NOT NULL    COMMENT '规则类型',
  check_type INTEGER NULL    COMMENT '检查类型',
  hint_type INTEGER NULL    COMMENT '提示类型',
  control_ids VARCHAR(500) NULL    COMMENT '控件集合',
  filters VARCHAR(1000) default '[]'  NOT NULL    COMMENT '初始过滤',
  rule_items VARCHAR(1000) default '[]'  NOT NULL    COMMENT '规则项',
  open_drawer BOOLEAN default 0  NULL    COMMENT '打开抽屉',
  disabled BOOLEAN default 0  NOT NULL    COMMENT '已禁用',
  order_num INTEGER NULL    COMMENT '排序',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_worksheet_rule primary key (rule_id)
);

CREATE TABLE mlc_app_worksheet_button(
  btn_id VARCHAR(32) NOT NULL    COMMENT '按钮ID',
  worksheet_id VARCHAR(32) NOT NULL    COMMENT '工作表ID',
  name VARCHAR(20) NOT NULL    COMMENT '名称',
  is_all_view INTEGER NULL    COMMENT '是否全部视图',
  display_views VARCHAR(500) NULL    COMMENT '显示视图',
  click_type INTEGER NULL    COMMENT '点击类型',
  confirm_msg VARCHAR(30) NULL    COMMENT '确认消息',
  sure_name VARCHAR(10) NULL    COMMENT '确认名称',
  cancel_name VARCHAR(10) NULL    COMMENT '取消名称',
  write_type INTEGER NULL    COMMENT '填写对象类型',
  write_object INTEGER NULL    COMMENT '填写内容',
  write_controls VARCHAR(500) NULL    COMMENT '填写控件',
  relation_control VARCHAR(36) NULL    COMMENT '关联记录ID',
  add_relation_control_id VARCHAR(36) NULL    COMMENT '新建关联记录ID',
  workflow_type INTEGER NULL    COMMENT '继续执行工作流',
  workflow_id VARCHAR(36) NULL    COMMENT '工作流ID',
  filters VARCHAR(1000) default '[]'  NULL    COMMENT '过滤器',
  color VARCHAR(1000) default '#2296F3'  NULL    COMMENT '颜色',
  icon VARCHAR(1000) default 'done_2'  NULL    COMMENT '图标',
  icon_url VARCHAR(100) default 'https://fp1.mingdaoyun.cn/custom_icon/PNG/done_2.png'  NULL    COMMENT '图标URL',
  `desc` VARCHAR(200) NULL    COMMENT '描述',
  advanced_setting VARCHAR(1000) default '{}'  NULL    COMMENT '高级设置',
  enable_confirm BOOLEAN default 0  NULL    COMMENT '启用二次确认',
  verify_pwd BOOLEAN default 0  NULL    COMMENT '校验密码',
  is_batch BOOLEAN default 0  NULL    COMMENT '是否多条数据源',
  show_type INTEGER default 1  NULL    COMMENT '启用按钮类型',
  add_relation_control VARCHAR(200) NULL    COMMENT '添加关联控件',
  edit_attrs VARCHAR(200) NULL    COMMENT '属性',
  disabled BOOLEAN default 0  NULL    COMMENT '禁用',
  status INTEGER default 1  NULL    COMMENT '状态',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_worksheet_button primary key (btn_id)
);

CREATE TABLE mlc_app_worksheet_print_template(
  print_template_id VARCHAR(32) NOT NULL    COMMENT '打印模板 ID',
  worksheet_id VARCHAR(32) NOT NULL    COMMENT '工作表 ID',
  name VARCHAR(30) NOT NULL    COMMENT '名称',
  form_name VARCHAR(30) NULL    COMMENT '来自名称',
  company_name VARCHAR(30) NULL    COMMENT '公司名称',
  type INTEGER default 0  NULL    COMMENT '类型',
  `range` INTEGER default 1  NULL    COMMENT '使用范围',
  control_styles VARCHAR(255) default '[]'  NULL    COMMENT '控件样式',
  relation_style VARCHAR(255) default '[]'  NULL    COMMENT '关联样式',
  owner_account VARCHAR(30) NULL    COMMENT '拥有者',
  print_time BOOLEAN default 1  NULL    COMMENT '打印时间',
  qr_code BOOLEAN default 1  NULL    COMMENT '二维码',
  print_account BOOLEAN default 1  NULL    COMMENT '打印人',
  logo_checked BOOLEAN default 1  NULL    COMMENT '打印 logo',
  title_checked BOOLEAN default 1  NULL    COMMENT '标题是否打印',
  company_name_checked BOOLEAN default 0  NULL    COMMENT '公司名称否打印',
  form_name_checked BOOLEAN default 1  NULL    COMMENT '表单标题',
  create_time_checked BOOLEAN default 0  NULL    COMMENT '创建时间是否打印',
  create_account_checked BOOLEAN default 0  NULL    COMMENT '创建者是否打印',
  update_time_checked BOOLEAN default 0  NULL    COMMENT '更新时间是否打印',
  update_account_checked BOOLEAN default 0  NULL    COMMENT '更新人是否打印',
  owner_account_checked BOOLEAN default 0  NULL    COMMENT '拥有者是否打印',
  show_data BOOLEAN default 0  NULL    COMMENT '空值是否隐藏',
  print_option BOOLEAN default 0  NULL    COMMENT '选项字段平铺打印',
  share_type INTEGER default 0  NULL    COMMENT '分享类型',
  font INTEGER NULL    COMMENT '字体',
  allow_download_permission INTEGER default 0  NULL    COMMENT '允许下载打印文件',
  advance_settings VARCHAR(1000) default '[
            {
                "key": "atta_style",
                "value": "{}"
            }
        ]'  NULL    COMMENT '高级设置',
  approve_position INTEGER default 0  NULL    COMMENT '审批签名位置',
  approval_ids VARCHAR(100) default '[]'  NULL    COMMENT '审批批准编号',
  views VARCHAR(500) NULL    COMMENT '视图',
  order_number VARCHAR(10) default '[]'  NULL    COMMENT '排序',
  filters VARCHAR(1000) default '[]'  NULL    COMMENT '过滤器',
  remark LONGTEXT NULL    COMMENT '备注',
  disabled BOOLEAN default 0  NULL    COMMENT '禁用',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME unique (name),
  constraint PK_mlc_app_worksheet_print_template primary key (print_template_id)
);

CREATE TABLE mlc_app_worksheet_switch(
  switch_id VARCHAR(32) NOT NULL    COMMENT '按钮ID',
  worksheet_id VARCHAR(32) NOT NULL    COMMENT '工作表ID',
  type INTEGER NOT NULL    COMMENT '开关类型',
  role_type INTEGER NOT NULL    COMMENT '角色类型',
  state BOOLEAN NOT NULL    COMMENT '状态',
  view VARCHAR(100) NULL    COMMENT '视图',
  view_ids VARCHAR(100) NULL    COMMENT '视图',
  version INTEGER NOT NULL    COMMENT '数据版本',
  create_by VARCHAR(50) NOT NULL    COMMENT '创建人',
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '创建时间',
  update_by VARCHAR(50) NOT NULL    COMMENT '修改人',
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)  NOT NULL    COMMENT '修改时间',
  constraint PK_mlc_app_worksheet_switch primary key (switch_id)
);


   ALTER TABLE mlc_app_info COMMENT '应用信息';
                
   ALTER TABLE mlc_app_resource COMMENT '应用资源树表';
                
   ALTER TABLE mlc_app_section COMMENT '应用分组';
                
   ALTER TABLE mlc_app_member COMMENT '应用成员';
                
   ALTER TABLE mlc_app_role COMMENT '应用角色';
                
   ALTER TABLE mlc_app_worksheet COMMENT '工作表';
                
   ALTER TABLE mlc_app_member_role COMMENT '成员角色中间表';
                
   ALTER TABLE mlc_app_role_resource COMMENT '应用角色资源表';
                
   ALTER TABLE mlc_app_worksheet_view COMMENT '工作表视图';
                
   ALTER TABLE mlc_app_worksheet_rule COMMENT '工作表业务规则';
                
   ALTER TABLE mlc_app_worksheet_button COMMENT '工作表自定义按钮';
                
   ALTER TABLE mlc_app_worksheet_print_template COMMENT '工作表自定义打印模板';
                
   ALTER TABLE mlc_app_worksheet_switch COMMENT '工作表开关';
                
