
CREATE TABLE mlc_app_info(
  app_id VARCHAR2(32) NOT NULL ,
  model_definition_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(30) NOT NULL ,
  icon VARCHAR2(50) default '0_lego'   ,
  icon_color VARCHAR2(10) default '#2296F3'   ,
  icon_url VARCHAR2(100) default 'https://fp1.mingdaoyun.cn/customIcon/0_lego.svg'   ,
  create_type INTEGER default 0   ,
  source_type INTEGER default 1   ,
  nav_color VARCHAR2(30) default '#2296F3'   ,
  light_color VARCHAR2(30)  ,
  group_id VARCHAR2(32)  ,
  is_lock CHAR(1) default 0   ,
  is_new CHAR(1) default 0   ,
  fixed CHAR(1) default 0   ,
  pc_display CHAR(1) default 0   ,
  app_display CHAR(1) default 0   ,
  pc_navi_style INTEGER default 0   ,
  display_icon VARCHAR2(3) default '011'   ,
  select_app_itme_type INTEGER default 2   ,
  web_mobile_display CHAR(1) default 0   ,
  app_navi_style INTEGER default 0   ,
  grid_display_mode INTEGER default 0   ,
  app_navi_display_type INTEGER default 0   ,
  view_hide_navi CHAR(1) default 1   ,
  is_marked CHAR(1) default 0   ,
  description VARCHAR2(2000)  ,
  app_status INTEGER default 1   ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_info primary key (app_id)
);

CREATE TABLE mlc_app_resource(
  resource_id VARCHAR2(32) NOT NULL ,
  parent_id VARCHAR2(32)  ,
  children_id VARCHAR2(50) NOT NULL ,
  resource_type CLOB NOT NULL ,
  permissions VARCHAR2(1000) NOT NULL ,
  status INTEGER default 1  NOT NULL ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_resource primary key (resource_id)
);

CREATE TABLE mlc_app_section(
  app_section_id VARCHAR2(32) NOT NULL ,
  app_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(30) NOT NULL ,
  parent_id VARCHAR2(50)  ,
  icon VARCHAR2(30)  ,
  icon_url VARCHAR2(100)  ,
  is_lock CHAR(1) default 0   ,
  fixed CHAR(1) default 0   ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_section primary key (app_section_id)
);

CREATE TABLE mlc_app_member(
  member_id VARCHAR2(32) NOT NULL ,
  app_id VARCHAR2(32) NOT NULL ,
  member_org_user_id VARCHAR2(32) NOT NULL ,
  member_type INTEGER NOT NULL ,
  status INTEGER default 1  NOT NULL ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_member primary key (member_id)
);

CREATE TABLE mlc_app_role(
  role_id VARCHAR2(32) NOT NULL ,
  app_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(20) NOT NULL ,
  role_type INTEGER NOT NULL ,
  app_settings_enum INTEGER default 1  NOT NULL ,
  notify CHAR(1) default 0  NOT NULL ,
  is_debug CHAR(1) default 0  NOT NULL ,
  hide_app_for_members CHAR(1) default 0  NOT NULL ,
  permission_way INTEGER  ,
  extend_attrs VARCHAR2(100)  ,
  optional_controls VARCHAR2(100)  ,
  general_add CHAR(1)  ,
  general_share CHAR(1)  ,
  general_import CHAR(1)  ,
  general_export CHAR(1)  ,
  general_discussion CHAR(1)  ,
  general_system_printing CHAR(1)  ,
  general_attachment_download CHAR(1)  ,
  general_logging CHAR(1)  ,
  sort_index INTEGER  ,
  description VARCHAR2(200)  ,
  status INTEGER default 1  NOT NULL ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_role primary key (role_id)
);

CREATE TABLE mlc_app_worksheet(
  worksheet_id VARCHAR2(32) NOT NULL ,
  app_id VARCHAR2(32) NOT NULL ,
  app_section_id VARCHAR2(32) NOT NULL ,
  model_object_entity_id VARCHAR2(32) NOT NULL ,
  model_object_entity_name VARCHAR2(32) NOT NULL ,
  name VARCHAR2(30) NOT NULL ,
  alias VARCHAR2(30)  ,
  entity_name VARCHAR2(30) default '记录'   ,
  icon VARCHAR2(30) default 'table'   ,
  icon_color VARCHAR2(30) default '#2296F3'   ,
  icon_url VARCHAR2(100) default 'https://fp1.mingdaoyun.cn/customIcon/table.svg'   ,
  count INTEGER default 0   ,
  visible_type INTEGER default 1   ,
  open_approval CHAR(1) default 0   ,
  workflow_child_table_switch CHAR(1) default 0   ,
  navigate_hide CHAR(1) default 0   ,
  is_worksheet_query CHAR(1) default 0   ,
  is_marked CHAR(1) default 0   ,
  controls CLOB  ,
  advanced_setting VARCHAR2(1000) default '{}'   ,
  submit_setting VARCHAR2(1000) default '{}'   ,
  developer_notes VARCHAR2(200)  ,
  resume VARCHAR2(80)  ,
  dec VARCHAR2(500)  ,
  status INTEGER default 1   ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint UK_MLC_APP_WORKSHEET_ALIAS unique (alias),
  constraint PK_mlc_app_worksheet primary key (worksheet_id)
);

CREATE TABLE mlc_app_member_role(
  member_id VARCHAR2(32) NOT NULL ,
  role_id VARCHAR2(32) NOT NULL ,
  is_owner CHAR(1) default 0  NOT NULL ,
  is_manager CHAR(1) default 0  NOT NULL ,
  is_role_charger CHAR(1) default 0  NOT NULL ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_member_role primary key (member_id,role_id)
);

CREATE TABLE mlc_app_role_resource(
  sid VARCHAR2(32) NOT NULL ,
  role_id VARCHAR2(32) NOT NULL ,
  resource_id VARCHAR2(32) NOT NULL ,
  operation CLOB NOT NULL ,
  delta_permissions VARCHAR2(1000) NOT NULL ,
  priority INTEGER  ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_role_resource primary key (sid)
);

CREATE TABLE mlc_app_worksheet_view(
  view_id VARCHAR2(32) NOT NULL ,
  worksheet_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(30) NOT NULL ,
  un_read CHAR(1) default 0   ,
  sort_cid VARCHAR2(40)  ,
  sort_type INTEGER  ,
  cover_cid VARCHAR2(32)  ,
  cover_type INTEGER default 0   ,
  custom_display CHAR(1) default 0   ,
  display_controls VARCHAR2(500)  ,
  view_type INTEGER default 0   ,
  child_type INTEGER default 0   ,
  view_control VARCHAR2(100)  ,
  row_height INTEGER default 0   ,
  show_control_name CHAR(1) default 0   ,
  show_controls VARCHAR2(500)  ,
  layers_name VARCHAR2(500)  ,
  controls VARCHAR2(1000)  ,
  view_controls VARCHAR2(1000)  ,
  controls_sorts VARCHAR2(1000)  ,
  nav_group VARCHAR2(1000) default '[]'   ,
  filters VARCHAR2(1000) default '[]'   ,
  fast_filters VARCHAR2(1000) default '[]'   ,
  more_sort VARCHAR2(2000) default '[]'   ,
  advanced_setting VARCHAR2(1000) default '{}'   ,
  is_marked CHAR(1) default 0   ,
  order_num INTEGER  ,
  status INTEGER default 1   ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_worksheet_view primary key (view_id)
);

CREATE TABLE mlc_app_worksheet_rule(
  rule_id VARCHAR2(32) NOT NULL ,
  worksheet_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(30) NOT NULL ,
  type INTEGER NOT NULL ,
  check_type INTEGER  ,
  hint_type INTEGER  ,
  control_ids VARCHAR2(500)  ,
  filters VARCHAR2(1000) default '[]'  NOT NULL ,
  rule_items VARCHAR2(1000) default '[]'  NOT NULL ,
  open_drawer CHAR(1) default 0   ,
  disabled CHAR(1) default 0  NOT NULL ,
  order_num INTEGER  ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_worksheet_rule primary key (rule_id)
);

CREATE TABLE mlc_app_worksheet_button(
  btn_id VARCHAR2(32) NOT NULL ,
  worksheet_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(20) NOT NULL ,
  is_all_view INTEGER  ,
  display_views VARCHAR2(500)  ,
  click_type INTEGER  ,
  confirm_msg VARCHAR2(30)  ,
  sure_name VARCHAR2(10)  ,
  cancel_name VARCHAR2(10)  ,
  write_type INTEGER  ,
  write_object INTEGER  ,
  write_controls VARCHAR2(500)  ,
  relation_control VARCHAR2(36)  ,
  add_relation_control_id VARCHAR2(36)  ,
  workflow_type INTEGER  ,
  workflow_id VARCHAR2(36)  ,
  filters VARCHAR2(1000) default '[]'   ,
  color VARCHAR2(1000) default '#2296F3'   ,
  icon VARCHAR2(1000) default 'done_2'   ,
  icon_url VARCHAR2(100) default 'https://fp1.mingdaoyun.cn/custom_icon/PNG/done_2.png'   ,
  "desc" VARCHAR2(200)  ,
  advanced_setting VARCHAR2(1000) default '{}'   ,
  enable_confirm CHAR(1) default 0   ,
  verify_pwd CHAR(1) default 0   ,
  is_batch CHAR(1) default 0   ,
  show_type INTEGER default 1   ,
  add_relation_control VARCHAR2(200)  ,
  edit_attrs VARCHAR2(200)  ,
  disabled CHAR(1) default 0   ,
  status INTEGER default 1   ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_worksheet_button primary key (btn_id)
);

CREATE TABLE mlc_app_worksheet_print_template(
  print_template_id VARCHAR2(32) NOT NULL ,
  worksheet_id VARCHAR2(32) NOT NULL ,
  name VARCHAR2(30) NOT NULL ,
  form_name VARCHAR2(30)  ,
  company_name VARCHAR2(30)  ,
  type INTEGER default 0   ,
  range INTEGER default 1   ,
  control_styles VARCHAR2(255) default '[]'   ,
  relation_style VARCHAR2(255) default '[]'   ,
  owner_account VARCHAR2(30)  ,
  print_time CHAR(1) default 1   ,
  qr_code CHAR(1) default 1   ,
  print_account CHAR(1) default 1   ,
  logo_checked CHAR(1) default 1   ,
  title_checked CHAR(1) default 1   ,
  company_name_checked CHAR(1) default 0   ,
  form_name_checked CHAR(1) default 1   ,
  create_time_checked CHAR(1) default 0   ,
  create_account_checked CHAR(1) default 0   ,
  update_time_checked CHAR(1) default 0   ,
  update_account_checked CHAR(1) default 0   ,
  owner_account_checked CHAR(1) default 0   ,
  show_data CHAR(1) default 0   ,
  print_option CHAR(1) default 0   ,
  share_type INTEGER default 0   ,
  font INTEGER  ,
  allow_download_permission INTEGER default 0   ,
  advance_settings VARCHAR2(1000) default '[
            {
                "key": "atta_style",
                "value": "{}"
            }
        ]'   ,
  approve_position INTEGER default 0   ,
  approval_ids VARCHAR2(100) default '[]'   ,
  views VARCHAR2(500)  ,
  order_number VARCHAR2(10) default '[]'   ,
  filters VARCHAR2(1000) default '[]'   ,
  remark CLOB  ,
  disabled CHAR(1) default 0   ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME unique (name),
  constraint PK_mlc_app_worksheet_print_template primary key (print_template_id)
);

CREATE TABLE mlc_app_worksheet_switch(
  switch_id VARCHAR2(32) NOT NULL ,
  worksheet_id VARCHAR2(32) NOT NULL ,
  type INTEGER NOT NULL ,
  role_type INTEGER NOT NULL ,
  state CHAR(1) NOT NULL ,
  "view" VARCHAR2(100)  ,
  view_ids VARCHAR2(100)  ,
  version INTEGER NOT NULL ,
  create_by VARCHAR2(50) NOT NULL ,
  created_at TIMESTAMP NOT NULL ,
  update_by VARCHAR2(50) NOT NULL ,
  updated_at TIMESTAMP NOT NULL ,
  constraint PK_mlc_app_worksheet_switch primary key (switch_id)
);


      COMMENT ON TABLE mlc_app_info IS '应用信息';
                
      COMMENT ON COLUMN mlc_app_info.app_id IS '应用ID';
                    
      COMMENT ON COLUMN mlc_app_info.model_definition_id IS '模型定义Id';
                    
      COMMENT ON COLUMN mlc_app_info.name IS '应用名';
                    
      COMMENT ON COLUMN mlc_app_info.icon IS '图标';
                    
      COMMENT ON COLUMN mlc_app_info.icon_color IS '图标颜色';
                    
      COMMENT ON COLUMN mlc_app_info.icon_url IS '图标路径';
                    
      COMMENT ON COLUMN mlc_app_info.create_type IS '创建类型';
                    
      COMMENT ON COLUMN mlc_app_info.source_type IS '来源类型';
                    
      COMMENT ON COLUMN mlc_app_info.nav_color IS '导航颜色';
                    
      COMMENT ON COLUMN mlc_app_info.light_color IS '背景色';
                    
      COMMENT ON COLUMN mlc_app_info.group_id IS '分组id';
                    
      COMMENT ON COLUMN mlc_app_info.is_lock IS '锁定状态';
                    
      COMMENT ON COLUMN mlc_app_info.is_new IS '新建状态';
                    
      COMMENT ON COLUMN mlc_app_info.fixed IS '维护状态';
                    
      COMMENT ON COLUMN mlc_app_info.pc_display IS 'Pc 端显示';
                    
      COMMENT ON COLUMN mlc_app_info.app_display IS 'App 端显示';
                    
      COMMENT ON COLUMN mlc_app_info.pc_navi_style IS 'pc 端导航方式';
                    
      COMMENT ON COLUMN mlc_app_info.display_icon IS '显示图标级别';
                    
      COMMENT ON COLUMN mlc_app_info.select_app_itme_type IS '记住上次使用';
                    
      COMMENT ON COLUMN mlc_app_info.web_mobile_display IS 'Web 移动端显示';
                    
      COMMENT ON COLUMN mlc_app_info.app_navi_style IS '移动端导航方式';
                    
      COMMENT ON COLUMN mlc_app_info.grid_display_mode IS '移动端显示模式';
                    
      COMMENT ON COLUMN mlc_app_info.app_navi_display_type IS '移动端分组展开方式';
                    
      COMMENT ON COLUMN mlc_app_info.view_hide_navi IS '查看隐藏项';
                    
      COMMENT ON COLUMN mlc_app_info.is_marked IS '星标状态';
                    
      COMMENT ON COLUMN mlc_app_info.description IS '应用说明';
                    
      COMMENT ON COLUMN mlc_app_info.app_status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_info.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_info.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_info.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_info.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_info.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_resource IS '应用资源树表';
                
      COMMENT ON COLUMN mlc_app_resource.resource_id IS '角色工作表字段 ID';
                    
      COMMENT ON COLUMN mlc_app_resource.parent_id IS '父级ID';
                    
      COMMENT ON COLUMN mlc_app_resource.children_id IS '子级ID';
                    
      COMMENT ON COLUMN mlc_app_resource.resource_type IS '资源类型';
                    
      COMMENT ON COLUMN mlc_app_resource.permissions IS '权限标识集合';
                    
      COMMENT ON COLUMN mlc_app_resource.status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_resource.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_resource.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_resource.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_resource.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_resource.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_section IS '应用分组';
                
      COMMENT ON COLUMN mlc_app_section.app_section_id IS '应用分组ID';
                    
      COMMENT ON COLUMN mlc_app_section.app_id IS '应用ID';
                    
      COMMENT ON COLUMN mlc_app_section.name IS '应用名';
                    
      COMMENT ON COLUMN mlc_app_section.parent_id IS '父级ID';
                    
      COMMENT ON COLUMN mlc_app_section.icon IS '图标';
                    
      COMMENT ON COLUMN mlc_app_section.icon_url IS '图标路径';
                    
      COMMENT ON COLUMN mlc_app_section.is_lock IS '锁定状态';
                    
      COMMENT ON COLUMN mlc_app_section.fixed IS '维护状态';
                    
      COMMENT ON COLUMN mlc_app_section.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_section.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_section.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_section.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_section.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_member IS '应用成员';
                
      COMMENT ON COLUMN mlc_app_member.member_id IS '成员ID';
                    
      COMMENT ON COLUMN mlc_app_member.app_id IS '应用 ID';
                    
      COMMENT ON COLUMN mlc_app_member.member_org_user_id IS '成员关联组织用户';
                    
      COMMENT ON COLUMN mlc_app_member.member_type IS '角色类型';
                    
      COMMENT ON COLUMN mlc_app_member.status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_member.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_member.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_member.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_member.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_member.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_role IS '应用角色';
                
      COMMENT ON COLUMN mlc_app_role.role_id IS '角色 ID';
                    
      COMMENT ON COLUMN mlc_app_role.app_id IS '应用 ID';
                    
      COMMENT ON COLUMN mlc_app_role.name IS '名称';
                    
      COMMENT ON COLUMN mlc_app_role.role_type IS '角色类型';
                    
      COMMENT ON COLUMN mlc_app_role.app_settings_enum IS '设置';
                    
      COMMENT ON COLUMN mlc_app_role.notify IS '是否通知';
                    
      COMMENT ON COLUMN mlc_app_role.is_debug IS '是否开启调试';
                    
      COMMENT ON COLUMN mlc_app_role.hide_app_for_members IS '隐藏应用';
                    
      COMMENT ON COLUMN mlc_app_role.permission_way IS '权限定义方式';
                    
      COMMENT ON COLUMN mlc_app_role.extend_attrs IS '操作标签';
                    
      COMMENT ON COLUMN mlc_app_role.optional_controls IS '操作标签';
                    
      COMMENT ON COLUMN mlc_app_role.general_add IS '允许新增';
                    
      COMMENT ON COLUMN mlc_app_role.general_share IS '允许分享';
                    
      COMMENT ON COLUMN mlc_app_role.general_import IS '允许导入';
                    
      COMMENT ON COLUMN mlc_app_role.general_export IS '允许导出';
                    
      COMMENT ON COLUMN mlc_app_role.general_discussion IS '允许讨论';
                    
      COMMENT ON COLUMN mlc_app_role.general_system_printing IS '允许打印';
                    
      COMMENT ON COLUMN mlc_app_role.general_attachment_download IS '允许下载附件';
                    
      COMMENT ON COLUMN mlc_app_role.general_logging IS '允许查看日志';
                    
      COMMENT ON COLUMN mlc_app_role.sort_index IS '排序';
                    
      COMMENT ON COLUMN mlc_app_role.description IS '描述';
                    
      COMMENT ON COLUMN mlc_app_role.status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_role.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_role.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_role.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_role.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_role.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_worksheet IS '工作表';
                
      COMMENT ON COLUMN mlc_app_worksheet.worksheet_id IS '工作表ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet.app_id IS '应用 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet.app_section_id IS '应用分组 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet.model_object_entity_id IS '模型对象 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet.model_object_entity_name IS '模型对象名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet.name IS '工作表名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet.alias IS '工作表别名';
                    
      COMMENT ON COLUMN mlc_app_worksheet.entity_name IS '记录名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet.icon IS '图标';
                    
      COMMENT ON COLUMN mlc_app_worksheet.icon_color IS '图标颜色';
                    
      COMMENT ON COLUMN mlc_app_worksheet.icon_url IS '图标路径';
                    
      COMMENT ON COLUMN mlc_app_worksheet.count IS '数量';
                    
      COMMENT ON COLUMN mlc_app_worksheet.visible_type IS '分享状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet.open_approval IS '开启审批';
                    
      COMMENT ON COLUMN mlc_app_worksheet.workflow_child_table_switch IS '工作流子表切换';
                    
      COMMENT ON COLUMN mlc_app_worksheet.navigate_hide IS '侧边栏状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet.is_worksheet_query IS '是否配置工作表查询';
                    
      COMMENT ON COLUMN mlc_app_worksheet.is_marked IS '收藏状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet.controls IS '控件';
                    
      COMMENT ON COLUMN mlc_app_worksheet.advanced_setting IS '高级设置';
                    
      COMMENT ON COLUMN mlc_app_worksheet.submit_setting IS '提交设置';
                    
      COMMENT ON COLUMN mlc_app_worksheet.developer_notes IS '开发者备注';
                    
      COMMENT ON COLUMN mlc_app_worksheet.resume IS '摘要';
                    
      COMMENT ON COLUMN mlc_app_worksheet.dec IS '详细说明';
                    
      COMMENT ON COLUMN mlc_app_worksheet.status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_worksheet.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_worksheet.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_worksheet.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_member_role IS '成员角色中间表';
                
      COMMENT ON COLUMN mlc_app_member_role.member_id IS '成员ID';
                    
      COMMENT ON COLUMN mlc_app_member_role.role_id IS '角色ID';
                    
      COMMENT ON COLUMN mlc_app_member_role.is_owner IS '是否拥有者';
                    
      COMMENT ON COLUMN mlc_app_member_role.is_manager IS '是否管理员';
                    
      COMMENT ON COLUMN mlc_app_member_role.is_role_charger IS '是否角色负责人';
                    
      COMMENT ON COLUMN mlc_app_member_role.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_member_role.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_member_role.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_member_role.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_member_role.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_role_resource IS '应用角色资源表';
                
      COMMENT ON COLUMN mlc_app_role_resource.sid IS '主键ID';
                    
      COMMENT ON COLUMN mlc_app_role_resource.role_id IS '角色ID';
                    
      COMMENT ON COLUMN mlc_app_role_resource.resource_id IS '资源ID';
                    
      COMMENT ON COLUMN mlc_app_role_resource.operation IS '权限操作类型';
                    
      COMMENT ON COLUMN mlc_app_role_resource.delta_permissions IS '变更的权限标签';
                    
      COMMENT ON COLUMN mlc_app_role_resource.priority IS '冲突时优先级';
                    
      COMMENT ON COLUMN mlc_app_role_resource.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_role_resource.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_role_resource.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_role_resource.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_role_resource.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_worksheet_view IS '工作表视图';
                
      COMMENT ON COLUMN mlc_app_worksheet_view.view_id IS '视图 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.worksheet_id IS '工作表 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.name IS '工作表名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.un_read IS '是否已读';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.sort_cid IS '排序字段';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.sort_type IS '排序类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.cover_cid IS '封面字段';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.cover_type IS '封面类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.custom_display IS '是否配置自定义显示列';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.display_controls IS '显示字段';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.view_type IS '视图类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.child_type IS '层级类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.view_control IS '视图维度ID(分组ID)';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.row_height IS '行高';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.show_control_name IS '显示控件名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.show_controls IS 'Web显示字段';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.layers_name IS '层级名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.controls IS '视图隐藏字段';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.view_controls IS '多表层级视图控件';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.controls_sorts IS '字段排序';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.nav_group IS '导航分组';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.filters IS '初始过滤';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.fast_filters IS '快速过滤';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.more_sort IS '更多排序';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.advanced_setting IS '高级设置';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.is_marked IS '收藏状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.order_num IS '排序';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_view.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_worksheet_rule IS '工作表业务规则';
                
      COMMENT ON COLUMN mlc_app_worksheet_rule.rule_id IS '业务规则 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.worksheet_id IS '工作表 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.name IS '工作表名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.type IS '规则类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.check_type IS '检查类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.hint_type IS '提示类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.control_ids IS '控件集合';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.filters IS '初始过滤';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.rule_items IS '规则项';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.open_drawer IS '打开抽屉';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.disabled IS '已禁用';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.order_num IS '排序';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_rule.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_worksheet_button IS '工作表自定义按钮';
                
      COMMENT ON COLUMN mlc_app_worksheet_button.btn_id IS '按钮ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.worksheet_id IS '工作表ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.name IS '名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.is_all_view IS '是否全部视图';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.display_views IS '显示视图';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.click_type IS '点击类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.confirm_msg IS '确认消息';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.sure_name IS '确认名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.cancel_name IS '取消名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.write_type IS '填写对象类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.write_object IS '填写内容';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.write_controls IS '填写控件';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.relation_control IS '关联记录ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.add_relation_control_id IS '新建关联记录ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.workflow_type IS '继续执行工作流';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.workflow_id IS '工作流ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.filters IS '过滤器';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.color IS '颜色';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.icon IS '图标';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.icon_url IS '图标URL';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button."desc" IS '描述';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.advanced_setting IS '高级设置';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.enable_confirm IS '启用二次确认';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.verify_pwd IS '校验密码';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.is_batch IS '是否多条数据源';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.show_type IS '启用按钮类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.add_relation_control IS '添加关联控件';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.edit_attrs IS '属性';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.disabled IS '禁用';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.status IS '状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_button.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_worksheet_print_template IS '工作表自定义打印模板';
                
      COMMENT ON COLUMN mlc_app_worksheet_print_template.print_template_id IS '打印模板 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.worksheet_id IS '工作表 ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.name IS '名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.form_name IS '来自名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.company_name IS '公司名称';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.type IS '类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.range IS '使用范围';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.control_styles IS '控件样式';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.relation_style IS '关联样式';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.owner_account IS '拥有者';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.print_time IS '打印时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.qr_code IS '二维码';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.print_account IS '打印人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.logo_checked IS '打印 logo';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.title_checked IS '标题是否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.company_name_checked IS '公司名称否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.form_name_checked IS '表单标题';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.create_time_checked IS '创建时间是否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.create_account_checked IS '创建者是否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.update_time_checked IS '更新时间是否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.update_account_checked IS '更新人是否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.owner_account_checked IS '拥有者是否打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.show_data IS '空值是否隐藏';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.print_option IS '选项字段平铺打印';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.share_type IS '分享类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.font IS '字体';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.allow_download_permission IS '允许下载打印文件';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.advance_settings IS '高级设置';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.approve_position IS '审批签名位置';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.approval_ids IS '审批批准编号';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.views IS '视图';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.order_number IS '排序';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.filters IS '过滤器';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.remark IS '备注';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.disabled IS '禁用';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_print_template.updated_at IS '修改时间';
                    
      COMMENT ON TABLE mlc_app_worksheet_switch IS '工作表开关';
                
      COMMENT ON COLUMN mlc_app_worksheet_switch.switch_id IS '按钮ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.worksheet_id IS '工作表ID';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.type IS '开关类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.role_type IS '角色类型';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.state IS '状态';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch."view" IS '视图';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.view_ids IS '视图';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.version IS '数据版本';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.create_by IS '创建人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.created_at IS '创建时间';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.update_by IS '修改人';
                    
      COMMENT ON COLUMN mlc_app_worksheet_switch.updated_at IS '修改时间';
                    
