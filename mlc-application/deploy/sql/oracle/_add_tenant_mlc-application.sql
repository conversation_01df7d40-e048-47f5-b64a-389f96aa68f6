
    alter table mlc_app_info add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_member add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_member_role add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_resource add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_role add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_role_resource add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_section add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_button add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_print_template add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_rule add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_switch add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_worksheet_view add NOP_TENANT_ID VARCHAR2(32) DEFAULT '0' NOT NULL;

alter table mlc_app_info drop constraint PK_mlc_app_info;
alter table mlc_app_info add constraint PK_mlc_app_info primary key (NOP_TENANT_ID, app_id);

alter table mlc_app_member drop constraint PK_mlc_app_member;
alter table mlc_app_member add constraint PK_mlc_app_member primary key (NOP_TENANT_ID, member_id);

alter table mlc_app_member_role drop constraint PK_mlc_app_member_role;
alter table mlc_app_member_role add constraint PK_mlc_app_member_role primary key (NOP_TENANT_ID, member_id,role_id);

alter table mlc_app_resource drop constraint PK_mlc_app_resource;
alter table mlc_app_resource add constraint PK_mlc_app_resource primary key (NOP_TENANT_ID, resource_id);

alter table mlc_app_role drop constraint PK_mlc_app_role;
alter table mlc_app_role add constraint PK_mlc_app_role primary key (NOP_TENANT_ID, role_id);

alter table mlc_app_role_resource drop constraint PK_mlc_app_role_resource;
alter table mlc_app_role_resource add constraint PK_mlc_app_role_resource primary key (NOP_TENANT_ID, sid);

alter table mlc_app_section drop constraint PK_mlc_app_section;
alter table mlc_app_section add constraint PK_mlc_app_section primary key (NOP_TENANT_ID, app_section_id);

alter table mlc_app_worksheet drop constraint PK_mlc_app_worksheet;
alter table mlc_app_worksheet add constraint PK_mlc_app_worksheet primary key (NOP_TENANT_ID, worksheet_id);

alter table mlc_app_worksheet_button drop constraint PK_mlc_app_worksheet_button;
alter table mlc_app_worksheet_button add constraint PK_mlc_app_worksheet_button primary key (NOP_TENANT_ID, btn_id);

alter table mlc_app_worksheet_print_template drop constraint PK_mlc_app_worksheet_print_template;
alter table mlc_app_worksheet_print_template add constraint PK_mlc_app_worksheet_print_template primary key (NOP_TENANT_ID, print_template_id);

alter table mlc_app_worksheet_rule drop constraint PK_mlc_app_worksheet_rule;
alter table mlc_app_worksheet_rule add constraint PK_mlc_app_worksheet_rule primary key (NOP_TENANT_ID, rule_id);

alter table mlc_app_worksheet_switch drop constraint PK_mlc_app_worksheet_switch;
alter table mlc_app_worksheet_switch add constraint PK_mlc_app_worksheet_switch primary key (NOP_TENANT_ID, switch_id);

alter table mlc_app_worksheet_view drop constraint PK_mlc_app_worksheet_view;
alter table mlc_app_worksheet_view add constraint PK_mlc_app_worksheet_view primary key (NOP_TENANT_ID, view_id);

alter table mlc_app_worksheet drop constraint UK_MLC_APP_WORKSHEET_ALIAS;
alter table mlc_app_worksheet add constraint UK_MLC_APP_WORKSHEET_ALIAS
                     unique (NOP_TENANT_ID,alias);

                alter table mlc_app_worksheet_print_template drop constraint UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME;
alter table mlc_app_worksheet_print_template add constraint UK_MLC_APP_WORKSHEET_PRINT_TEMPLATE_NAME
                     unique (NOP_TENANT_ID,name);

                
